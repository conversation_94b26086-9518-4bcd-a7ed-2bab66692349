{"swagger": "2.0", "info": {"title": "crmeb_java_front", "version": "last", "description": "crmeb_java_front"}, "basePath": "/", "tags": [{"name": "上传文件", "description": "Upload Front Controller"}, {"name": "二维码服务", "description": "Qr Code Controller"}, {"name": "优惠券", "description": "Coupon Controller"}, {"name": "商品", "description": "Product Controller"}, {"name": "商品 -- 购物车", "description": "Cart Controller"}, {"name": "城市服务", "description": "City Controller"}, {"name": "客服", "description": "Store Service Controller"}, {"name": "微信 -- 开放平台", "description": "We Chat Controller"}, {"name": "提货点", "description": "Store Controller"}, {"name": "文章", "description": "Article Controller"}, {"name": "物流公司", "description": "Express Controller"}, {"name": "用户 -- 充值", "description": "User Recharge Controller"}, {"name": "用户 -- 地址", "description": "User Address Controller"}, {"name": "用户 -- 点赞/收藏", "description": "User Collect Controller"}, {"name": "用户 -- 用户中心", "description": "User Controller"}, {"name": "用户 -- 登录注册", "description": "Login Controller"}, {"name": "用户 -- 签到", "description": "User Sign Controller"}, {"name": "营销 -- 优惠券", "description": "User Coupon Controller"}, {"name": "订单", "description": "Store Order Controller"}, {"name": "首页", "description": "Index Controller"}], "schemes": ["http"], "paths": {"/api/front/user/upload/image": {"post": {"tags": ["上传文件"], "summary": "图片上传", "consumes": ["multipart/form-data"], "parameters": [{"name": "model", "in": "query", "required": false, "description": "模块 用户user,商品product,微信wechat,news文章", "type": "string"}, {"name": "pid", "in": "query", "required": false, "description": "分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列 ", "type": "string"}, {"name": "multipart", "in": "formData", "required": false, "description": "multipart", "type": "file"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"extName": {"type": "string"}, "fileName": {"type": "string"}, "fileSize": {"type": "integer", "format": "int64"}, "serverPath": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}, "title": "FileResultVo", "$$ref": "#/definitions/FileResultVo"}, "message": {"type": "string"}}, "title": "CommonResult«FileResultVo»", "$$ref": "#/definitions/CommonResult«FileResultVo»"}}}}}, "/api/front/qrcode/get": {"post": {"tags": ["二维码服务"], "summary": "获取二维码", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "additionalProperties": {"type": "object"}}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«Map«string,object»»", "$$ref": "#/definitions/CommonResult«Map«string,object»»"}}}}}, "/api/front/qrcode/base64": {"post": {"tags": ["二维码服务"], "summary": "远程图片转base64", "consumes": ["application/json"], "parameters": [{"name": "url", "in": "query", "required": false, "description": "url", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«Map«string,object»»", "$$ref": "#/definitions/CommonResult«Map«string,object»»"}}}}}, "/api/front/coupons": {"get": {"tags": ["优惠券"], "summary": "分页列表", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "productId", "in": "query", "required": false, "description": "productId", "type": "string"}, {"name": "type", "in": "query", "required": false, "description": "type", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "day": {"type": "integer", "format": "int32", "description": "天数"}, "id": {"type": "integer", "format": "int32", "description": "优惠券表ID"}, "isDel": {"type": "boolean", "description": "是否删除 状态（0：否，1：是）"}, "isFixedTime": {"type": "boolean", "description": "是否固定使用时间, 默认0 否， 1是"}, "isLimited": {"type": "boolean", "description": "是否限量, 默认0 不限量， 1限量"}, "isUse": {"type": "boolean", "description": "是否已领取未使用"}, "lastTotal": {"type": "integer", "format": "int32", "description": "剩余数量"}, "minPrice": {"type": "number", "description": "最低消费，0代表不限制"}, "money": {"type": "number", "description": "兑换的优惠券面值"}, "name": {"type": "string", "description": "优惠券名称"}, "primaryKey": {"type": "string", "description": "所属商品id / 分类id"}, "receiveEndTime": {"type": "string", "format": "date-time", "description": "可领取结束时间"}, "receiveStartTime": {"type": "string", "format": "date-time", "description": "可领取开始时间"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态（0：关闭，1：开启）"}, "total": {"type": "integer", "format": "int32", "description": "发放总数"}, "type": {"type": "integer", "format": "int32", "description": "优惠券类型 0-通用 1 普通券, 2 新人券, 3 购买商品赠送券, 4 付费会员券"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "useEndTime": {"type": "string", "format": "date-time", "description": "可使用时间范围 结束时间"}, "useStartTime": {"type": "string", "format": "date-time", "description": "可使用时间范围 开始时间"}}, "title": "StoreCoupon对象", "description": "优惠券表", "$$ref": "#/definitions/StoreCoupon对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«StoreCoupon对象»»", "$$ref": "#/definitions/CommonResult«List«StoreCoupon对象»»"}}}}}, "/api/front/coupons/order": {"get": {"tags": ["优惠券"], "summary": "当前购物车可用优惠券", "consumes": ["text/plain"], "parameters": [{"name": "cartId", "in": "query", "required": false, "description": "cartId", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON><PERSON>"], "properties": {"cid": {"type": "integer", "format": "int32", "description": "兑换的项目id"}, "couponId": {"type": "integer", "format": "int32", "description": "优惠券发布id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "id": {"type": "integer", "format": "int32", "description": "id"}, "minPrice": {"type": "number", "description": "最低消费多少金额可用优惠券"}, "money": {"type": "number", "description": "优惠券的面值"}, "name": {"type": "string", "description": "优惠券名称"}, "primaryKey": {"type": "string", "description": "主键id 商品id/分类id"}, "receiveEndTime": {"type": "string", "format": "date-time", "description": "过期时间"}, "receiveStartTime": {"type": "string", "format": "date-time", "description": "开始使用时间"}, "status": {"type": "integer", "format": "int32", "description": "状态（0：未使用，1：已使用, 2:已失效）"}, "type": {"type": "string", "description": "获取方式"}, "uid": {"type": "integer", "format": "int32", "description": "领取人id"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "useTime": {"type": "string", "format": "date-time", "description": "使用时间"}, "useType": {"type": "integer", "format": "int32", "description": "使用类型 1 全场通用, 2 商品券, 3 品类券"}}, "title": "StoreCouponUserOrder对象", "description": "下单之前可以使用的优惠券对象", "$$ref": "#/definitions/StoreCouponUserOrder对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«StoreCouponUserOrder对象»»", "$$ref": "#/definitions/CommonResult«List«StoreCouponUserOrder对象»»"}}}}}, "/api/front/product/hot": {"get": {"tags": ["商品"], "summary": "为你推荐", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«ProductResponse对象»", "$$ref": "#/definitions/CommonPage«ProductResponse对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«ProductResponse对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«ProductResponse对象»»"}}}}}, "/api/front/products": {"get": {"tags": ["商品"], "summary": "商品列表", "consumes": ["text/plain"], "parameters": [{"name": "cid", "in": "query", "required": false, "description": "分类id", "type": "string"}, {"name": "keyword", "in": "query", "required": false, "description": "搜索关键字", "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "news", "in": "query", "required": false, "description": "是否新品", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "priceOrder", "in": "query", "required": false, "description": "价格排序", "type": "string"}, {"name": "salesOrder", "in": "query", "required": false, "description": "销量排序", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«ProductResponse对象»", "$$ref": "#/definitions/CommonPage«ProductResponse对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«ProductResponse对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«ProductResponse对象»»"}}}}}, "/api/front/reply/list/{id}": {"get": {"tags": ["商品"], "summary": "商品评论列表", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "type", "in": "query", "required": false, "description": "评价等级|0=全部,1=好评,2=中评,3=差评", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"avatar": {"type": "string", "description": "用户头像"}, "comment": {"type": "string", "description": "评论内容"}, "createTime": {"type": "string", "format": "date-time", "description": "评论时间"}, "id": {"type": "integer", "format": "int32", "description": "评论ID"}, "isDel": {"type": "boolean", "description": "0未删除1已删除"}, "isReply": {"type": "boolean", "description": "0未回复1已回复"}, "merchantReplyContent": {"type": "string", "description": "管理员回复内容"}, "merchantReplyTime": {"type": "integer", "format": "int32", "description": "管理员回复时间"}, "nickname": {"type": "string", "description": "用户名称"}, "oid": {"type": "integer", "format": "int32", "description": "订单ID"}, "pics": {"type": "array", "description": "评论图片", "items": {"type": "string"}}, "productId": {"type": "integer", "format": "int32", "description": "商品id"}, "productScore": {"type": "integer", "format": "int32", "description": "商品分数"}, "replyType": {"type": "string", "description": "某种商品类型(普通商品、秒杀商品）"}, "serviceScore": {"type": "integer", "format": "int32", "description": "服务分数"}, "storeProduct": {"type": "object", "properties": {"activity": {"type": "string", "description": "活动显示排序1=秒杀，2=砍价，3=拼团"}, "addTime": {"type": "integer", "format": "int32", "description": "添加时间"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "browse": {"type": "integer", "format": "int32", "description": "浏览量"}, "cateId": {"type": "string", "description": "分类id"}, "codePath": {"type": "string", "description": "商品二维码地址(用户小程序海报)"}, "cost": {"type": "number", "description": "成本价"}, "ficti": {"type": "integer", "format": "int32", "description": "虚拟销量"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isBargain": {"type": "boolean", "description": "砍价状态 0未开启 1开启"}, "isBenefit": {"type": "boolean", "description": "是否优惠"}, "isBest": {"type": "boolean", "description": "是否精品"}, "isDel": {"type": "boolean", "description": "是否删除"}, "isGood": {"type": "boolean", "description": "是否优品推荐"}, "isHot": {"type": "boolean", "description": "是否热卖"}, "isNew": {"type": "boolean", "description": "是否新品"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSeckill": {"type": "boolean", "description": "秒杀状态 0 未开启 1已开启"}, "isShow": {"type": "boolean", "description": "状态（0：未上架，1：上架）"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "merUse": {"type": "boolean", "description": "商户是否代理 0不可代理1可代理"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "soureLink": {"type": "string", "description": "淘宝京东1688类型"}, "specType": {"type": "boolean", "description": "规格 0单 1多"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "videoLink": {"type": "string", "description": "主图视频链接"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProduct对象", "description": "商品表", "$$ref": "#/definitions/StoreProduct对象"}, "uid": {"type": "integer", "format": "int32", "description": "用户ID"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}, "title": "StoreProductReplyResponse", "$$ref": "#/definitions/StoreProductReplyResponse"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«StoreProductReplyResponse»", "$$ref": "#/definitions/CommonPage«StoreProductReplyResponse»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«StoreProductReplyResponse»»", "$$ref": "#/definitions/CommonResult«CommonPage«StoreProductReplyResponse»»"}}}}}, "/api/front/reply/config/{id}": {"get": {"tags": ["商品"], "summary": "商品评论数量", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"goodCount": {"type": "integer", "format": "int64", "description": "好评总数"}, "inCount": {"type": "integer", "format": "int64", "description": "中评总数"}, "poorCount": {"type": "integer", "format": "int64", "description": "差评总数"}, "replyChance": {"type": "string", "description": "好评率"}, "replyStar": {"type": "integer", "format": "int32", "description": "评分星数"}, "sumCount": {"type": "integer", "format": "int64", "description": "评论总数"}}, "title": "StoreProductReplayCountResponse对象", "description": "产品评价数量和好评度", "$$ref": "#/definitions/StoreProductReplayCountResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«StoreProductReplayCountResponse对象»", "$$ref": "#/definitions/CommonResult«StoreProductReplayCountResponse对象»"}}}}}, "/api/front/product/detail/{id}": {"get": {"tags": ["商品"], "summary": "商品详情", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"activity": {"type": "array", "description": "拼团，砍价，秒杀商品集合", "items": {"type": "object"}}, "base64Image": {"type": "string", "description": "主图base64"}, "goodList": {"type": "array", "description": "优品推荐列表", "items": {"type": "object", "properties": {"activity": {"type": "array", "description": "秒杀，团购，砍价", "items": {"type": "object"}}, "checkCoupon": {"type": "boolean", "description": "可用优惠券"}, "id": {"type": "integer", "format": "int32", "description": "id"}, "image": {"type": "string", "description": "img Url"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价"}, "storeName": {"type": "string", "description": "商品名称"}}, "title": "StoreProductRecommendResponse", "$$ref": "#/definitions/StoreProductRecommendResponse"}}, "priceName": {"type": "string", "description": "返佣金额区间"}, "productAttr": {"type": "array", "description": "产品属性", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "productValue": {"type": "object", "description": "商品属性详情", "additionalProperties": {"type": "object"}}, "reply": {"type": "object", "description": "最新评价"}, "replyChance": {"type": "integer", "format": "int32", "description": "好评率"}, "replyCount": {"type": "integer", "format": "int32", "description": "评价数量"}, "storeInfo": {"type": "object", "properties": {"activity": {"type": "string", "description": "活动显示排序1=秒杀，2=砍价，3=拼团"}, "addTime": {"type": "integer", "format": "int32", "description": "添加时间"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "browse": {"type": "integer", "format": "int32", "description": "浏览量"}, "cateId": {"type": "string", "description": "分类id"}, "cateIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "cateValues": {"type": "string", "description": "分类中文"}, "codePath": {"type": "string", "description": "商品二维码地址(用户小程序海报)"}, "content": {"type": "string", "description": "商品描述"}, "cost": {"type": "number", "description": "成本价"}, "ficti": {"type": "integer", "format": "int32", "description": "虚拟销量"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isBargain": {"type": "boolean", "description": "砍价状态 0未开启 1开启"}, "isBenefit": {"type": "boolean", "description": "是否优惠"}, "isBest": {"type": "boolean", "description": "是否精品"}, "isDel": {"type": "boolean", "description": "是否删除"}, "isGood": {"type": "boolean", "description": "是否优品推荐"}, "isHot": {"type": "boolean", "description": "是否热卖"}, "isNew": {"type": "boolean", "description": "是否新品"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSeckill": {"type": "boolean", "description": "秒杀状态 0 未开启 1已开启"}, "isShow": {"type": "boolean", "description": "状态（0：未上架，1：上架）"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "merUse": {"type": "boolean", "description": "商户是否代理 0不可代理1可代理"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "soureLink": {"type": "string", "description": "淘宝京东1688类型"}, "specType": {"type": "boolean", "description": "规格 0单 1多"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "userCollect": {"type": "boolean", "description": "收藏标识"}, "userLike": {"type": "boolean", "description": "点赞标识"}, "videoLink": {"type": "string", "description": "主图视频链接"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProductStoreInfoResponse对象", "description": "商品表", "$$ref": "#/definitions/StoreProductStoreInfoResponse对象"}}, "title": "ProductDetailResponse对象", "description": "商品详情H5", "$$ref": "#/definitions/ProductDetailResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«ProductDetailResponse对象»", "$$ref": "#/definitions/CommonResult«ProductDetailResponse对象»"}}}}}, "/api/front/category": {"get": {"tags": ["商品"], "summary": "获取分类", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"child": {"type": "array", "items": {"originalRef": "CategoryTreeVo", "$ref": "#/definitions/CategoryTreeVo"}}, "extra": {"type": "string", "description": "扩展字段"}, "id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "分类名称"}, "path": {"type": "string", "description": "路径"}, "pid": {"type": "integer", "format": "int32", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态, 0正常，1失效"}, "type": {"type": "integer", "format": "int32", "description": "类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置"}, "url": {"type": "string", "description": "地址"}}, "title": "CategoryTreeVo", "$$ref": "#/definitions/CategoryTreeVo"}}, "message": {"type": "string"}}, "title": "CommonResult«List«CategoryTreeVo»»", "$$ref": "#/definitions/CommonResult«List«CategoryTreeVo»»"}}}}}, "/api/front/cart/num": {"post": {"tags": ["商品 -- 购物车"], "summary": "修改", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "query", "required": false, "description": "id", "type": "string"}, {"name": "number", "in": "query", "required": false, "description": "number", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/cart/list": {"get": {"tags": ["商品 -- 购物车"], "summary": "分页列表", "consumes": ["text/plain"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": false, "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"addTime": {"type": "string"}, "attrStatus": {"type": "boolean", "description": "商品是否有效"}, "bargainId": {"type": "integer", "format": "int32", "description": "砍价id"}, "brokerage": {"type": "number", "description": "一级分佣"}, "brokerageTwo": {"type": "number", "description": "二级分佣"}, "cartNum": {"type": "integer", "format": "int32", "description": "商品数量"}, "combinationId": {"type": "integer", "format": "int32", "description": "拼团id"}, "costPrice": {"type": "number"}, "id": {"type": "integer", "format": "int64", "description": "购物车表ID"}, "isNew": {"type": "boolean", "description": "是否为立即购买"}, "isReply": {"type": "integer", "format": "int32"}, "productAttrUnique": {"type": "string", "description": "商品属性"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "productInfo": {"type": "object", "properties": {"attrInfo": {"type": "object", "properties": {"attrValue": {"type": "string", "description": "产品属性值和属性名对应关系"}, "barCode": {"type": "string", "description": "商品条码"}, "brokerage": {"type": "number", "description": "一级返佣"}, "brokerageTwo": {"type": "number", "description": "二级返佣"}, "cost": {"type": "number", "description": "成本价"}, "id": {"type": "integer", "format": "int32", "description": "attrId"}, "image": {"type": "string", "description": "图片"}, "otPrice": {"type": "number", "description": "原价"}, "price": {"type": "number", "description": "属性金额"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "quota": {"type": "integer", "format": "int32", "description": "活动限购数量"}, "quotaShow": {"type": "integer", "format": "int32", "description": "活动限购数量显示"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "stock": {"type": "integer", "format": "int32", "description": "属性对应的库存"}, "suk": {"type": "string", "description": "商品属性索引值 (attr_value|attr_value[|....])"}, "type": {"type": "integer", "format": "int32", "description": "活动类型 0=商品，1=秒杀，2=砍价，3=拼团"}, "unique": {"type": "string", "description": "唯一值"}, "volume": {"type": "number", "description": "体积"}, "weight": {"type": "number", "description": "重量"}}, "title": "StoreProductAttrValue对象", "description": "商品属性值表", "$$ref": "#/definitions/StoreProductAttrValue对象"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "cateId": {"type": "string", "description": "分类id"}, "cost": {"type": "number", "description": "成本价"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProductCartProductInfoResponse对象", "description": "商品信息，购物车列表使用", "$$ref": "#/definitions/StoreProductCartProductInfoResponse对象"}, "seckillId": {"type": "integer", "format": "int32", "description": "秒杀商品ID"}, "truePrice": {"type": "number"}, "trueStock": {"type": "integer", "format": "int32"}, "type": {"type": "string", "description": "类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户ID"}, "vipTruePrice": {"type": "number"}}, "title": "StoreCartResponse", "description": "购物车ListResponse", "$$ref": "#/definitions/StoreCartResponse"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«StoreCartResponse»", "$$ref": "#/definitions/CommonPage«StoreCartResponse»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«StoreCartResponse»»", "$$ref": "#/definitions/CommonResult«CommonPage«StoreCartResponse»»"}}}}}, "/api/front/cart/delete": {"post": {"tags": ["商品 -- 购物车"], "summary": "删除", "consumes": ["application/json"], "parameters": [{"name": "ids", "in": "query", "required": false, "description": "ids", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/cart/count": {"get": {"tags": ["商品 -- 购物车"], "summary": "数量", "consumes": ["text/plain"], "parameters": [{"name": "numType", "in": "query", "required": false, "description": "numType", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«Map«object,object»»", "$$ref": "#/definitions/CommonResult«Map«object,object»»"}}}}}, "/api/front/cart/save": {"post": {"tags": ["商品 -- 购物车"], "summary": "新增", "consumes": ["application/json"], "parameters": [{"name": "cartNum", "in": "query", "required": false, "description": "商品数量", "type": "string"}, {"name": "isNew", "in": "query", "required": false, "description": "是否为立即购买", "type": "string"}, {"name": "productAttrUnique", "in": "query", "required": false, "description": "商品属性 -- attr 对象的id", "type": "string"}, {"name": "productId", "in": "query", "required": false, "description": "商品ID", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}}, "title": "CommonResult«HashMap«string,string»»", "$$ref": "#/definitions/CommonResult«HashMap«string,string»»"}}}}}, "/api/front/cart/resetcart": {"post": {"tags": ["商品 -- 购物车"], "summary": "购物车重选提交", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "购物车id"}, "num": {"type": "integer", "format": "int32", "description": "购物车数量"}, "productId": {"type": "integer", "format": "int32", "description": "商品id"}, "unique": {"type": "integer", "format": "int32", "description": "AttrValue Id"}}, "title": "CartResetRequest", "$$ref": "#/definitions/CartResetRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/city/list": {"get": {"tags": ["城市服务"], "summary": "树形结构", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/user/service/lst": {"get": {"tags": ["客服"], "summary": "客服列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"avatar": {"type": "string", "description": "客服头像"}, "createTime": {"type": "string", "format": "date-time", "description": "添加时间"}, "customer": {"type": "boolean", "description": "是否展示统计管理"}, "id": {"type": "integer", "format": "int32", "description": "客服id"}, "nickname": {"type": "string", "description": "代理名称"}, "notify": {"type": "integer", "format": "int32", "description": "订单通知1开启0关闭"}, "status": {"type": "boolean", "description": "0隐藏1显示"}, "uid": {"type": "integer", "format": "int32", "description": "客服uid"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}, "title": "StoreService对象", "description": "客服表", "$$ref": "#/definitions/StoreService对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«StoreService对象»»", "$$ref": "#/definitions/CommonResult«List«StoreService对象»»"}}}}}, "/api/front/user/service/record/{toUid}": {"get": {"tags": ["客服"], "summary": "聊天记录", "consumes": ["text/plain"], "parameters": [{"name": "toUid", "in": "path", "description": "聊天人编号", "required": true, "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "添加时间"}, "id": {"type": "integer", "format": "int32", "description": "客服用户对话记录表ID"}, "msn": {"type": "string", "description": "消息内容"}, "msnType": {"type": "boolean", "description": "消息类型 1=文字 2=表情 3=图片 4=语音"}, "remind": {"type": "boolean", "description": "是否提醒过"}, "toUid": {"type": "integer", "format": "int32", "description": "接收人uid"}, "type": {"type": "boolean", "description": "是否已读（0：否；1：是；）"}, "uid": {"type": "integer", "format": "int32", "description": "发送人uid"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}, "title": "StoreServiceLog对象", "description": "客服用户对话记录表", "$$ref": "#/definitions/StoreServiceLog对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«StoreServiceLog对象»", "$$ref": "#/definitions/CommonPage«StoreServiceLog对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«StoreServiceLog对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«StoreServiceLog对象»»"}}}}}, "/api/front/wechat/getLogo": {"get": {"tags": ["微信 -- 开放平台"], "summary": "小程序获取授权logo", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}}, "title": "CommonResult«Map«string,string»»", "$$ref": "#/definitions/CommonResult«Map«string,string»»"}}}}}, "/api/front/wechat/authorize/login": {"get": {"tags": ["微信 -- 开放平台"], "summary": "微信登录公共号授权登录", "consumes": ["text/plain"], "parameters": [{"name": "code", "in": "query", "required": false, "description": "code", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"expiresTime": {"type": "string", "format": "date-time", "description": "用户登录密钥到期时间"}, "token": {"type": "string", "description": "用户登录密钥"}, "user": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "title": "LoginResponse", "description": "用户登录返回数据", "$$ref": "#/definitions/LoginResponse"}, "message": {"type": "string"}}, "title": "CommonResult«LoginResponse»", "$$ref": "#/definitions/CommonResult«LoginResponse»"}}}}}, "/api/front/wechat/authorize/program/login": {"post": {"tags": ["微信 -- 开放平台"], "summary": "微信登录小程序授权登录", "consumes": ["application/json"], "parameters": [{"name": "code", "in": "query", "required": false, "description": "code", "type": "string"}, {"name": "root", "in": "body", "description": "三方用户注册对象", "schema": {"type": "object", "required": ["avatar", "nick<PERSON><PERSON>", "sex", "spread_spid"], "properties": {"avatar": {"type": "string", "description": "用户头像"}, "city": {"type": "string", "description": "普通用户个人资料填写的城市"}, "country": {"type": "string", "description": "国家，如中国为CN"}, "nickName": {"type": "string", "description": "用户昵称"}, "province": {"type": "string", "description": "用户个人资料填写的省份"}, "sex": {"type": "string", "description": "性别"}, "spread_spid": {"type": "integer", "format": "int32", "description": "推广人id"}}, "title": "RegisterThirdUserRequest对象", "description": "三方用户注册对象", "$$ref": "#/definitions/RegisterThirdUserRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"expiresTime": {"type": "string", "format": "date-time", "description": "用户登录密钥到期时间"}, "token": {"type": "string", "description": "用户登录密钥"}, "user": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "title": "LoginResponse", "description": "用户登录返回数据", "$$ref": "#/definitions/LoginResponse"}, "message": {"type": "string"}}, "title": "CommonResult«LoginResponse»", "$$ref": "#/definitions/CommonResult«LoginResponse»"}}}}}, "/api/front/wechat/config": {"get": {"tags": ["微信 -- 开放平台"], "summary": "获取微信公众号js配置", "consumes": ["text/plain"], "parameters": [{"name": "url", "in": "query", "required": false, "description": "页面地址url", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/wechat/authorize/get": {"get": {"tags": ["微信 -- 开放平台"], "summary": "获取授权页面跳转地址", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/wechat/info/{id}": {"get": {"tags": ["微信 -- 开放平台"], "summary": "详情", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"content": {"type": "string", "description": "回复内容"}, "createTime": {"type": "string", "format": "date-time", "description": "添加时间"}, "id": {"type": "integer", "format": "int32", "description": "模板id"}, "name": {"type": "string", "description": "模板名"}, "status": {"type": "integer", "format": "int32", "description": "状态"}, "tempId": {"type": "string", "description": "模板ID"}, "tempKey": {"type": "string", "description": "模板编号"}, "type": {"type": "boolean", "description": "0=订阅消息,1=微信模板消息"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}, "title": "TemplateMessage对象", "description": "微信模板", "$$ref": "#/definitions/TemplateMessage对象"}, "message": {"type": "string"}}, "title": "CommonResult«TemplateMessage对象»", "$$ref": "#/definitions/CommonResult«TemplateMessage对象»"}}}}}, "/api/front/store/list": {"post": {"tags": ["提货点"], "summary": "附近的提货点", "consumes": ["application/json"], "parameters": [{"name": "latitude", "in": "query", "required": false, "description": "经度", "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "longitude", "in": "query", "required": false, "description": "纬度", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "required": ["list"], "properties": {"list": {"type": "array", "description": "附近的门店列表", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "省市区"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "dayTime": {"type": "string", "description": "每日营业开关时间"}, "detailedAddress": {"type": "string", "description": "详细地址"}, "distance": {"type": "string", "description": "距离，单位米"}, "id": {"type": "integer", "format": "int32"}, "image": {"type": "string", "description": "门店logo"}, "introduction": {"type": "string", "description": "简介"}, "isDel": {"type": "boolean", "description": "是否删除"}, "isShow": {"type": "boolean", "description": "是否显示"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "name": {"type": "string", "description": "门店名称"}, "phone": {"type": "string", "description": "手机号码"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "validTime": {"type": "string", "description": "核销有效日期"}}, "title": "SystemStoreNearVo对象", "description": "门店自提", "$$ref": "#/definitions/SystemStoreNearVo对象"}}, "tengXunMapKey": {"type": "string", "description": "腾讯地图key"}}, "title": "StoreNearRequest对象", "description": "附近的门店", "$$ref": "#/definitions/StoreNearRequest对象"}, "message": {"type": "string"}}, "title": "CommonResult«StoreNearRequest对象»", "$$ref": "#/definitions/CommonResult«StoreNearRequest对象»"}}}}}, "/api/front/article/category/list": {"get": {"tags": ["文章"], "summary": "分类列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"child": {"type": "array", "items": {"originalRef": "CategoryTreeVo", "$ref": "#/definitions/CategoryTreeVo"}}, "extra": {"type": "string", "description": "扩展字段"}, "id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "分类名称"}, "path": {"type": "string", "description": "路径"}, "pid": {"type": "integer", "format": "int32", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态, 0正常，1失效"}, "type": {"type": "integer", "format": "int32", "description": "类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置"}, "url": {"type": "string", "description": "地址"}}, "title": "CategoryTreeVo", "$$ref": "#/definitions/CategoryTreeVo"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«CategoryTreeVo»", "$$ref": "#/definitions/CommonPage«CategoryTreeVo»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«CategoryTreeVo»»", "$$ref": "#/definitions/CommonResult«CommonPage«CategoryTreeVo»»"}}}}}, "/api/front/article/list/{cid}": {"get": {"tags": ["文章"], "summary": "分页列表", "consumes": ["text/plain"], "parameters": [{"name": "cid", "in": "path", "description": "cid", "required": true, "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"adminId": {"type": "integer", "format": "int32", "description": "管理员id"}, "author": {"type": "string", "description": "文章作者"}, "categoryName": {"type": "string", "description": "分类"}, "cid": {"type": "string", "description": "分类id"}, "content": {"type": "string", "description": "文章内容"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "hide": {"type": "boolean", "description": "是否隐藏"}, "id": {"type": "integer", "format": "int32", "description": "文章管理ID"}, "imageInput": {"type": "array", "description": "文章图片 前端用", "items": {"type": "string"}}, "imageInputs": {"type": "string", "description": "文章图片 后端用"}, "isBanner": {"type": "boolean", "description": "是否轮播图(小程序)"}, "isHot": {"type": "boolean", "description": "是否热门(小程序)"}, "mediaId": {"type": "string", "description": "微信素材媒体id"}, "merId": {"type": "integer", "format": "int32", "description": "商户id"}, "productId": {"type": "integer", "format": "int32", "description": "商品关联id"}, "shareSynopsis": {"type": "string", "description": "文章分享简介"}, "shareTitle": {"type": "string", "description": "文章分享标题"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态"}, "synopsis": {"type": "string", "description": "文章简介"}, "title": {"type": "string", "description": "文章标题"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "url": {"type": "string", "description": "原文链接"}, "visit": {"type": "string", "description": "浏览次数"}}, "title": "ArticleVo对象", "description": "文章管理表", "$$ref": "#/definitions/ArticleVo对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«ArticleVo对象»", "$$ref": "#/definitions/CommonPage«ArticleVo对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«ArticleVo对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«ArticleVo对象»»"}}}}}, "/api/front/article/hot/list": {"get": {"tags": ["文章"], "summary": "热门列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"adminId": {"type": "integer", "format": "int32", "description": "管理员id"}, "author": {"type": "string", "description": "文章作者"}, "categoryName": {"type": "string", "description": "分类"}, "cid": {"type": "string", "description": "分类id"}, "content": {"type": "string", "description": "文章内容"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "hide": {"type": "boolean", "description": "是否隐藏"}, "id": {"type": "integer", "format": "int32", "description": "文章管理ID"}, "imageInput": {"type": "array", "description": "文章图片 前端用", "items": {"type": "string"}}, "imageInputs": {"type": "string", "description": "文章图片 后端用"}, "isBanner": {"type": "boolean", "description": "是否轮播图(小程序)"}, "isHot": {"type": "boolean", "description": "是否热门(小程序)"}, "mediaId": {"type": "string", "description": "微信素材媒体id"}, "merId": {"type": "integer", "format": "int32", "description": "商户id"}, "productId": {"type": "integer", "format": "int32", "description": "商品关联id"}, "shareSynopsis": {"type": "string", "description": "文章分享简介"}, "shareTitle": {"type": "string", "description": "文章分享标题"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态"}, "synopsis": {"type": "string", "description": "文章简介"}, "title": {"type": "string", "description": "文章标题"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "url": {"type": "string", "description": "原文链接"}, "visit": {"type": "string", "description": "浏览次数"}}, "title": "ArticleVo对象", "description": "文章管理表", "$$ref": "#/definitions/ArticleVo对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«ArticleVo对象»", "$$ref": "#/definitions/CommonPage«ArticleVo对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«ArticleVo对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«ArticleVo对象»»"}}}}}, "/api/front/article/info": {"get": {"tags": ["文章"], "summary": "详情", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "query", "required": false, "description": "文章ID", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"adminId": {"type": "integer", "format": "int32", "description": "管理员id"}, "author": {"type": "string", "description": "文章作者"}, "categoryName": {"type": "string", "description": "分类"}, "cid": {"type": "string", "description": "分类id"}, "content": {"type": "string", "description": "文章内容"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "hide": {"type": "boolean", "description": "是否隐藏"}, "id": {"type": "integer", "format": "int32", "description": "文章管理ID"}, "imageInput": {"type": "array", "description": "文章图片 前端用", "items": {"type": "string"}}, "imageInputs": {"type": "string", "description": "文章图片 后端用"}, "isBanner": {"type": "boolean", "description": "是否轮播图(小程序)"}, "isHot": {"type": "boolean", "description": "是否热门(小程序)"}, "mediaId": {"type": "string", "description": "微信素材媒体id"}, "merId": {"type": "integer", "format": "int32", "description": "商户id"}, "productId": {"type": "integer", "format": "int32", "description": "商品关联id"}, "shareSynopsis": {"type": "string", "description": "文章分享简介"}, "shareTitle": {"type": "string", "description": "文章分享标题"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态"}, "synopsis": {"type": "string", "description": "文章简介"}, "title": {"type": "string", "description": "文章标题"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "url": {"type": "string", "description": "原文链接"}, "visit": {"type": "string", "description": "浏览次数"}}, "title": "ArticleVo对象", "description": "文章管理表", "$$ref": "#/definitions/ArticleVo对象"}, "message": {"type": "string"}}, "title": "CommonResult«ArticleVo对象»", "$$ref": "#/definitions/CommonResult«ArticleVo对象»"}}}}}, "/api/front/article/banner/list": {"get": {"tags": ["文章"], "summary": "轮播列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"adminId": {"type": "integer", "format": "int32", "description": "管理员id"}, "author": {"type": "string", "description": "文章作者"}, "categoryName": {"type": "string", "description": "分类"}, "cid": {"type": "string", "description": "分类id"}, "content": {"type": "string", "description": "文章内容"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "hide": {"type": "boolean", "description": "是否隐藏"}, "id": {"type": "integer", "format": "int32", "description": "文章管理ID"}, "imageInput": {"type": "array", "description": "文章图片 前端用", "items": {"type": "string"}}, "imageInputs": {"type": "string", "description": "文章图片 后端用"}, "isBanner": {"type": "boolean", "description": "是否轮播图(小程序)"}, "isHot": {"type": "boolean", "description": "是否热门(小程序)"}, "mediaId": {"type": "string", "description": "微信素材媒体id"}, "merId": {"type": "integer", "format": "int32", "description": "商户id"}, "productId": {"type": "integer", "format": "int32", "description": "商品关联id"}, "shareSynopsis": {"type": "string", "description": "文章分享简介"}, "shareTitle": {"type": "string", "description": "文章分享标题"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态"}, "synopsis": {"type": "string", "description": "文章简介"}, "title": {"type": "string", "description": "文章标题"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "url": {"type": "string", "description": "原文链接"}, "visit": {"type": "string", "description": "浏览次数"}}, "title": "ArticleVo对象", "description": "文章管理表", "$$ref": "#/definitions/ArticleVo对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«ArticleVo对象»", "$$ref": "#/definitions/CommonPage«ArticleVo对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«ArticleVo对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«ArticleVo对象»»"}}}}}, "/api/front/logistics": {"get": {"tags": ["物流公司"], "summary": "列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "快递公司简称"}, "id": {"type": "integer", "format": "int32", "description": "快递公司id"}, "isShow": {"type": "boolean", "description": "是否显示"}, "name": {"type": "string", "description": "快递公司全称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}}, "title": "Express对象", "description": "快递公司表", "$$ref": "#/definitions/Express对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«Express对象»", "$$ref": "#/definitions/CommonPage«Express对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«Express对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«Express对象»»"}}}}}, "/api/front/recharge/transferIn": {"post": {"tags": ["用户 -- 充值"], "summary": "余额转入", "consumes": ["application/json"], "parameters": [{"name": "price", "in": "query", "required": false, "description": "price", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/recharge/index": {"get": {"tags": ["用户 -- 充值"], "summary": "充值额度选择", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"giveMoney": {"type": "string", "description": "赠送金额"}, "id": {"type": "integer", "format": "int32", "description": "充值模板id"}, "price": {"type": "string", "description": "充值金额"}}, "title": "UserRechargeResponse对象", "description": "c", "$$ref": "#/definitions/UserRechargeResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserRechargeResponse对象»", "$$ref": "#/definitions/CommonResult«UserRechargeResponse对象»"}}}}}, "/api/front/recharge/wechat": {"post": {"tags": ["用户 -- 充值"], "summary": "公众号充值", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "充值", "schema": {"type": "object", "properties": {"from": {"type": "string", "description": "来源 | public =  微信公众号, weixinh5 =微信H5支付, routine = 小程序"}, "payType": {"type": "string", "description": "支付方式| weixin = 微信，alipay = 支付宝"}, "price": {"type": "number", "description": "充值金额"}, "rechar_id": {"type": "integer", "format": "int32", "description": "选择金额组合数据id"}}, "title": "UserRechargeRequest对象", "description": "充值", "$$ref": "#/definitions/UserRechargeRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"appId": {"type": "string", "description": "调用接口提交的公众账号ID"}, "h5PayUrl": {"type": "string", "description": "H5支付的跳转url"}, "nonceStr": {"type": "string", "description": "随机字符串"}, "package": {"type": "string", "description": "订单详情扩展字符串"}, "paySign": {"type": "string", "description": "签名"}, "prepayId": {"type": "string", "description": "微信生成的预支付回话标识，用于后续接口调用中使用，该值有效期为2小时,针对H5支付此参数无特殊用途"}, "signType": {"type": "string", "description": "签名方式"}, "timeStamp": {"type": "integer", "format": "int32", "description": "当前的时间，其他详见时间戳规则"}}, "title": "UserRechargePaymentResponse对象", "description": "充值返回对象", "$$ref": "#/definitions/UserRechargePaymentResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserRechargePaymentResponse对象»", "$$ref": "#/definitions/CommonResult«UserRechargePaymentResponse对象»"}}}}}, "/api/front/recharge/routine": {"post": {"tags": ["用户 -- 充值"], "summary": "小程序充值", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "充值", "schema": {"type": "object", "properties": {"from": {"type": "string", "description": "来源 | public =  微信公众号, weixinh5 =微信H5支付, routine = 小程序"}, "payType": {"type": "string", "description": "支付方式| weixin = 微信，alipay = 支付宝"}, "price": {"type": "number", "description": "充值金额"}, "rechar_id": {"type": "integer", "format": "int32", "description": "选择金额组合数据id"}}, "title": "UserRechargeRequest对象", "description": "充值", "$$ref": "#/definitions/UserRechargeRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«Map«string,object»»", "$$ref": "#/definitions/CommonResult«Map«string,object»»"}}}}}, "/api/front/address/edit": {"post": {"tags": ["用户 -- 地址"], "summary": "保存", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户地址", "schema": {"type": "object", "required": ["address", "detail", "isDefault", "phone", "realName"], "properties": {"address": {"type": "object", "required": ["city", "district", "province"], "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "district": {"type": "string", "description": "收货人所在区"}, "province": {"type": "string", "description": "收货人所在省"}}, "title": "UserAddressCityRequest对象", "description": "用户地址城市", "$$ref": "#/definitions/UserAddressCityRequest对象"}, "detail": {"type": "string", "description": "收货人详细地址"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "example": false, "description": "是否默认"}, "phone": {"type": "string", "description": "收货人电话"}, "realName": {"type": "string", "description": "收货人姓名"}}, "title": "UserAddressRequest对象", "description": "用户地址", "$$ref": "#/definitions/UserAddressRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserAddress对象»", "$$ref": "#/definitions/CommonResult«UserAddress对象»"}}}}}, "/api/front/address/list": {"get": {"tags": ["用户 -- 地址"], "summary": "列表", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserAddress对象»", "$$ref": "#/definitions/CommonPage«UserAddress对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserAddress对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserAddress对象»»"}}}}}, "/api/front/address/del": {"post": {"tags": ["用户 -- 地址"], "summary": "删除", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户地址", "schema": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "用户地址id"}}, "title": "UserAddressDelRequest对象", "description": "用户地址", "$$ref": "#/definitions/UserAddressDelRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/address/detail/{id}": {"get": {"tags": ["用户 -- 地址"], "summary": "获取单个地址", "consumes": ["text/plain"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserAddress对象»", "$$ref": "#/definitions/CommonResult«UserAddress对象»"}}}}}, "/api/front/address/default": {"get": {"tags": ["用户 -- 地址"], "summary": "获取默认地址", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserAddress对象»", "$$ref": "#/definitions/CommonResult«UserAddress对象»"}}}}}, "/api/front/address/default/set": {"post": {"tags": ["用户 -- 地址"], "summary": "设置默认地址", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户地址", "schema": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "用户地址id"}}, "title": "UserAddressDelRequest对象", "description": "用户地址", "$$ref": "#/definitions/UserAddressDelRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserAddress对象»", "$$ref": "#/definitions/CommonResult«UserAddress对象»"}}}}}, "/api/front/collect/del": {"post": {"tags": ["用户 -- 点赞/收藏"], "summary": "取消收藏产品", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "商品点赞和收藏表", "schema": {"type": "object", "properties": {"category": {"type": "string", "description": "产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)"}, "id": {"type": "integer", "format": "int32", "description": "商品ID"}}, "title": "UserCollectRequest对象", "description": "商品点赞和收藏表", "$$ref": "#/definitions/UserCollectRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/collect/all": {"post": {"tags": ["用户 -- 点赞/收藏"], "summary": "批量收藏", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "商品点赞和收藏表", "schema": {"type": "object", "properties": {"category": {"type": "string", "description": "产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)"}, "id": {"type": "array", "description": "商品ID", "items": {"type": "integer", "format": "int32"}}}, "title": "StoreProductRelationRequest对象", "description": "商品点赞和收藏表", "$$ref": "#/definitions/StoreProductRelationRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/collect/add": {"post": {"tags": ["用户 -- 点赞/收藏"], "summary": "添加收藏产品", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "商品点赞和收藏表", "schema": {"type": "object", "properties": {"category": {"type": "string", "description": "产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)"}, "id": {"type": "integer", "format": "int32", "description": "商品ID"}}, "title": "UserCollectRequest对象", "description": "商品点赞和收藏表", "$$ref": "#/definitions/UserCollectRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/collect/user": {"get": {"tags": ["用户 -- 点赞/收藏"], "summary": "获取收藏产品", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"activity": {"type": "string", "description": "活动显示排序1=秒杀，2=砍价，3=拼团"}, "addTime": {"type": "integer", "format": "int32", "description": "添加时间"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "browse": {"type": "integer", "format": "int32", "description": "浏览量"}, "cateId": {"type": "string", "description": "分类id"}, "codePath": {"type": "string", "description": "商品二维码地址(用户小程序海报)"}, "cost": {"type": "number", "description": "成本价"}, "ficti": {"type": "integer", "format": "int32", "description": "虚拟销量"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isBargain": {"type": "boolean", "description": "砍价状态 0未开启 1开启"}, "isBenefit": {"type": "boolean", "description": "是否优惠"}, "isBest": {"type": "boolean", "description": "是否精品"}, "isDel": {"type": "boolean", "description": "是否删除"}, "isGood": {"type": "boolean", "description": "是否优品推荐"}, "isHot": {"type": "boolean", "description": "是否热卖"}, "isNew": {"type": "boolean", "description": "是否新品"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSeckill": {"type": "boolean", "description": "秒杀状态 0 未开启 1已开启"}, "isShow": {"type": "boolean", "description": "状态（0：未上架，1：上架）"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "merUse": {"type": "boolean", "description": "商户是否代理 0不可代理1可代理"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "soureLink": {"type": "string", "description": "淘宝京东1688类型"}, "specType": {"type": "boolean", "description": "规格 0单 1多"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "videoLink": {"type": "string", "description": "主图视频链接"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProduct对象", "description": "商品表", "$$ref": "#/definitions/StoreProduct对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«StoreProduct对象»", "$$ref": "#/definitions/CommonPage«StoreProduct对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«StoreProduct对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«StoreProduct对象»»"}}}}}, "/api/front/user/level/grade": {"get": {"tags": ["用户 -- 用户中心"], "summary": "会员等级列表", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "discount": {"type": "number", "description": "享受折扣"}, "experience": {"type": "integer", "format": "int32", "description": "购买金额|经验达到"}, "grade": {"type": "integer", "format": "int32", "description": "会员等级"}, "icon": {"type": "string", "description": "会员图标"}, "id": {"type": "integer", "format": "int32"}, "image": {"type": "string", "description": "会员卡背景"}, "isDel": {"type": "boolean", "description": "是否删除.1=删除,0=未删除"}, "isShow": {"type": "boolean", "description": "是否显示 1=显示,0=隐藏"}, "memo": {"type": "string", "description": "说明"}, "name": {"type": "string", "description": "会员名称"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "SystemUserLevel对象", "description": "设置用户等级表", "$$ref": "#/definitions/SystemUserLevel对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«SystemUserLevel对象»", "$$ref": "#/definitions/CommonPage«SystemUserLevel对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«SystemUserLevel对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«SystemUserLevel对象»»"}}}}}, "/api/front/brokerage_rank": {"get": {"tags": ["用户 -- 用户中心"], "summary": "佣金排行", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "type", "in": "query", "required": false, "description": "type", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«User对象»»", "$$ref": "#/definitions/CommonResult«List«User对象»»"}}}}}, "/api/front/user/edit": {"post": {"tags": ["用户 -- 用户中心"], "summary": "修改个人资料", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "修改个人资料", "schema": {"type": "object", "properties": {"avatar": {"type": "string", "description": "用户头像"}, "nickname": {"type": "string", "description": "用户昵称"}}, "title": "UserEditRequest对象", "description": "修改个人资料", "$$ref": "#/definitions/UserEditRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/user/brokerageRankNumber": {"get": {"tags": ["用户 -- 用户中心"], "summary": "当前用户在佣金排行第几名", "consumes": ["text/plain"], "parameters": [{"name": "type", "in": "query", "required": false, "description": "type", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}}, "title": "CommonResult«int»", "$$ref": "#/definitions/CommonResult«int»"}}}}}, "/api/front/userinfo": {"get": {"tags": ["用户 -- 用户中心"], "summary": "当前登录用户信息", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}, "message": {"type": "string"}}, "title": "CommonResult«User对象»", "$$ref": "#/definitions/CommonResult«User对象»"}}}}}, "/api/front/register/reset": {"post": {"tags": ["用户 -- 用户中心"], "summary": "手机号修改密码", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "修改密码", "schema": {"type": "object", "required": ["account", "<PERSON><PERSON>a", "password"], "properties": {"account": {"type": "string", "description": "手机号"}, "captcha": {"type": "string", "description": "验证码"}, "password": {"type": "string", "description": "密码"}}, "title": "PasswordRequest对象", "description": "修改密码", "$$ref": "#/definitions/PasswordRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/rank": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广人排行", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "type", "in": "query", "required": false, "description": "type", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«User对象»»", "$$ref": "#/definitions/CommonResult«List«User对象»»"}}}}}, "/api/front/spread/count/{type}": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广佣金/提现总和", "consumes": ["text/plain"], "parameters": [{"name": "type", "in": "path", "description": "类型 佣金类型3=佣金,4=提现", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "number"}}, "message": {"type": "string"}}, "title": "CommonResult«Map«string,bigdecimal»»", "$$ref": "#/definitions/CommonResult«Map«string,bigdecimal»»"}}}}}, "/api/front/spread/commission/{type}": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广佣金明细", "consumes": ["text/plain"], "parameters": [{"name": "type", "in": "path", "description": "类型 佣金类型|0=全部,1=消费,2=充值,3=返佣,4=提现", "required": true, "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "description": "月份"}, "list": {"type": "array", "description": "数据", "items": {"type": "object", "properties": {"add_time": {"type": "string", "format": "date-time", "description": "创建时间"}, "balance": {"type": "number", "description": "剩余"}, "category": {"type": "string", "description": "明细种类"}, "id": {"type": "integer", "format": "int32", "description": "用户账单id"}, "linkId": {"type": "string", "description": "关联id"}, "mark": {"type": "string", "description": "备注"}, "number": {"type": "number", "description": "明细数字"}, "pm": {"type": "integer", "format": "int32", "description": "0 = 支出 1 = 获得"}, "status": {"type": "integer", "format": "int32", "description": "0 = 带确定 1 = 有效 -1 = 无效"}, "title": {"type": "string", "description": "账单标题"}, "type": {"type": "string", "description": "明细类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户uid"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserBill对象", "description": "用户账单表", "$$ref": "#/definitions/UserBill对象"}}}, "title": "UserSpreadCommissionResponse对象", "description": "推广佣金明细", "$$ref": "#/definitions/UserSpreadCommissionResponse对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserSpreadCommissionResponse对象»", "$$ref": "#/definitions/CommonPage«UserSpreadCommissionResponse对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserSpreadCommissionResponse对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserSpreadCommissionResponse对象»»"}}}}}, "/api/front/commission": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广数据接口(昨天的佣金 累计提现金额 当前佣金)", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"commissionCount": {"type": "number", "description": "当前佣金"}, "extractCount": {"type": "number", "description": "累计提现金额"}, "lastDayCount": {"type": "number", "description": "昨天的佣金"}}, "title": "UserCommissionResponse对象", "description": "推广佣金明细", "$$ref": "#/definitions/UserCommissionResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserCommissionResponse对象»", "$$ref": "#/definitions/CommonResult«UserCommissionResponse对象»"}}}}}, "/api/front/user/spread/banner": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广海报图", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "id"}, "pic": {"type": "string", "description": "背景图"}, "title": {"type": "string", "description": "名称"}}, "title": "UserSpreadBannerResponse对象", "description": "用户推广海报", "$$ref": "#/definitions/UserSpreadBannerResponse对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«UserSpreadBannerResponse对象»»", "$$ref": "#/definitions/CommonResult«List«UserSpreadBannerResponse对象»»"}}}}}, "/api/front/spread/people": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广用户", "consumes": ["text/plain"], "parameters": [{"name": "grade", "in": "query", "required": false, "description": "推荐人类型|0=一级|1=二级", "type": "string"}, {"name": "isAsc", "in": "query", "required": false, "description": "排序值 DESC ASC", "type": "string"}, {"name": "keyword", "in": "query", "required": false, "description": "搜索关键字", "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "sortKey", "in": "query", "required": false, "description": "排序, 排序|childCount=团队排序,numberCount=金额排序,orderCount=订单排序", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"avatar": {"type": "string", "description": "用户头像"}, "childCount": {"type": "integer", "format": "int32", "description": "推广人数"}, "nickname": {"type": "string", "description": "用户昵称"}, "numberCount": {"type": "number", "description": "订单金额"}, "orderCount": {"type": "integer", "format": "int32", "description": "订单数量"}, "time": {"type": "string", "description": "添加时间"}, "uid": {"type": "integer", "format": "int32", "description": "用户编号"}}, "title": "UserSpreadPeopleResponse对象", "description": "推广人信息", "$$ref": "#/definitions/UserSpreadPeopleResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserSpreadPeopleResponse对象»", "$$ref": "#/definitions/CommonResult«UserSpreadPeopleResponse对象»"}}}}}, "/api/front/spread/order": {"get": {"tags": ["用户 -- 用户中心"], "summary": "推广订单", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64", "description": "累计推广订单"}, "list": {"type": "array", "description": "推广人列表", "items": {"type": "object", "properties": {"child": {"type": "array", "description": "推广订单信息", "items": {"type": "object", "properties": {"avatar": {"type": "string", "description": "用户头像"}, "nickname": {"type": "string", "description": "用户昵称"}, "number": {"type": "number", "description": "返佣金额"}, "orderId": {"type": "string", "description": "订单号"}, "time": {"type": "string", "format": "date-time", "description": "返佣时间"}, "type": {"type": "string", "description": "订单显示类型"}}, "title": "UserSpreadOrderItemChildResponse对象", "description": "推广订单信息子集", "$$ref": "#/definitions/UserSpreadOrderItemChildResponse对象"}}, "count": {"type": "integer", "format": "int32", "description": "推广条数"}, "time": {"type": "string", "description": "推广年月"}}, "title": "UserSpreadOrderItemResponse对象", "description": "推广订单信息", "$$ref": "#/definitions/UserSpreadOrderItemResponse对象"}}}, "title": "UserSpreadOrderResponse对象", "description": "推广订单", "$$ref": "#/definitions/UserSpreadOrderResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserSpreadOrderResponse对象»", "$$ref": "#/definitions/CommonResult«UserSpreadOrderResponse对象»"}}}}}, "/api/front/extract/cash": {"post": {"tags": ["用户 -- 用户中心"], "summary": "提现申请", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户提现", "schema": {"type": "object", "properties": {"alipayCode": {"type": "string", "description": "支付宝账号"}, "bankname": {"type": "string", "description": "提现银行名称"}, "cardum": {"type": "string", "description": "银行卡"}, "extractType": {"type": "string", "description": "提现方式| alipay=支付宝,bank=银行卡,weixin=微信"}, "money": {"type": "number", "description": "提现金额"}, "name": {"type": "string", "description": "姓名"}, "wechat": {"type": "string", "description": "微信号"}}, "title": "UserExtractRequest对象", "description": "用户提现", "$$ref": "#/definitions/UserExtractRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/extract/bank": {"get": {"tags": ["用户 -- 用户中心"], "summary": "提现银行/提现最低金额", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"brokenCommission": {"type": "number", "description": "冻结佣金"}, "brokenDay": {"type": "string", "description": "冻结天数"}, "commissionCount": {"type": "number", "description": "可提现佣金"}, "extractBank": {"type": "array", "description": "提现银行", "items": {"type": "string"}}, "minPrice": {"type": "string", "description": "提现最低金额"}}, "title": "UserExtractCashResponse对象", "description": "提现银行/提现最低金额", "$$ref": "#/definitions/UserExtractCashResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserExtractCashResponse对象»", "$$ref": "#/definitions/CommonResult«UserExtractCashResponse对象»"}}}}}, "/api/front/user/balance": {"get": {"tags": ["用户 -- 用户中心"], "summary": "用户资金统计", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"nowMoney": {"type": "number", "description": "当前总资金"}, "orderStatusSum": {"type": "number", "description": "累计消费"}, "recharge": {"type": "number", "description": "累计充值"}}, "title": "UserBalanceResponse对象", "description": "用户资金统计", "$$ref": "#/definitions/UserBalanceResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserBalanceResponse对象»", "$$ref": "#/definitions/CommonResult«UserBalanceResponse对象»"}}}}}, "/api/front/integral/list": {"get": {"tags": ["用户 -- 用户中心"], "summary": "积分记录", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"add_time": {"type": "string", "format": "date-time", "description": "创建时间"}, "balance": {"type": "number", "description": "剩余"}, "category": {"type": "string", "description": "明细种类"}, "id": {"type": "integer", "format": "int32", "description": "用户账单id"}, "linkId": {"type": "string", "description": "关联id"}, "mark": {"type": "string", "description": "备注"}, "number": {"type": "number", "description": "明细数字"}, "pm": {"type": "integer", "format": "int32", "description": "0 = 支出 1 = 获得"}, "status": {"type": "integer", "format": "int32", "description": "0 = 带确定 1 = 有效 -1 = 无效"}, "title": {"type": "string", "description": "账单标题"}, "type": {"type": "string", "description": "明细类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户uid"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserBill对象", "description": "用户账单表", "$$ref": "#/definitions/UserBill对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserBill对象»", "$$ref": "#/definitions/CommonPage«UserBill对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserBill对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserBill对象»»"}}}}}, "/api/front/user/expList": {"get": {"tags": ["用户 -- 用户中心"], "summary": "经验记录", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"add_time": {"type": "string", "format": "date-time", "description": "创建时间"}, "balance": {"type": "number", "description": "剩余"}, "category": {"type": "string", "description": "明细种类"}, "id": {"type": "integer", "format": "int32", "description": "用户账单id"}, "linkId": {"type": "string", "description": "关联id"}, "mark": {"type": "string", "description": "备注"}, "number": {"type": "number", "description": "明细数字"}, "pm": {"type": "integer", "format": "int32", "description": "0 = 支出 1 = 获得"}, "status": {"type": "integer", "format": "int32", "description": "0 = 带确定 1 = 有效 -1 = 无效"}, "title": {"type": "string", "description": "账单标题"}, "type": {"type": "string", "description": "明细类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户uid"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserBill对象", "description": "用户账单表", "$$ref": "#/definitions/UserBill对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserBill对象»", "$$ref": "#/definitions/CommonPage«UserBill对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserBill对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserBill对象»»"}}}}}, "/api/front/binding": {"post": {"tags": ["用户 -- 用户中心"], "summary": "绑定手机号", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "绑定手机号", "schema": {"type": "object", "required": ["account", "<PERSON><PERSON>a"], "properties": {"account": {"type": "string", "description": "手机号"}, "captcha": {"type": "string", "description": "验证码"}}, "title": "UserBindingRequest对象", "description": "绑定手机号", "$$ref": "#/definitions/UserBindingRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/menu/user": {"get": {"tags": ["用户 -- 用户中心"], "summary": "获取个人中心菜单", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}, "message": {"type": "string"}}, "title": "CommonResult«HashMap«string,object»»", "$$ref": "#/definitions/CommonResult«HashMap«string,object»»"}}}}}, "/api/front/user": {"get": {"tags": ["用户 -- 用户中心"], "summary": "获取个人资料", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "integer", "format": "int32", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "couponCount": {"type": "integer", "format": "int32", "description": "用户优惠券数量"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "integer", "format": "int32", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "orderStatusNum": {"type": "object", "properties": {"noBuy": {"type": "integer", "format": "int32", "description": "未支付订单数量"}, "noPink": {"type": "integer", "format": "int32", "description": "拼团的订单数量"}, "noPostage": {"type": "integer", "format": "int32", "description": "未发货订单数量"}, "noRefund": {"type": "integer", "format": "int32", "description": "退款的订单数量"}, "noReply": {"type": "integer", "format": "int32", "description": "未评论订单数量"}, "noTake": {"type": "integer", "format": "int32", "description": "未收货订单数量"}}, "title": "UserCenterOrderStatusNumResponse对象", "description": "个人中心 -- 订单状态数量", "$$ref": "#/definitions/UserCenterOrderStatusNumResponse对象"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "pwd": {"type": "string", "description": "用户密码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广元id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}, "vip": {"type": "boolean", "description": "是否会员"}, "vipIcon": {"type": "string", "description": "会员图标"}, "vipName": {"type": "string", "description": "会员名称"}}, "title": "UserCenterResponse对象", "description": "个人中心", "$$ref": "#/definitions/UserCenterResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserCenterResponse对象»", "$$ref": "#/definitions/CommonResult«UserCenterResponse对象»"}}}}}, "/api/front/sendCode": {"post": {"tags": ["用户 -- 登录注册"], "summary": " 发送短信", "consumes": ["application/json"], "parameters": [{"name": "phone", "in": "query", "required": false, "description": "手机号码", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/login/mobile": {"post": {"tags": ["用户 -- 登录注册"], "summary": "手机号登录接口", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "手机号注册", "schema": {"type": "object", "required": ["account", "<PERSON><PERSON>a"], "properties": {"account": {"type": "string", "description": "手机号"}, "captcha": {"type": "string", "description": "验证码"}, "spread": {"type": "integer", "format": "int32", "description": "推广人id"}}, "title": "LoginMobileRequest对象", "description": "手机号注册", "$$ref": "#/definitions/LoginMobileRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"expiresTime": {"type": "string", "format": "date-time", "description": "用户登录密钥到期时间"}, "token": {"type": "string", "description": "用户登录密钥"}, "user": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "title": "LoginResponse", "description": "用户登录返回数据", "$$ref": "#/definitions/LoginResponse"}, "message": {"type": "string"}}, "title": "CommonResult«LoginResponse»", "$$ref": "#/definitions/CommonResult«LoginResponse»"}}}}}, "/api/front/login": {"post": {"tags": ["用户 -- 登录注册"], "summary": "账号密码登录", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "手机快速登录", "schema": {"type": "object", "required": ["account", "password"], "properties": {"account": {"type": "string", "example": ***********, "description": "手机号"}, "password": {"type": "string", "example": "Abc123", "description": "密码"}}, "title": "LoginRequest对象", "description": "手机快速登录", "$$ref": "#/definitions/LoginRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"expiresTime": {"type": "string", "format": "date-time", "description": "用户登录密钥到期时间"}, "token": {"type": "string", "description": "用户登录密钥"}, "user": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}}, "title": "LoginResponse", "description": "用户登录返回数据", "$$ref": "#/definitions/LoginResponse"}, "message": {"type": "string"}}, "title": "CommonResult«LoginResponse»", "$$ref": "#/definitions/CommonResult«LoginResponse»"}}}}}, "/api/front/logout": {"get": {"tags": ["用户 -- 登录注册"], "summary": "退出", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/user/sign/list": {"get": {"tags": ["用户 -- 签到"], "summary": "分页列表", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"createDay": {"type": "string", "format": "date-time", "description": "签到日期"}, "number": {"type": "integer", "format": "int32", "description": "获得积分"}, "title": {"type": "string", "description": "签到说明"}}, "title": "UserSign对象", "description": "签到记录表", "$$ref": "#/definitions/UserSign对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserSign对象»", "$$ref": "#/definitions/CommonPage«UserSign对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserSign对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserSign对象»»"}}}}}, "/api/front/user/sign/month": {"get": {"tags": ["用户 -- 签到"], "summary": "分页列表", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"createDay": {"type": "string", "format": "date-time", "description": "签到日期"}, "number": {"type": "integer", "format": "int32", "description": "获得积分"}, "title": {"type": "string", "description": "签到说明"}}, "title": "UserSign对象", "description": "签到记录表", "$$ref": "#/definitions/UserSign对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«UserSign对象»", "$$ref": "#/definitions/CommonPage«UserSign对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«UserSign对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«UserSign对象»»"}}}}}, "/api/front/user/sign/integral": {"get": {"tags": ["用户 -- 签到"], "summary": "签到", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"day": {"type": "integer", "format": "int32", "description": "第几天"}, "experience": {"type": "integer", "format": "int32", "description": "经验"}, "id": {"type": "integer", "format": "int32"}, "integral": {"type": "integer", "format": "int32", "description": "积分"}, "title": {"type": "string", "description": "显示文字"}}, "title": "SystemGroupDataSignConfigVo对象", "description": "签到记录", "$$ref": "#/definitions/SystemGroupDataSignConfigVo对象"}, "message": {"type": "string"}}, "title": "CommonResult«SystemGroupDataSignConfigVo对象»", "$$ref": "#/definitions/CommonResult«SystemGroupDataSignConfigVo对象»"}}}}}, "/api/front/user/sign/user": {"post": {"tags": ["用户 -- 签到"], "summary": "签到用户信息", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "个人签到信息", "schema": {"type": "object", "properties": {"all": {"type": "boolean", "description": "是否统计签到和统计积分使用情况|1=是,0=否"}, "integral": {"type": "boolean", "description": "是否统计积分使用情况|1=是,0=否"}, "sign": {"type": "boolean", "description": "是否统计签到|1=是,0=否"}}, "title": "UserSignInfoRequest对象", "description": "个人签到信息", "$$ref": "#/definitions/UserSignInfoRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"avatar": {"type": "string", "description": "用户头像"}, "deductionIntegral": {"type": "integer", "format": "int32", "description": "累计抵扣积分"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isDaySign": {"type": "boolean", "description": "今天是否签到"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "isYesterdaySign": {"type": "boolean", "description": "昨天是否签到"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "sumIntegral": {"type": "integer", "format": "int32", "description": "累计总积分"}, "sumSignDay": {"type": "integer", "format": "int32", "description": "累计签到次数"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "yesterdayIntegral": {"type": "integer", "format": "int32", "description": "昨天累计积分"}}, "title": "UserSignInfoResponse对象", "description": "修改个人资料", "$$ref": "#/definitions/UserSignInfoResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«UserSignInfoResponse对象»", "$$ref": "#/definitions/CommonResult«UserSignInfoResponse对象»"}}}}}, "/api/front/user/sign/get": {"get": {"tags": ["用户 -- 签到"], "summary": "详情", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}, "message": {"type": "string"}}, "title": "CommonResult«HashMap«string,object»»", "$$ref": "#/definitions/CommonResult«HashMap«string,object»»"}}}}}, "/api/front/user/sign/config": {"get": {"tags": ["用户 -- 签到"], "summary": "配置", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "integer", "format": "int32", "description": "第几天"}, "experience": {"type": "integer", "format": "int32", "description": "经验"}, "id": {"type": "integer", "format": "int32"}, "integral": {"type": "integer", "format": "int32", "description": "积分"}, "title": {"type": "string", "description": "显示文字"}}, "title": "SystemGroupDataSignConfigVo对象", "description": "签到记录", "$$ref": "#/definitions/SystemGroupDataSignConfigVo对象"}}, "page": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "totalPage": {"type": "integer", "format": "int32"}}, "title": "CommonPage«SystemGroupDataSignConfigVo对象»", "$$ref": "#/definitions/CommonPage«SystemGroupDataSignConfigVo对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«SystemGroupDataSignConfigVo对象»»", "$$ref": "#/definitions/CommonResult«CommonPage«SystemGroupDataSignConfigVo对象»»"}}}}}, "/api/front/coupon/list": {"get": {"tags": ["营销 -- 优惠券"], "summary": "我的优惠券", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON><PERSON>"], "properties": {"avatar": {"type": "string", "description": "用户头像"}, "cid": {"type": "integer", "format": "int32", "description": "兑换的项目id"}, "couponId": {"type": "integer", "format": "int32", "description": "优惠券发布id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "endTime": {"type": "string", "format": "date-time", "description": "过期时间"}, "id": {"type": "integer", "format": "int32", "description": "id"}, "isValid": {"type": "boolean", "description": "用户头像"}, "minPrice": {"type": "number", "description": "最低消费多少金额可用优惠券"}, "money": {"type": "number", "description": "优惠券的面值"}, "name": {"type": "string", "description": "优惠券名称"}, "nickname": {"type": "string", "description": "用户昵称"}, "primaryKey": {"type": "string", "description": "主键id 商品id/分类id"}, "startTime": {"type": "string", "format": "date-time", "description": "开始使用时间"}, "status": {"type": "integer", "format": "int32", "description": "状态（0：未使用，1：已使用, 2:已失效）"}, "type": {"type": "string", "description": "获取方式"}, "uid": {"type": "integer", "format": "int32", "description": "领取人id"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "useTime": {"type": "string", "format": "date-time", "description": "使用时间"}, "useType": {"type": "integer", "format": "int32", "description": "使用类型 1 全场通用, 2 商品券, 3 品类券"}}, "title": "StoreCouponUserResponse对象", "description": "优惠券记录表", "$$ref": "#/definitions/StoreCouponUserResponse对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«StoreCouponUserResponse对象»»", "$$ref": "#/definitions/CommonResult«List«StoreCouponUserResponse对象»»"}}}}}, "/api/front/coupon/receive/batch": {"post": {"tags": ["营销 -- 优惠券"], "summary": "批量领券", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户领券", "schema": {"type": "object", "properties": {"couponId": {"type": "array", "description": "优惠券id", "items": {"type": "integer", "format": "int32"}}}, "title": "UserCouponReceiveRequest对象", "description": "用户领券", "$$ref": "#/definitions/UserCouponReceiveRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/coupon/receive": {"post": {"tags": ["营销 -- 优惠券"], "summary": "领券", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "用户领券", "schema": {"type": "object", "properties": {"couponId": {"type": "array", "description": "优惠券id", "items": {"type": "integer", "format": "int32"}}}, "title": "UserCouponReceiveRequest对象", "description": "用户领券", "$$ref": "#/definitions/UserCouponReceiveRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "string"}, "message": {"type": "string"}}, "title": "CommonResult«string»", "$$ref": "#/definitions/CommonResult«string»"}}}}}, "/api/front/order/again": {"post": {"tags": ["订单"], "summary": "再次下单", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"nui": {"type": "string", "description": "订单id"}}, "title": "OrderAgainRequest", "$$ref": "#/definitions/OrderAgainRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/del": {"post": {"tags": ["订单"], "summary": "删除订单", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "query", "required": false, "description": "id", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/order/product": {"post": {"tags": ["订单"], "summary": "待评价商品信息查询", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32", "description": "订单id"}, "uni": {"type": "string", "description": "商品attrid"}}, "title": "GetProductReply", "$$ref": "#/definitions/GetProductReply"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/pay": {"post": {"tags": ["订单"], "summary": "支付", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "订单支付", "schema": {"type": "object", "properties": {"from": {"type": "string", "description": "支付平台"}, "paytype": {"type": "string", "description": "支付类型"}, "uni": {"type": "string", "description": "订单id"}}, "title": "OrderPayRequest对象", "description": "订单支付", "$$ref": "#/definitions/OrderPayRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/express/{orderId}": {"get": {"tags": ["订单"], "summary": "物流信息查询", "consumes": ["text/plain"], "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/create/{key}": {"post": {"tags": ["订单"], "summary": "生成订单", "consumes": ["application/json"], "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "type": "string"}, {"name": "root", "in": "body", "description": "创建订单参数", "schema": {"type": "object", "required": ["isNew"], "properties": {"addressId": {"type": "integer", "format": "int32", "description": "收货地址id"}, "bargainId": {"type": "integer", "format": "int32"}, "combinationId": {"type": "integer", "format": "int32"}, "couponId": {"type": "integer", "format": "int32"}, "formId": {"type": "integer", "format": "int32"}, "from": {"type": "string"}, "isNew": {"type": "boolean", "description": "是否为立即购买"}, "mark": {"type": "string"}, "payType": {"type": "string"}, "phone": {"type": "string", "description": "手机号码"}, "pinkId": {"type": "integer", "format": "int32"}, "realName": {"type": "string", "description": "真实名称"}, "seckillId": {"type": "integer", "format": "int32"}, "shippingType": {"type": "integer", "format": "int32", "description": "快递类型"}, "storeId": {"type": "integer", "format": "int32"}, "useIntegral": {"type": "boolean"}}, "title": "OrderCreateRequest对象", "description": "创建订单参数", "$$ref": "#/definitions/OrderCreateRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/confirm": {"post": {"tags": ["订单"], "summary": "确认订单", "consumes": ["application/json"], "parameters": [{"name": "cartIds", "in": "query", "required": false, "description": "cartIds", "type": "string"}, {"name": "isNew", "in": "query", "required": false, "description": "isNew", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"addressInfo": {"type": "object", "properties": {"city": {"type": "string", "description": "收货人所在市"}, "cityId": {"type": "integer", "format": "int32", "description": "城市id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "detail": {"type": "string", "description": "收货人详细地址"}, "district": {"type": "string", "description": "收货人所在区"}, "id": {"type": "integer", "format": "int32", "description": "用户地址id"}, "isDefault": {"type": "boolean", "description": "是否默认"}, "isDel": {"type": "boolean", "description": "是否删除"}, "latitude": {"type": "string", "description": "纬度"}, "longitude": {"type": "string", "description": "经度"}, "phone": {"type": "string", "description": "收货人电话"}, "postCode": {"type": "integer", "format": "int32", "description": "邮编"}, "province": {"type": "string", "description": "收货人所在省"}, "realName": {"type": "string", "description": "收货人姓名"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}}, "title": "UserAddress对象", "description": "用户地址表", "$$ref": "#/definitions/UserAddress对象"}, "cartInfo": {"type": "array", "items": {"type": "object", "properties": {"addTime": {"type": "string"}, "attrStatus": {"type": "boolean", "description": "商品是否有效"}, "bargainId": {"type": "integer", "format": "int32", "description": "砍价id"}, "brokerage": {"type": "number", "description": "一级分佣"}, "brokerageTwo": {"type": "number", "description": "二级分佣"}, "cartNum": {"type": "integer", "format": "int32", "description": "商品数量"}, "combinationId": {"type": "integer", "format": "int32", "description": "拼团id"}, "costPrice": {"type": "number"}, "id": {"type": "integer", "format": "int64", "description": "购物车表ID"}, "isNew": {"type": "boolean", "description": "是否为立即购买"}, "isReply": {"type": "integer", "format": "int32"}, "productAttrUnique": {"type": "string", "description": "商品属性"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "productInfo": {"type": "object", "properties": {"attrInfo": {"type": "object", "properties": {"attrValue": {"type": "string", "description": "产品属性值和属性名对应关系"}, "barCode": {"type": "string", "description": "商品条码"}, "brokerage": {"type": "number", "description": "一级返佣"}, "brokerageTwo": {"type": "number", "description": "二级返佣"}, "cost": {"type": "number", "description": "成本价"}, "id": {"type": "integer", "format": "int32", "description": "attrId"}, "image": {"type": "string", "description": "图片"}, "otPrice": {"type": "number", "description": "原价"}, "price": {"type": "number", "description": "属性金额"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "quota": {"type": "integer", "format": "int32", "description": "活动限购数量"}, "quotaShow": {"type": "integer", "format": "int32", "description": "活动限购数量显示"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "stock": {"type": "integer", "format": "int32", "description": "属性对应的库存"}, "suk": {"type": "string", "description": "商品属性索引值 (attr_value|attr_value[|....])"}, "type": {"type": "integer", "format": "int32", "description": "活动类型 0=商品，1=秒杀，2=砍价，3=拼团"}, "unique": {"type": "string", "description": "唯一值"}, "volume": {"type": "number", "description": "体积"}, "weight": {"type": "number", "description": "重量"}}, "title": "StoreProductAttrValue对象", "description": "商品属性值表", "$$ref": "#/definitions/StoreProductAttrValue对象"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "cateId": {"type": "string", "description": "分类id"}, "cost": {"type": "number", "description": "成本价"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProductCartProductInfoResponse对象", "description": "商品信息，购物车列表使用", "$$ref": "#/definitions/StoreProductCartProductInfoResponse对象"}, "seckillId": {"type": "integer", "format": "int32", "description": "秒杀商品ID"}, "truePrice": {"type": "number"}, "trueStock": {"type": "integer", "format": "int32"}, "type": {"type": "string", "description": "类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户ID"}, "vipTruePrice": {"type": "number"}}, "title": "StoreCartResponse", "description": "购物车ListResponse", "$$ref": "#/definitions/StoreCartResponse"}}, "deduction": {"type": "boolean"}, "integralRatio": {"type": "string"}, "offlinePayStatus": {"type": "string"}, "offlinePostage": {"type": "string"}, "orderKey": {"type": "string"}, "other": {"type": "object", "additionalProperties": {"type": "object"}}, "payWeixinOpen": {"type": "string"}, "priceGroup": {"type": "object", "properties": {"costPrice": {"type": "number"}, "couponPrice": {"type": "number"}, "deductionPrice": {"type": "number"}, "payPostage": {"type": "number"}, "payPrice": {"type": "number"}, "storeFreePostage": {"type": "number"}, "storePostage": {"type": "number"}, "totalPrice": {"type": "number"}, "usedIntegral": {"type": "number"}, "vipPrice": {"type": "number"}}, "title": "PriceGroupResponse", "$$ref": "#/definitions/PriceGroupResponse"}, "storeSelfMention": {"type": "string"}, "systemStore": {"type": "string"}, "usableCoupon": {"type": "object", "required": ["<PERSON><PERSON><PERSON>"], "properties": {"avatar": {"type": "string", "description": "用户头像"}, "cid": {"type": "integer", "format": "int32", "description": "兑换的项目id"}, "couponId": {"type": "integer", "format": "int32", "description": "优惠券发布id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "endTime": {"type": "string", "format": "date-time", "description": "过期时间"}, "id": {"type": "integer", "format": "int32", "description": "id"}, "isValid": {"type": "boolean", "description": "用户头像"}, "minPrice": {"type": "number", "description": "最低消费多少金额可用优惠券"}, "money": {"type": "number", "description": "优惠券的面值"}, "name": {"type": "string", "description": "优惠券名称"}, "nickname": {"type": "string", "description": "用户昵称"}, "primaryKey": {"type": "string", "description": "主键id 商品id/分类id"}, "startTime": {"type": "string", "format": "date-time", "description": "开始使用时间"}, "status": {"type": "integer", "format": "int32", "description": "状态（0：未使用，1：已使用, 2:已失效）"}, "type": {"type": "string", "description": "获取方式"}, "uid": {"type": "integer", "format": "int32", "description": "领取人id"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "useTime": {"type": "string", "format": "date-time", "description": "使用时间"}, "useType": {"type": "integer", "format": "int32", "description": "使用类型 1 全场通用, 2 商品券, 3 品类券"}}, "title": "StoreCouponUserResponse对象", "description": "优惠券记录表", "$$ref": "#/definitions/StoreCouponUserResponse对象"}, "userInfo": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "addIp": {"type": "string", "description": "添加ip"}, "addres": {"type": "string", "description": "详细地址"}, "adminid": {"type": "integer", "format": "int32", "description": "管理员编号 "}, "avatar": {"type": "string", "description": "用户头像"}, "birthday": {"type": "string", "description": "生日"}, "brokeragePrice": {"type": "number", "description": "佣金金额"}, "cardId": {"type": "string", "description": "身份证号码"}, "cleanTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "experience": {"type": "integer", "format": "int32", "description": "用户剩余经验"}, "groupId": {"type": "string", "description": "用户分组id"}, "integral": {"type": "number", "description": "用户剩余积分"}, "isPromoter": {"type": "boolean", "description": "是否为推广员"}, "lastIp": {"type": "string", "description": "最后一次登录ip"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "level": {"type": "integer", "format": "int32", "description": "等级"}, "loginType": {"type": "string", "description": "用户登陆类型，h5,wechat,routine"}, "mark": {"type": "string", "description": "用户备注"}, "nickname": {"type": "string", "description": "用户昵称"}, "nowMoney": {"type": "number", "description": "用户余额"}, "partnerId": {"type": "integer", "format": "int32", "description": "合伙人id"}, "path": {"type": "string", "description": "用户推广等级"}, "payCount": {"type": "integer", "format": "int32", "description": "用户购买次数"}, "phone": {"type": "string", "description": "手机号码"}, "realName": {"type": "string", "description": "真实姓名"}, "signNum": {"type": "integer", "format": "int32", "description": "连续签到天数"}, "spreadCount": {"type": "integer", "format": "int32", "description": "下级人数"}, "spreadTime": {"type": "string", "format": "date-time", "description": "推广员关联时间"}, "spreadUid": {"type": "integer", "format": "int32", "description": "推广人id"}, "status": {"type": "boolean", "description": "1为正常，0为禁止"}, "subscribe": {"type": "boolean", "description": "是否关注公众号"}, "tagId": {"type": "string", "description": "用户标签id"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "updateTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "userType": {"type": "string", "description": "用户类型"}}, "title": "User对象", "description": "用户表", "$$ref": "#/definitions/User对象"}, "yuePayStatus": {"type": "string"}}, "title": "ConfirmOrderResponse", "$$ref": "#/definitions/ConfirmOrderResponse"}, "message": {"type": "string"}}, "title": "CommonResult«ConfirmOrderResponse»", "$$ref": "#/definitions/CommonResult«ConfirmOrderResponse»"}}}}}, "/api/front/order/computed/{key}": {"post": {"tags": ["订单"], "summary": "计算价格", "consumes": ["application/json"], "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "type": "string"}, {"name": "root", "in": "body", "schema": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int32", "description": "地址id"}, "couponId": {"type": "integer", "format": "int32", "description": "优惠券id"}, "payType": {"type": "string", "description": "支付类型"}, "shippingType": {"type": "integer", "format": "int32", "description": "快递类型"}, "useIntegral": {"type": "boolean", "description": "抵扣积分"}}, "title": "OrderComputedRequest", "$$ref": "#/definitions/OrderComputedRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/list": {"get": {"tags": ["订单"], "summary": "订单列表", "consumes": ["text/plain"], "parameters": [{"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "type", "in": "query", "required": false, "description": "评价等级|0=未支付,1=待发货,2=待收货,3=待评价,4=已完成", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "properties": {"addTime": {"type": "string"}, "cartInfo": {"type": "array", "items": {"type": "object", "properties": {"info": {"type": "object", "properties": {"addTime": {"type": "string"}, "attrStatus": {"type": "boolean", "description": "商品是否有效"}, "bargainId": {"type": "integer", "format": "int32", "description": "砍价id"}, "brokerage": {"type": "number", "description": "一级分佣"}, "brokerageTwo": {"type": "number", "description": "二级分佣"}, "cartNum": {"type": "integer", "format": "int32", "description": "商品数量"}, "combinationId": {"type": "integer", "format": "int32", "description": "拼团id"}, "costPrice": {"type": "number"}, "id": {"type": "integer", "format": "int64", "description": "购物车表ID"}, "isNew": {"type": "boolean", "description": "是否为立即购买"}, "isReply": {"type": "integer", "format": "int32"}, "productAttrUnique": {"type": "string", "description": "商品属性"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "productInfo": {"type": "object", "properties": {"attrInfo": {"type": "object", "properties": {"attrValue": {"type": "string", "description": "产品属性值和属性名对应关系"}, "barCode": {"type": "string", "description": "商品条码"}, "brokerage": {"type": "number", "description": "一级返佣"}, "brokerageTwo": {"type": "number", "description": "二级返佣"}, "cost": {"type": "number", "description": "成本价"}, "id": {"type": "integer", "format": "int32", "description": "attrId"}, "image": {"type": "string", "description": "图片"}, "otPrice": {"type": "number", "description": "原价"}, "price": {"type": "number", "description": "属性金额"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "quota": {"type": "integer", "format": "int32", "description": "活动限购数量"}, "quotaShow": {"type": "integer", "format": "int32", "description": "活动限购数量显示"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "stock": {"type": "integer", "format": "int32", "description": "属性对应的库存"}, "suk": {"type": "string", "description": "商品属性索引值 (attr_value|attr_value[|....])"}, "type": {"type": "integer", "format": "int32", "description": "活动类型 0=商品，1=秒杀，2=砍价，3=拼团"}, "unique": {"type": "string", "description": "唯一值"}, "volume": {"type": "number", "description": "体积"}, "weight": {"type": "number", "description": "重量"}}, "title": "StoreProductAttrValue对象", "description": "商品属性值表", "$$ref": "#/definitions/StoreProductAttrValue对象"}, "barCode": {"type": "string", "description": "商品条码（一维码）"}, "cateId": {"type": "string", "description": "分类id"}, "cost": {"type": "number", "description": "成本价"}, "giveIntegral": {"type": "number", "description": "获得积分"}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "isPostage": {"type": "boolean", "description": "是否包邮"}, "isSub": {"type": "boolean", "description": "是否单独分佣"}, "keyword": {"type": "string", "description": "关键字"}, "merId": {"type": "integer", "format": "int32", "description": "商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)"}, "otPrice": {"type": "number", "description": "市场价"}, "postage": {"type": "number", "description": "邮费"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sliderImage": {"type": "string", "description": "轮播图"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeInfo": {"type": "string", "description": "商品简介"}, "storeName": {"type": "string", "description": "商品名称"}, "tempId": {"type": "integer", "format": "int32", "description": "运费模板ID"}, "unitName": {"type": "string", "description": "单位名"}, "vipPrice": {"type": "number", "description": "会员价格"}}, "title": "StoreProductCartProductInfoResponse对象", "description": "商品信息，购物车列表使用", "$$ref": "#/definitions/StoreProductCartProductInfoResponse对象"}, "seckillId": {"type": "integer", "format": "int32", "description": "秒杀商品ID"}, "truePrice": {"type": "number"}, "trueStock": {"type": "integer", "format": "int32"}, "type": {"type": "string", "description": "类型"}, "uid": {"type": "integer", "format": "int32", "description": "用户ID"}, "vipTruePrice": {"type": "number"}}, "title": "StoreCartResponse", "description": "购物车ListResponse", "$$ref": "#/definitions/StoreCartResponse"}, "orderId": {"type": "integer", "format": "int32", "description": "订单id"}, "productId": {"type": "integer", "format": "int32", "description": "商品ID"}, "unique": {"type": "string", "description": "唯一id"}}, "title": "StoreOrderInfoVo对象", "description": "订单购物详情表", "$$ref": "#/definitions/StoreOrderInfoVo对象"}}, "offlinePayStatus": {"type": "integer", "format": "int32"}, "payTime": {"type": "string"}, "status": {"type": "object", "properties": {"deliveryType": {"type": "string"}, "msg": {"type": "string"}, "payType": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "title": "OrderAgainItemVo", "$$ref": "#/definitions/OrderAgainItemVo"}, "statusPic": {"type": "string"}, "storeOrder": {"type": "object", "properties": {"backIntegral": {"type": "number", "description": "给用户退了多少积分"}, "bargainId": {"type": "integer", "format": "int32", "description": "砍价id"}, "clerkId": {"type": "integer", "format": "int32", "description": "店员id"}, "combinationId": {"type": "integer", "format": "int32", "description": "拼团商品id0一般商品"}, "cost": {"type": "number", "description": "成本价"}, "couponId": {"type": "integer", "format": "int32", "description": "优惠券id"}, "couponPrice": {"type": "number", "description": "优惠券金额"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "deductionPrice": {"type": "number", "description": "抵扣金额"}, "deliveryId": {"type": "string", "description": "快递单号/手机号"}, "deliveryName": {"type": "string", "description": "快递名称/送货人姓名"}, "deliveryType": {"type": "string", "description": "发货类型"}, "freightPrice": {"type": "number", "description": "运费金额"}, "gainIntegral": {"type": "number", "description": "消费赚取积分"}, "id": {"type": "integer", "format": "int32", "description": "订单ID"}, "isChannel": {"type": "integer", "format": "int32", "description": "支付渠道(0微信公众号1微信小程序)"}, "isDel": {"type": "boolean", "description": "是否删除"}, "isMerCheck": {"type": "integer", "format": "int32"}, "isRemind": {"type": "boolean", "description": "消息提醒"}, "isSystemDel": {"type": "boolean", "description": "后台是否删除"}, "mark": {"type": "string", "description": "备注"}, "merId": {"type": "integer", "format": "int32", "description": "商户ID"}, "orderId": {"type": "string", "description": "订单号"}, "paid": {"type": "boolean", "description": "支付状态"}, "payPostage": {"type": "number", "description": "支付邮费"}, "payPrice": {"type": "number", "description": "实际支付金额"}, "payTime": {"type": "string", "format": "date-time", "description": "支付时间"}, "payType": {"type": "string", "description": "支付方式"}, "pinkId": {"type": "integer", "format": "int32", "description": "拼团id 0没有拼团"}, "realName": {"type": "string", "description": "用户姓名"}, "refundPrice": {"type": "number", "description": "退款金额"}, "refundReason": {"type": "string", "description": "不退款的理由"}, "refundReasonTime": {"type": "string", "format": "date-time", "description": "退款时间"}, "refundReasonWap": {"type": "string", "description": "前台退款原因"}, "refundReasonWapExplain": {"type": "string", "description": "退款用户说明"}, "refundReasonWapImg": {"type": "string", "description": "退款图片"}, "refundStatus": {"type": "integer", "format": "int32", "description": "0 未退款 1 申请中 2 已退款"}, "remark": {"type": "string", "description": "管理员备注"}, "seckillId": {"type": "integer", "format": "int32", "description": "秒杀商品ID"}, "shippingType": {"type": "integer", "format": "int32", "description": "配送方式 1=快递 ，2=门店自提"}, "status": {"type": "integer", "format": "int32", "description": "订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货，待评价；3：已完成；）"}, "storeId": {"type": "integer", "format": "int32", "description": "门店id"}, "totalNum": {"type": "integer", "format": "int32", "description": "订单商品总数"}, "totalPostage": {"type": "number", "description": "邮费"}, "totalPrice": {"type": "number", "description": "订单总价"}, "uid": {"type": "integer", "format": "int32", "description": "用户id"}, "unique": {"type": "string", "description": "唯一id(md5加密)类似id"}, "useIntegral": {"type": "number", "description": "使用积分"}, "userAddress": {"type": "string", "description": "详细地址"}, "userPhone": {"type": "string", "description": "用户电话"}, "verifyCode": {"type": "string", "description": "核销码"}}, "title": "StoreOrder对象", "description": "订单表", "$$ref": "#/definitions/StoreOrder对象"}}, "title": "OrderAgainVo", "$$ref": "#/definitions/OrderAgainVo"}}, "message": {"type": "string"}}, "title": "CommonResult«List«OrderAgainVo»»", "$$ref": "#/definitions/CommonResult«List«OrderAgainVo»»"}}}}}, "/api/front/order/cancel": {"post": {"tags": ["订单"], "summary": "订单取消", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "query", "required": false, "description": "id", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/order/data": {"get": {"tags": ["订单"], "summary": "订单头部数量", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/take": {"post": {"tags": ["订单"], "summary": "订单收货", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "query", "required": false, "description": "id", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/order/detail/{orderId}": {"get": {"tags": ["订单"], "summary": "订单详情", "consumes": ["text/plain"], "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/refund/reason": {"get": {"tags": ["订单"], "summary": "订单退款理由", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/order/refund": {"post": {"tags": ["订单"], "summary": "订单退款申请", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "订单申请退款", "schema": {"type": "object", "required": ["id", "text"], "properties": {"id": {"type": "integer", "format": "int32", "description": "订单id"}, "refund_reason_wap_explain": {"type": "string", "description": "备注说明"}, "refund_reason_wap_img": {"type": "string", "description": "退款凭证(多个图片请用,(英文逗号)隔开)"}, "text": {"type": "string", "description": "退款原因"}}, "title": "OrderRefundApplyRequest对象", "description": "订单申请退款", "$$ref": "#/definitions/OrderRefundApplyRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/order/comment": {"post": {"tags": ["订单"], "summary": "评价订单", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "评论表", "schema": {"type": "object", "required": ["comment", "pics", "productId", "productScore", "serviceScore"], "properties": {"comment": {"type": "string", "description": "评论内容"}, "oid": {"type": "integer", "format": "int32", "description": "订单ID， 移动端必须传递此参数"}, "pics": {"type": "string", "description": "评论图片"}, "productId": {"type": "integer", "format": "int32", "description": "商品id"}, "productScore": {"type": "integer", "format": "int32", "example": 5, "description": "商品分数"}, "serviceScore": {"type": "integer", "format": "int32", "example": 5, "description": "服务分数"}, "unique": {"type": "string", "description": "商品 属性id"}, "userId": {"type": "integer", "format": "int32", "description": "用户id， 后端必须传递此参数"}}, "title": "StoreProductReplyAddRequest对象", "description": "评论表", "$$ref": "#/definitions/StoreProductReplyAddRequest对象"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "boolean"}, "message": {"type": "string"}}, "title": "CommonResult«boolean»", "$$ref": "#/definitions/CommonResult«boolean»"}}}}}, "/api/front/order/refund/verify": {"post": {"tags": ["订单"], "summary": "退款订单验证", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"refund_reason_wap_explain": {"type": "string", "description": "退款备注说明"}, "refund_reason_wap_img": {"type": "string", "description": "退款凭证图片"}, "text": {"type": "string", "description": "退款原因"}, "uni": {"type": "string", "description": "待退款订单"}}, "title": "OrderRefundVerifyRequest", "$$ref": "#/definitions/OrderRefundVerifyRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult«object»", "$$ref": "#/definitions/CommonResult«object»"}}}}}, "/api/front/share": {"get": {"tags": ["首页"], "summary": "分享配置", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}}, "title": "CommonResult«HashMap«string,string»»", "$$ref": "#/definitions/CommonResult«HashMap«string,string»»"}}}}}, "/api/front/search/keyword": {"get": {"tags": ["首页"], "summary": "热门搜索", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "message": {"type": "string"}}, "title": "CommonResult«List«HashMap«string,object»»»", "$$ref": "#/definitions/CommonResult«List«HashMap«string,object»»»"}}}}}, "/api/front/groom/list/{type}": {"get": {"tags": ["首页"], "summary": "首页产品的轮播图和产品信息", "consumes": ["text/plain"], "parameters": [{"name": "type", "in": "path", "description": "类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】", "required": true, "type": "string"}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "type": "string"}, {"name": "page", "in": "query", "required": false, "description": "页码", "type": "string"}, {"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"activity": {"type": "array", "description": "活动区域图片", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "banner": {"type": "array", "description": "首页banner滚动图", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "benefit": {"type": "array", "description": "首页促销单品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "couponList": {"type": "array", "description": "优惠券", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "explosiveMoney": {"type": "array", "description": "首页超值爆款", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "info": {"type": "object", "properties": {"bastBanner": {"type": "array", "description": "首页精品推荐图片", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "bastInfo": {"type": "string", "description": "精品推荐简介"}, "bastList": {"type": "array", "description": "精品推荐", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "bastNumber": {"type": "string", "description": "精品推荐个数"}, "fastInfo": {"type": "string", "description": "快速选择简介"}, "fastList": {"type": "array", "description": "分类", "items": {"type": "object", "properties": {"extra": {"type": "string", "description": "扩展字段"}, "id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "分类名称"}, "path": {"type": "string", "description": "路径"}, "pid": {"type": "integer", "format": "int32", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态, 0正常，1失效"}, "type": {"type": "integer", "format": "int32", "description": "类型ID | 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置 "}, "url": {"type": "string", "description": "地址"}}, "title": "Category对象", "description": "分类表", "$$ref": "#/definitions/Category对象"}}, "fastNumber": {"type": "string", "description": "快速选择分类个数"}, "firstInfo": {"type": "string", "description": "首发新品简介"}, "firstList": {"type": "array", "description": "首发新品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "firstNumber": {"type": "string", "description": "首发新品个数"}, "promotionNumber": {"type": "string", "description": "首页促销单品"}, "salesInfo": {"type": "string", "description": "促销单品简介"}}, "title": "IndexInfoItemResponse对象", "description": "用户登录返回数据", "$$ref": "#/definitions/IndexInfoItemResponse对象"}, "likeInfo": {"type": "array", "description": "热门榜单", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "list": {"type": "array", "description": "商品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "logoUrl": {"type": "string", "description": "企业logo"}, "lovely": {"type": "object", "description": "首发新品广告图", "additionalProperties": {"type": "object"}}, "menus": {"type": "array", "description": "导航模块", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "roll": {"type": "array", "description": "新闻简报消息滚动", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "subscribe": {"type": "boolean", "description": "是否关注"}}, "title": "IndexInfoResponse对象", "description": "用户登录返回数据", "$$ref": "#/definitions/IndexInfoResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«IndexInfoResponse对象»", "$$ref": "#/definitions/CommonResult«IndexInfoResponse对象»"}}}}}, "/api/front/index": {"get": {"tags": ["首页"], "summary": "首页数据", "consumes": ["text/plain"], "parameters": [{"name": "raw", "in": "body", "description": "raw paramter", "schema": {"type": "string", "format": "binary"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object", "properties": {"activity": {"type": "array", "description": "活动区域图片", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "banner": {"type": "array", "description": "首页banner滚动图", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "benefit": {"type": "array", "description": "首页促销单品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "couponList": {"type": "array", "description": "优惠券", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "explosiveMoney": {"type": "array", "description": "首页超值爆款", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "info": {"type": "object", "properties": {"bastBanner": {"type": "array", "description": "首页精品推荐图片", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "bastInfo": {"type": "string", "description": "精品推荐简介"}, "bastList": {"type": "array", "description": "精品推荐", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "bastNumber": {"type": "string", "description": "精品推荐个数"}, "fastInfo": {"type": "string", "description": "快速选择简介"}, "fastList": {"type": "array", "description": "分类", "items": {"type": "object", "properties": {"extra": {"type": "string", "description": "扩展字段"}, "id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "分类名称"}, "path": {"type": "string", "description": "路径"}, "pid": {"type": "integer", "format": "int32", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "boolean", "description": "状态, 0正常，1失效"}, "type": {"type": "integer", "format": "int32", "description": "类型ID | 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置 "}, "url": {"type": "string", "description": "地址"}}, "title": "Category对象", "description": "分类表", "$$ref": "#/definitions/Category对象"}}, "fastNumber": {"type": "string", "description": "快速选择分类个数"}, "firstInfo": {"type": "string", "description": "首发新品简介"}, "firstList": {"type": "array", "description": "首发新品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "firstNumber": {"type": "string", "description": "首发新品个数"}, "promotionNumber": {"type": "string", "description": "首页促销单品"}, "salesInfo": {"type": "string", "description": "促销单品简介"}}, "title": "IndexInfoItemResponse对象", "description": "用户登录返回数据", "$$ref": "#/definitions/IndexInfoItemResponse对象"}, "likeInfo": {"type": "array", "description": "热门榜单", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "list": {"type": "array", "description": "商品", "items": {"type": "object", "properties": {"cateId": {"type": "array", "description": "分类id", "items": {"type": "integer", "format": "int32"}}, "id": {"type": "integer", "format": "int32", "description": "商品id"}, "image": {"type": "string", "description": "商品图片"}, "otPrice": {"type": "number", "description": "市场价"}, "price": {"type": "number", "description": "商品价格"}, "sales": {"type": "integer", "format": "int32", "description": "销量"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "storeName": {"type": "string", "description": "商品名称"}, "unitName": {"type": "string", "description": "单位名"}}, "title": "ProductResponse对象", "description": "商品表", "$$ref": "#/definitions/ProductResponse对象"}}, "logoUrl": {"type": "string", "description": "企业logo"}, "lovely": {"type": "object", "description": "首发新品广告图", "additionalProperties": {"type": "object"}}, "menus": {"type": "array", "description": "导航模块", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "roll": {"type": "array", "description": "新闻简报消息滚动", "items": {"type": "object", "title": "HashMap«string,object»", "additionalProperties": {"type": "object"}, "$$ref": "#/definitions/HashMap«string,object»"}}, "subscribe": {"type": "boolean", "description": "是否关注"}}, "title": "IndexInfoResponse对象", "description": "用户登录返回数据", "$$ref": "#/definitions/IndexInfoResponse对象"}, "message": {"type": "string"}}, "title": "CommonResult«IndexInfoResponse对象»", "$$ref": "#/definitions/CommonResult«IndexInfoResponse对象»"}}}}}}}