### api/live/roomInfo

- method : GET
- req params
  - roomId : 房间id

- resp
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "roomInfo": {
      "id": 1,
      "title": "live title",
      "cover": "cover",
      // 0 未开始，1 直播中 2 已结束
      "status": 1,
      // 开始毫秒时间戳
      "start_time": 1697872000,
      // 结束毫秒时间戳
      "end_time": 1697872000
    },
    "socketUrl": "ws://127.0.0.1:8080",
    // 直播地址
    "liveUrl": "",
    "token": "token"
  }
}
```