[{"index": 0, "name": "上传文件", "desc": "Upload Front Controller", "add_time": 1597369231, "up_time": 1597369231, "list": [{"query_path": {"path": "/api/front/user/upload/image", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7382, "method": "POST", "title": "图片上传", "path": "/api/front/user/upload/image", "req_params": [], "req_body_form": [{"required": "0", "_id": "5f35eb99bc7e8e560914fc7d", "name": "multipart", "desc": "multipart", "type": "file"}], "req_headers": [{"required": "1", "_id": "5f35eb99bc7e8e560914fc7e", "name": "Content-Type", "value": "multipart/form-data"}], "req_query": [{"required": "0", "_id": "5f35eb99bc7e8e560914fc80", "name": "model", "desc": "模块 用户user,商品product,微信wechat,news文章"}, {"required": "0", "_id": "5f35eb99bc7e8e560914fc7f", "name": "pid", "desc": "分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列 "}], "req_body_type": "form", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"extName\": {\n          \"type\": \"string\"\n        },\n        \"fileName\": {\n          \"type\": \"string\"\n        },\n        \"fileSize\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"serverPath\": {\n          \"type\": \"string\"\n        },\n        \"type\": {\n          \"type\": \"string\"\n        },\n        \"url\": {\n          \"type\": \"string\"\n        }\n      },\n      \"title\": \"FileResultVo\",\n      \"$$ref\": \"#/definitions/FileResultVo\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«FileResultVo»\",\n  \"$$ref\": \"#/definitions/CommonResult«FileResultVo»\"\n}", "project_id": 56, "catid": 4892, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}]}, {"index": 0, "name": "二维码服务", "desc": "Qr Code Controller", "add_time": 1597369232, "up_time": 1597369232, "list": [{"query_path": {"path": "/api/front/qrcode/get", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7318, "method": "POST", "title": "获取二维码", "path": "/api/front/qrcode/get", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc4e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«string,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«string,object»»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"object\"\n  }\n}", "project_id": 56, "catid": 4901, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/qrcode/base64", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7316, "method": "POST", "title": "远程图片转base64", "path": "/api/front/qrcode/base64", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc4c", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc4d", "name": "url", "desc": "url"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«string,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«string,object»»\"\n}", "project_id": 56, "catid": 4901, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}]}, {"index": 0, "name": "优惠券", "desc": "Coupon Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/coupons", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7252, "method": "GET", "title": "分页列表", "path": "/api/front/coupons", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb94bc7e8e560914fc20", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb94bc7e8e560914fc1f", "name": "page", "desc": "页码"}, {"required": "0", "_id": "5f35eb94bc7e8e560914fc1e", "name": "productId", "desc": "productId"}, {"required": "0", "_id": "5f35eb94bc7e8e560914fc1d", "name": "type", "desc": "type"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"day\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"天数\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"优惠券表ID\"\n          },\n          \"isDel\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否删除 状态（0：否，1：是）\"\n          },\n          \"isFixedTime\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否固定使用时间, 默认0 否， 1是\"\n          },\n          \"isLimited\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否限量, 默认0 不限量， 1限量\"\n          },\n          \"isUse\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否已领取未使用\"\n          },\n          \"lastTotal\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"剩余数量\"\n          },\n          \"minPrice\": {\n            \"type\": \"number\",\n            \"description\": \"最低消费，0代表不限制\"\n          },\n          \"money\": {\n            \"type\": \"number\",\n            \"description\": \"兑换的优惠券面值\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"优惠券名称\"\n          },\n          \"primaryKey\": {\n            \"type\": \"string\",\n            \"description\": \"所属商品id / 分类id\"\n          },\n          \"receiveEndTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"可领取结束时间\"\n          },\n          \"receiveStartTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"可领取开始时间\"\n          },\n          \"sort\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"排序\"\n          },\n          \"status\": {\n            \"type\": \"boolean\",\n            \"description\": \"状态（0：关闭，1：开启）\"\n          },\n          \"total\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"发放总数\"\n          },\n          \"type\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"优惠券类型 0-通用 1 普通券, 2 新人券, 3 购买商品赠送券, 4 付费会员券\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"更新时间\"\n          },\n          \"useEndTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"可使用时间范围 结束时间\"\n          },\n          \"useStartTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"可使用时间范围 开始时间\"\n          }\n        },\n        \"title\": \"StoreCoupon对象\",\n        \"description\": \"优惠券表\",\n        \"$$ref\": \"#/definitions/StoreCoupon对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«StoreCoupon对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«StoreCoupon对象»»\"\n}", "project_id": 56, "catid": 4910, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/coupons/order", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7254, "method": "GET", "title": "当前购物车可用优惠券", "path": "/api/front/coupons/order", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "1", "_id": "5f35eb94bc7e8e560914fc21", "name": "cartId", "desc": "cartId"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"primaryKey\"\n        ],\n        \"properties\": {\n          \"cid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"兑换的项目id\"\n          },\n          \"couponId\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"优惠券发布id\"\n          },\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"id\"\n          },\n          \"minPrice\": {\n            \"type\": \"number\",\n            \"description\": \"最低消费多少金额可用优惠券\"\n          },\n          \"money\": {\n            \"type\": \"number\",\n            \"description\": \"优惠券的面值\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"优惠券名称\"\n          },\n          \"primaryKey\": {\n            \"type\": \"string\",\n            \"description\": \"主键id 商品id/分类id\"\n          },\n          \"receiveEndTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"过期时间\"\n          },\n          \"receiveStartTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"开始使用时间\"\n          },\n          \"status\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"状态（0：未使用，1：已使用, 2:已失效）\"\n          },\n          \"type\": {\n            \"type\": \"string\",\n            \"description\": \"获取方式\"\n          },\n          \"uid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"领取人id\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"更新时间\"\n          },\n          \"useTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"使用时间\"\n          },\n          \"useType\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"使用类型 1 全场通用, 2 商品券, 3 品类券\"\n          }\n        },\n        \"title\": \"StoreCouponUserOrder对象\",\n        \"description\": \"下单之前可以使用的优惠券对象\",\n        \"$$ref\": \"#/definitions/StoreCouponUserOrder对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«StoreCouponUserOrder对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«StoreCouponUserOrder对象»»\"\n}", "project_id": 56, "catid": 4910, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "商品", "desc": "Product Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/product/hot", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7312, "method": "GET", "title": "为你推荐", "path": "/api/front/product/hot", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb96bc7e8e560914fc44", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc43", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«ProductResponse对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«ProductResponse对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«ProductResponse对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«ProductResponse对象»»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/products", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7314, "method": "GET", "title": "商品列表", "path": "/api/front/products", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb96bc7e8e560914fc4b", "name": "cid", "desc": "分类id"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc4a", "name": "keyword", "desc": "搜索关键字"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc49", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc48", "name": "news", "desc": "是否新品"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc47", "name": "page", "desc": "页码"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc46", "name": "priceOrder", "desc": "价格排序"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc45", "name": "salesOrder", "desc": "销量排序"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«ProductResponse对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«ProductResponse对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«ProductResponse对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«ProductResponse对象»»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/reply/list/{id}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7334, "method": "GET", "title": "商品评论列表", "path": "/api/front/reply/list/{id}", "req_params": [{"_id": "5f35eb97bc7e8e560914fc58", "name": "id", "desc": "id"}], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb97bc7e8e560914fc5b", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb97bc7e8e560914fc5a", "name": "page", "desc": "页码"}, {"required": "0", "_id": "5f35eb97bc7e8e560914fc59", "name": "type", "desc": "评价等级|0=全部,1=好评,2=中评,3=差评"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"avatar\": {\n                \"type\": \"string\",\n                \"description\": \"用户头像\"\n              },\n              \"comment\": {\n                \"type\": \"string\",\n                \"description\": \"评论内容\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"评论时间\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"评论ID\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"0未删除1已删除\"\n              },\n              \"isReply\": {\n                \"type\": \"boolean\",\n                \"description\": \"0未回复1已回复\"\n              },\n              \"merchantReplyContent\": {\n                \"type\": \"string\",\n                \"description\": \"管理员回复内容\"\n              },\n              \"merchantReplyTime\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"管理员回复时间\"\n              },\n              \"nickname\": {\n                \"type\": \"string\",\n                \"description\": \"用户名称\"\n              },\n              \"oid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"订单ID\"\n              },\n              \"pics\": {\n                \"type\": \"array\",\n                \"description\": \"评论图片\",\n                \"items\": {\n                  \"type\": \"string\"\n                }\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"productScore\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品分数\"\n              },\n              \"replyType\": {\n                \"type\": \"string\",\n                \"description\": \"某种商品类型(普通商品、秒杀商品）\"\n              },\n              \"serviceScore\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"服务分数\"\n              },\n              \"storeProduct\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"activity\": {\n                    \"type\": \"string\",\n                    \"description\": \"活动显示排序1=秒杀，2=砍价，3=拼团\"\n                  },\n                  \"addTime\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"添加时间\"\n                  },\n                  \"barCode\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品条码（一维码）\"\n                  },\n                  \"browse\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"浏览量\"\n                  },\n                  \"cateId\": {\n                    \"type\": \"string\",\n                    \"description\": \"分类id\"\n                  },\n                  \"codePath\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品二维码地址(用户小程序海报)\"\n                  },\n                  \"cost\": {\n                    \"type\": \"number\",\n                    \"description\": \"成本价\"\n                  },\n                  \"ficti\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"虚拟销量\"\n                  },\n                  \"giveIntegral\": {\n                    \"type\": \"number\",\n                    \"description\": \"获得积分\"\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"isBargain\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"砍价状态 0未开启 1开启\"\n                  },\n                  \"isBenefit\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否优惠\"\n                  },\n                  \"isBest\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否精品\"\n                  },\n                  \"isDel\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否删除\"\n                  },\n                  \"isGood\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否优品推荐\"\n                  },\n                  \"isHot\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否热卖\"\n                  },\n                  \"isNew\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否新品\"\n                  },\n                  \"isPostage\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否包邮\"\n                  },\n                  \"isSeckill\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"秒杀状态 0 未开启 1已开启\"\n                  },\n                  \"isShow\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"状态（0：未上架，1：上架）\"\n                  },\n                  \"isSub\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否单独分佣\"\n                  },\n                  \"keyword\": {\n                    \"type\": \"string\",\n                    \"description\": \"关键字\"\n                  },\n                  \"merId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n                  },\n                  \"merUse\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"商户是否代理 0不可代理1可代理\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"postage\": {\n                    \"type\": \"number\",\n                    \"description\": \"邮费\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sliderImage\": {\n                    \"type\": \"string\",\n                    \"description\": \"轮播图\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"soureLink\": {\n                    \"type\": \"string\",\n                    \"description\": \"淘宝京东1688类型\"\n                  },\n                  \"specType\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"规格 0单 1多\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeInfo\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品简介\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"tempId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"运费模板ID\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  },\n                  \"videoLink\": {\n                    \"type\": \"string\",\n                    \"description\": \"主图视频链接\"\n                  },\n                  \"vipPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"会员价格\"\n                  }\n                },\n                \"title\": \"StoreProduct对象\",\n                \"description\": \"商品表\",\n                \"$$ref\": \"#/definitions/StoreProduct对象\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户ID\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"更新时间\"\n              }\n            },\n            \"title\": \"StoreProductReplyResponse\",\n            \"$$ref\": \"#/definitions/StoreProductReplyResponse\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«StoreProductReplyResponse»\",\n      \"$$ref\": \"#/definitions/CommonPage«StoreProductReplyResponse»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«StoreProductReplyResponse»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«StoreProductReplyResponse»»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/reply/config/{id}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7332, "method": "GET", "title": "商品评论数量", "path": "/api/front/reply/config/{id}", "req_params": [{"_id": "5f35eb97bc7e8e560914fc57", "name": "id", "desc": "id"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"goodCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\",\n          \"description\": \"好评总数\"\n        },\n        \"inCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\",\n          \"description\": \"中评总数\"\n        },\n        \"poorCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\",\n          \"description\": \"差评总数\"\n        },\n        \"replyChance\": {\n          \"type\": \"string\",\n          \"description\": \"好评率\"\n        },\n        \"replyStar\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"评分星数\"\n        },\n        \"sumCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\",\n          \"description\": \"评论总数\"\n        }\n      },\n      \"title\": \"StoreProductReplayCountResponse对象\",\n      \"description\": \"产品评价数量和好评度\",\n      \"$$ref\": \"#/definitions/StoreProductReplayCountResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«StoreProductReplayCountResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«StoreProductReplayCountResponse对象»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/product/detail/{id}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7310, "method": "GET", "title": "商品详情", "path": "/api/front/product/detail/{id}", "req_params": [{"_id": "5f35eb96bc7e8e560914fc42", "name": "id", "desc": "id"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"activity\": {\n          \"type\": \"array\",\n          \"description\": \"拼团，砍价，秒杀商品集合\",\n          \"items\": {\n            \"type\": \"object\"\n          }\n        },\n        \"base64Image\": {\n          \"type\": \"string\",\n          \"description\": \"主图base64\"\n        },\n        \"goodList\": {\n          \"type\": \"array\",\n          \"description\": \"优品推荐列表\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"activity\": {\n                \"type\": \"array\",\n                \"description\": \"秒杀，团购，砍价\",\n                \"items\": {\n                  \"type\": \"object\"\n                }\n              },\n              \"checkCoupon\": {\n                \"type\": \"boolean\",\n                \"description\": \"可用优惠券\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"img Url\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              }\n            },\n            \"title\": \"StoreProductRecommendResponse\",\n            \"$$ref\": \"#/definitions/StoreProductRecommendResponse\"\n          }\n        },\n        \"priceName\": {\n          \"type\": \"string\",\n          \"description\": \"返佣金额区间\"\n        },\n        \"productAttr\": {\n          \"type\": \"array\",\n          \"description\": \"产品属性\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"productValue\": {\n          \"type\": \"object\",\n          \"description\": \"商品属性详情\",\n          \"additionalProperties\": {\n            \"type\": \"object\"\n          }\n        },\n        \"reply\": {\n          \"type\": \"object\",\n          \"description\": \"最新评价\"\n        },\n        \"replyChance\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"好评率\"\n        },\n        \"replyCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"评价数量\"\n        },\n        \"storeInfo\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"activity\": {\n              \"type\": \"string\",\n              \"description\": \"活动显示排序1=秒杀，2=砍价，3=拼团\"\n            },\n            \"addTime\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"添加时间\"\n            },\n            \"barCode\": {\n              \"type\": \"string\",\n              \"description\": \"商品条码（一维码）\"\n            },\n            \"browse\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"浏览量\"\n            },\n            \"cateId\": {\n              \"type\": \"string\",\n              \"description\": \"分类id\"\n            },\n            \"cateIds\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            },\n            \"cateValues\": {\n              \"type\": \"string\",\n              \"description\": \"分类中文\"\n            },\n            \"codePath\": {\n              \"type\": \"string\",\n              \"description\": \"商品二维码地址(用户小程序海报)\"\n            },\n            \"content\": {\n              \"type\": \"string\",\n              \"description\": \"商品描述\"\n            },\n            \"cost\": {\n              \"type\": \"number\",\n              \"description\": \"成本价\"\n            },\n            \"ficti\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"虚拟销量\"\n            },\n            \"giveIntegral\": {\n              \"type\": \"number\",\n              \"description\": \"获得积分\"\n            },\n            \"id\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"商品id\"\n            },\n            \"image\": {\n              \"type\": \"string\",\n              \"description\": \"商品图片\"\n            },\n            \"isBargain\": {\n              \"type\": \"boolean\",\n              \"description\": \"砍价状态 0未开启 1开启\"\n            },\n            \"isBenefit\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否优惠\"\n            },\n            \"isBest\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否精品\"\n            },\n            \"isDel\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否删除\"\n            },\n            \"isGood\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否优品推荐\"\n            },\n            \"isHot\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否热卖\"\n            },\n            \"isNew\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否新品\"\n            },\n            \"isPostage\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否包邮\"\n            },\n            \"isSeckill\": {\n              \"type\": \"boolean\",\n              \"description\": \"秒杀状态 0 未开启 1已开启\"\n            },\n            \"isShow\": {\n              \"type\": \"boolean\",\n              \"description\": \"状态（0：未上架，1：上架）\"\n            },\n            \"isSub\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否单独分佣\"\n            },\n            \"keyword\": {\n              \"type\": \"string\",\n              \"description\": \"关键字\"\n            },\n            \"merId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n            },\n            \"merUse\": {\n              \"type\": \"boolean\",\n              \"description\": \"商户是否代理 0不可代理1可代理\"\n            },\n            \"otPrice\": {\n              \"type\": \"number\",\n              \"description\": \"市场价\"\n            },\n            \"postage\": {\n              \"type\": \"number\",\n              \"description\": \"邮费\"\n            },\n            \"price\": {\n              \"type\": \"number\",\n              \"description\": \"商品价格\"\n            },\n            \"sales\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"销量\"\n            },\n            \"sliderImage\": {\n              \"type\": \"string\",\n              \"description\": \"轮播图\"\n            },\n            \"sort\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"排序\"\n            },\n            \"soureLink\": {\n              \"type\": \"string\",\n              \"description\": \"淘宝京东1688类型\"\n            },\n            \"specType\": {\n              \"type\": \"boolean\",\n              \"description\": \"规格 0单 1多\"\n            },\n            \"stock\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"库存\"\n            },\n            \"storeInfo\": {\n              \"type\": \"string\",\n              \"description\": \"商品简介\"\n            },\n            \"storeName\": {\n              \"type\": \"string\",\n              \"description\": \"商品名称\"\n            },\n            \"tempId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"运费模板ID\"\n            },\n            \"unitName\": {\n              \"type\": \"string\",\n              \"description\": \"单位名\"\n            },\n            \"userCollect\": {\n              \"type\": \"boolean\",\n              \"description\": \"收藏标识\"\n            },\n            \"userLike\": {\n              \"type\": \"boolean\",\n              \"description\": \"点赞标识\"\n            },\n            \"videoLink\": {\n              \"type\": \"string\",\n              \"description\": \"主图视频链接\"\n            },\n            \"vipPrice\": {\n              \"type\": \"number\",\n              \"description\": \"会员价格\"\n            }\n          },\n          \"title\": \"StoreProductStoreInfoResponse对象\",\n          \"description\": \"商品表\",\n          \"$$ref\": \"#/definitions/StoreProductStoreInfoResponse对象\"\n        }\n      },\n      \"title\": \"ProductDetailResponse对象\",\n      \"description\": \"商品详情H5\",\n      \"$$ref\": \"#/definitions/ProductDetailResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«ProductDetailResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«ProductDetailResponse对象»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/category", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7232, "method": "GET", "title": "获取分类", "path": "/api/front/category", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"child\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"originalRef\": \"CategoryTreeVo\",\n              \"$ref\": \"#/definitions/CategoryTreeVo\"\n            }\n          },\n          \"extra\": {\n            \"type\": \"string\",\n            \"description\": \"扩展字段\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"分类名称\"\n          },\n          \"path\": {\n            \"type\": \"string\",\n            \"description\": \"路径\"\n          },\n          \"pid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"父级ID\"\n          },\n          \"sort\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"排序\"\n          },\n          \"status\": {\n            \"type\": \"boolean\",\n            \"description\": \"状态, 0正常，1失效\"\n          },\n          \"type\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置\"\n          },\n          \"url\": {\n            \"type\": \"string\",\n            \"description\": \"地址\"\n          }\n        },\n        \"title\": \"CategoryTreeVo\",\n        \"$$ref\": \"#/definitions/CategoryTreeVo\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«CategoryTreeVo»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«CategoryTreeVo»»\"\n}", "project_id": 56, "catid": 4919, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}]}, {"index": 0, "name": "商品 -- 购物车", "desc": "Cart Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/cart/num", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7226, "method": "POST", "title": "修改", "path": "/api/front/cart/num", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc0d", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc0f", "name": "id", "desc": "id"}, {"required": "1", "_id": "5f35eb93bc7e8e560914fc0e", "name": "number", "desc": "number"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/cart/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7224, "method": "GET", "title": "分页列表", "path": "/api/front/cart/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc0c", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON>"}, {"required": "0", "_id": "5f35eb93bc7e8e560914fc0b", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb93bc7e8e560914fc0a", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"addTime\": {\n                \"type\": \"string\"\n              },\n              \"attrStatus\": {\n                \"type\": \"boolean\",\n                \"description\": \"商品是否有效\"\n              },\n              \"bargainId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"砍价id\"\n              },\n              \"brokerage\": {\n                \"type\": \"number\",\n                \"description\": \"一级分佣\"\n              },\n              \"brokerageTwo\": {\n                \"type\": \"number\",\n                \"description\": \"二级分佣\"\n              },\n              \"cartNum\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品数量\"\n              },\n              \"combinationId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"拼团id\"\n              },\n              \"costPrice\": {\n                \"type\": \"number\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int64\",\n                \"description\": \"购物车表ID\"\n              },\n              \"isNew\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否为立即购买\"\n              },\n              \"isReply\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"productAttrUnique\": {\n                \"type\": \"string\",\n                \"description\": \"商品属性\"\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品ID\"\n              },\n              \"productInfo\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"attrInfo\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"attrValue\": {\n                        \"type\": \"string\",\n                        \"description\": \"产品属性值和属性名对应关系\"\n                      },\n                      \"barCode\": {\n                        \"type\": \"string\",\n                        \"description\": \"商品条码\"\n                      },\n                      \"brokerage\": {\n                        \"type\": \"number\",\n                        \"description\": \"一级返佣\"\n                      },\n                      \"brokerageTwo\": {\n                        \"type\": \"number\",\n                        \"description\": \"二级返佣\"\n                      },\n                      \"cost\": {\n                        \"type\": \"number\",\n                        \"description\": \"成本价\"\n                      },\n                      \"id\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"attrId\"\n                      },\n                      \"image\": {\n                        \"type\": \"string\",\n                        \"description\": \"图片\"\n                      },\n                      \"otPrice\": {\n                        \"type\": \"number\",\n                        \"description\": \"原价\"\n                      },\n                      \"price\": {\n                        \"type\": \"number\",\n                        \"description\": \"属性金额\"\n                      },\n                      \"productId\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"商品ID\"\n                      },\n                      \"quota\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动限购数量\"\n                      },\n                      \"quotaShow\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动限购数量显示\"\n                      },\n                      \"sales\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"销量\"\n                      },\n                      \"stock\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"属性对应的库存\"\n                      },\n                      \"suk\": {\n                        \"type\": \"string\",\n                        \"description\": \"商品属性索引值 (attr_value|attr_value[|....])\"\n                      },\n                      \"type\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动类型 0=商品，1=秒杀，2=砍价，3=拼团\"\n                      },\n                      \"unique\": {\n                        \"type\": \"string\",\n                        \"description\": \"唯一值\"\n                      },\n                      \"volume\": {\n                        \"type\": \"number\",\n                        \"description\": \"体积\"\n                      },\n                      \"weight\": {\n                        \"type\": \"number\",\n                        \"description\": \"重量\"\n                      }\n                    },\n                    \"title\": \"StoreProductAttrValue对象\",\n                    \"description\": \"商品属性值表\",\n                    \"$$ref\": \"#/definitions/StoreProductAttrValue对象\"\n                  },\n                  \"barCode\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品条码（一维码）\"\n                  },\n                  \"cateId\": {\n                    \"type\": \"string\",\n                    \"description\": \"分类id\"\n                  },\n                  \"cost\": {\n                    \"type\": \"number\",\n                    \"description\": \"成本价\"\n                  },\n                  \"giveIntegral\": {\n                    \"type\": \"number\",\n                    \"description\": \"获得积分\"\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"isPostage\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否包邮\"\n                  },\n                  \"isSub\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否单独分佣\"\n                  },\n                  \"keyword\": {\n                    \"type\": \"string\",\n                    \"description\": \"关键字\"\n                  },\n                  \"merId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"postage\": {\n                    \"type\": \"number\",\n                    \"description\": \"邮费\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sliderImage\": {\n                    \"type\": \"string\",\n                    \"description\": \"轮播图\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeInfo\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品简介\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"tempId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"运费模板ID\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  },\n                  \"vipPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"会员价格\"\n                  }\n                },\n                \"title\": \"StoreProductCartProductInfoResponse对象\",\n                \"description\": \"商品信息，购物车列表使用\",\n                \"$$ref\": \"#/definitions/StoreProductCartProductInfoResponse对象\"\n              },\n              \"seckillId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"秒杀商品ID\"\n              },\n              \"truePrice\": {\n                \"type\": \"number\"\n              },\n              \"trueStock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"type\": {\n                \"type\": \"string\",\n                \"description\": \"类型\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户ID\"\n              },\n              \"vipTruePrice\": {\n                \"type\": \"number\"\n              }\n            },\n            \"title\": \"StoreCartResponse\",\n            \"description\": \"购物车ListResponse\",\n            \"$$ref\": \"#/definitions/StoreCartResponse\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«StoreCartResponse»\",\n      \"$$ref\": \"#/definitions/CommonPage«StoreCartResponse»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«StoreCartResponse»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«StoreCartResponse»»\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/cart/delete", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7222, "method": "POST", "title": "删除", "path": "/api/front/cart/delete", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc08", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc09", "name": "ids", "desc": "ids"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/cart/count", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7220, "method": "GET", "title": "数量", "path": "/api/front/cart/count", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb92bc7e8e560914fc07", "name": "numType", "desc": "numType"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«object,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«object,object»»\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/cart/save", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7230, "method": "POST", "title": "新增", "path": "/api/front/cart/save", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc11", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc15", "name": "cartNum", "desc": "商品数量"}, {"required": "1", "_id": "5f35eb93bc7e8e560914fc14", "name": "isNew", "desc": "是否为立即购买"}, {"required": "0", "_id": "5f35eb93bc7e8e560914fc13", "name": "productAttrUnique", "desc": "商品属性 -- attr 对象的id"}, {"required": "1", "_id": "5f35eb93bc7e8e560914fc12", "name": "productId", "desc": "商品ID"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«HashMap«string,string»»\",\n  \"$$ref\": \"#/definitions/CommonResult«HashMap«string,string»»\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/cart/resetcart", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7228, "method": "POST", "title": "购物车重选提交", "path": "/api/front/cart/resetcart", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc10", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\",\n      \"description\": \"购物车id\"\n    },\n    \"num\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"购物车数量\"\n    },\n    \"productId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"商品id\"\n    },\n    \"unique\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"AttrValue Id\"\n    }\n  },\n  \"title\": \"CartResetRequest\",\n  \"$$ref\": \"#/definitions/CartResetRequest\"\n}", "project_id": 56, "catid": 4928, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}]}, {"index": 0, "name": "城市服务", "desc": "City Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/city/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7234, "method": "GET", "title": "树形结构", "path": "/api/front/city/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 4937, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}]}, {"index": 0, "name": "客服", "desc": "Store Service Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/user/service/lst", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7364, "method": "GET", "title": "客服列表", "path": "/api/front/user/service/lst", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"avatar\": {\n            \"type\": \"string\",\n            \"description\": \"客服头像\"\n          },\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"添加时间\"\n          },\n          \"customer\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否展示统计管理\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"客服id\"\n          },\n          \"nickname\": {\n            \"type\": \"string\",\n            \"description\": \"代理名称\"\n          },\n          \"notify\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"订单通知1开启0关闭\"\n          },\n          \"status\": {\n            \"type\": \"boolean\",\n            \"description\": \"0隐藏1显示\"\n          },\n          \"uid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"客服uid\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"更新时间\"\n          }\n        },\n        \"title\": \"StoreService对象\",\n        \"description\": \"客服表\",\n        \"$$ref\": \"#/definitions/StoreService对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«StoreService对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«StoreService对象»»\"\n}", "project_id": 56, "catid": 4946, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/service/record/{toUid}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7366, "method": "GET", "title": "聊天记录", "path": "/api/front/user/service/record/{toUid}", "req_params": [{"_id": "5f35eb98bc7e8e560914fc73", "name": "toUid", "desc": "聊天人编号"}], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc75", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc74", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"添加时间\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"客服用户对话记录表ID\"\n              },\n              \"msn\": {\n                \"type\": \"string\",\n                \"description\": \"消息内容\"\n              },\n              \"msnType\": {\n                \"type\": \"boolean\",\n                \"description\": \"消息类型 1=文字 2=表情 3=图片 4=语音\"\n              },\n              \"remind\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否提醒过\"\n              },\n              \"toUid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"接收人uid\"\n              },\n              \"type\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否已读（0：否；1：是；）\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"发送人uid\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"更新时间\"\n              }\n            },\n            \"title\": \"StoreServiceLog对象\",\n            \"description\": \"客服用户对话记录表\",\n            \"$$ref\": \"#/definitions/StoreServiceLog对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«StoreServiceLog对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«StoreServiceLog对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«StoreServiceLog对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«StoreServiceLog对象»»\"\n}", "project_id": 56, "catid": 4946, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}]}, {"index": 0, "name": "微信 -- 开放平台", "desc": "We Chat Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/wechat/getLogo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7394, "method": "GET", "title": "小程序获取授权logo", "path": "/api/front/wechat/getLogo", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«string,string»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«string,string»»\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/wechat/authorize/login", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7388, "method": "GET", "title": "微信登录公共号授权登录", "path": "/api/front/wechat/authorize/login", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "1", "_id": "5f35eb99bc7e8e560914fc81", "name": "code", "desc": "code"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"expiresTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"用户登录密钥到期时间\"\n        },\n        \"token\": {\n          \"type\": \"string\",\n          \"description\": \"用户登录密钥\"\n        },\n        \"user\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"account\": {\n              \"type\": \"string\",\n              \"description\": \"用户账号\"\n            },\n            \"addIp\": {\n              \"type\": \"string\",\n              \"description\": \"添加ip\"\n            },\n            \"addres\": {\n              \"type\": \"string\",\n              \"description\": \"详细地址\"\n            },\n            \"adminid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"管理员编号 \"\n            },\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"birthday\": {\n              \"type\": \"string\",\n              \"description\": \"生日\"\n            },\n            \"brokeragePrice\": {\n              \"type\": \"number\",\n              \"description\": \"佣金金额\"\n            },\n            \"cardId\": {\n              \"type\": \"string\",\n              \"description\": \"身份证号码\"\n            },\n            \"cleanTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"experience\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户剩余经验\"\n            },\n            \"groupId\": {\n              \"type\": \"string\",\n              \"description\": \"用户分组id\"\n            },\n            \"integral\": {\n              \"type\": \"number\",\n              \"description\": \"用户剩余积分\"\n            },\n            \"isPromoter\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否为推广员\"\n            },\n            \"lastIp\": {\n              \"type\": \"string\",\n              \"description\": \"最后一次登录ip\"\n            },\n            \"lastLoginTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"level\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"等级\"\n            },\n            \"loginType\": {\n              \"type\": \"string\",\n              \"description\": \"用户登陆类型，h5,wechat,routine\"\n            },\n            \"mark\": {\n              \"type\": \"string\",\n              \"description\": \"用户备注\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"nowMoney\": {\n              \"type\": \"number\",\n              \"description\": \"用户余额\"\n            },\n            \"partnerId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"合伙人id\"\n            },\n            \"path\": {\n              \"type\": \"string\",\n              \"description\": \"用户推广等级\"\n            },\n            \"payCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户购买次数\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"手机号码\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"真实姓名\"\n            },\n            \"signNum\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"连续签到天数\"\n            },\n            \"spreadCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"下级人数\"\n            },\n            \"spreadTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"推广员关联时间\"\n            },\n            \"spreadUid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"推广人id\"\n            },\n            \"status\": {\n              \"type\": \"boolean\",\n              \"description\": \"1为正常，0为禁止\"\n            },\n            \"subscribe\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否关注公众号\"\n            },\n            \"tagId\": {\n              \"type\": \"string\",\n              \"description\": \"用户标签id\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"userType\": {\n              \"type\": \"string\",\n              \"description\": \"用户类型\"\n            }\n          },\n          \"title\": \"User对象\",\n          \"description\": \"用户表\",\n          \"$$ref\": \"#/definitions/User对象\"\n        }\n      },\n      \"title\": \"LoginResponse\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/LoginResponse\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«LoginResponse»\",\n  \"$$ref\": \"#/definitions/CommonResult«LoginResponse»\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/wechat/authorize/program/login", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7390, "method": "POST", "title": "微信登录小程序授权登录", "path": "/api/front/wechat/authorize/program/login", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb99bc7e8e560914fc82", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb99bc7e8e560914fc83", "name": "code", "desc": "code"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"expiresTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"用户登录密钥到期时间\"\n        },\n        \"token\": {\n          \"type\": \"string\",\n          \"description\": \"用户登录密钥\"\n        },\n        \"user\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"account\": {\n              \"type\": \"string\",\n              \"description\": \"用户账号\"\n            },\n            \"addIp\": {\n              \"type\": \"string\",\n              \"description\": \"添加ip\"\n            },\n            \"addres\": {\n              \"type\": \"string\",\n              \"description\": \"详细地址\"\n            },\n            \"adminid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"管理员编号 \"\n            },\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"birthday\": {\n              \"type\": \"string\",\n              \"description\": \"生日\"\n            },\n            \"brokeragePrice\": {\n              \"type\": \"number\",\n              \"description\": \"佣金金额\"\n            },\n            \"cardId\": {\n              \"type\": \"string\",\n              \"description\": \"身份证号码\"\n            },\n            \"cleanTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"experience\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户剩余经验\"\n            },\n            \"groupId\": {\n              \"type\": \"string\",\n              \"description\": \"用户分组id\"\n            },\n            \"integral\": {\n              \"type\": \"number\",\n              \"description\": \"用户剩余积分\"\n            },\n            \"isPromoter\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否为推广员\"\n            },\n            \"lastIp\": {\n              \"type\": \"string\",\n              \"description\": \"最后一次登录ip\"\n            },\n            \"lastLoginTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"level\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"等级\"\n            },\n            \"loginType\": {\n              \"type\": \"string\",\n              \"description\": \"用户登陆类型，h5,wechat,routine\"\n            },\n            \"mark\": {\n              \"type\": \"string\",\n              \"description\": \"用户备注\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"nowMoney\": {\n              \"type\": \"number\",\n              \"description\": \"用户余额\"\n            },\n            \"partnerId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"合伙人id\"\n            },\n            \"path\": {\n              \"type\": \"string\",\n              \"description\": \"用户推广等级\"\n            },\n            \"payCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户购买次数\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"手机号码\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"真实姓名\"\n            },\n            \"signNum\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"连续签到天数\"\n            },\n            \"spreadCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"下级人数\"\n            },\n            \"spreadTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"推广员关联时间\"\n            },\n            \"spreadUid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"推广人id\"\n            },\n            \"status\": {\n              \"type\": \"boolean\",\n              \"description\": \"1为正常，0为禁止\"\n            },\n            \"subscribe\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否关注公众号\"\n            },\n            \"tagId\": {\n              \"type\": \"string\",\n              \"description\": \"用户标签id\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"userType\": {\n              \"type\": \"string\",\n              \"description\": \"用户类型\"\n            }\n          },\n          \"title\": \"User对象\",\n          \"description\": \"用户表\",\n          \"$$ref\": \"#/definitions/User对象\"\n        }\n      },\n      \"title\": \"LoginResponse\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/LoginResponse\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«LoginResponse»\",\n  \"$$ref\": \"#/definitions/CommonResult«LoginResponse»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"avatar\",\n    \"nickName\",\n    \"sex\",\n    \"spread_spid\"\n  ],\n  \"properties\": {\n    \"avatar\": {\n      \"type\": \"string\",\n      \"description\": \"用户头像\"\n    },\n    \"city\": {\n      \"type\": \"string\",\n      \"description\": \"普通用户个人资料填写的城市\"\n    },\n    \"country\": {\n      \"type\": \"string\",\n      \"description\": \"国家，如中国为CN\"\n    },\n    \"nickName\": {\n      \"type\": \"string\",\n      \"description\": \"用户昵称\"\n    },\n    \"province\": {\n      \"type\": \"string\",\n      \"description\": \"用户个人资料填写的省份\"\n    },\n    \"sex\": {\n      \"type\": \"string\",\n      \"description\": \"性别\"\n    },\n    \"spread_spid\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"推广人id\"\n    }\n  },\n  \"title\": \"RegisterThirdUserRequest对象\",\n  \"description\": \"三方用户注册对象\",\n  \"$$ref\": \"#/definitions/RegisterThirdUserRequest对象\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/wechat/config", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7392, "method": "GET", "title": "获取微信公众号js配置", "path": "/api/front/wechat/config", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb99bc7e8e560914fc84", "name": "url", "desc": "页面地址url"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/wechat/authorize/get", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7386, "method": "GET", "title": "获取授权页面跳转地址", "path": "/api/front/wechat/authorize/get", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/wechat/info/{id}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7396, "method": "GET", "title": "详情", "path": "/api/front/wechat/info/{id}", "req_params": [{"_id": "5f35eb99bc7e8e560914fc85", "name": "id", "desc": "id"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"content\": {\n          \"type\": \"string\",\n          \"description\": \"回复内容\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"添加时间\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"模板id\"\n        },\n        \"name\": {\n          \"type\": \"string\",\n          \"description\": \"模板名\"\n        },\n        \"status\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"状态\"\n        },\n        \"tempId\": {\n          \"type\": \"string\",\n          \"description\": \"模板ID\"\n        },\n        \"tempKey\": {\n          \"type\": \"string\",\n          \"description\": \"模板编号\"\n        },\n        \"type\": {\n          \"type\": \"boolean\",\n          \"description\": \"0=订阅消息,1=微信模板消息\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"更新时间\"\n        }\n      },\n      \"title\": \"TemplateMessage对象\",\n      \"description\": \"微信模板\",\n      \"$$ref\": \"#/definitions/TemplateMessage对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«TemplateMessage对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«TemplateMessage对象»\"\n}", "project_id": 56, "catid": 4955, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}]}, {"index": 0, "name": "提货点", "desc": "Store Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/store/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7350, "method": "POST", "title": "附近的提货点", "path": "/api/front/store/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb98bc7e8e560914fc6a", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc6e", "name": "latitude", "desc": "经度"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc6d", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc6c", "name": "longitude", "desc": "纬度"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc6b", "name": "page", "desc": "页码"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"required\": [\n        \"list\"\n      ],\n      \"properties\": {\n        \"list\": {\n          \"type\": \"array\",\n          \"description\": \"附近的门店列表\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"address\": {\n                \"type\": \"string\",\n                \"description\": \"省市区\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"dayTime\": {\n                \"type\": \"string\",\n                \"description\": \"每日营业开关时间\"\n              },\n              \"detailedAddress\": {\n                \"type\": \"string\",\n                \"description\": \"详细地址\"\n              },\n              \"distance\": {\n                \"type\": \"string\",\n                \"description\": \"距离，单位米\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"门店logo\"\n              },\n              \"introduction\": {\n                \"type\": \"string\",\n                \"description\": \"简介\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否删除\"\n              },\n              \"isShow\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否显示\"\n              },\n              \"latitude\": {\n                \"type\": \"string\",\n                \"description\": \"纬度\"\n              },\n              \"longitude\": {\n                \"type\": \"string\",\n                \"description\": \"经度\"\n              },\n              \"name\": {\n                \"type\": \"string\",\n                \"description\": \"门店名称\"\n              },\n              \"phone\": {\n                \"type\": \"string\",\n                \"description\": \"手机号码\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"修改时间\"\n              },\n              \"validTime\": {\n                \"type\": \"string\",\n                \"description\": \"核销有效日期\"\n              }\n            },\n            \"title\": \"SystemStoreNearVo对象\",\n            \"description\": \"门店自提\",\n            \"$$ref\": \"#/definitions/SystemStoreNearVo对象\"\n          }\n        },\n        \"tengXunMapKey\": {\n          \"type\": \"string\",\n          \"description\": \"腾讯地图key\"\n        }\n      },\n      \"title\": \"StoreNearRequest对象\",\n      \"description\": \"附近的门店\",\n      \"$$ref\": \"#/definitions/StoreNearRequest对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«StoreNearRequest对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«StoreNearRequest对象»\"\n}", "project_id": 56, "catid": 4964, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}]}, {"index": 0, "name": "文章", "desc": "Article Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/article/category/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7208, "method": "GET", "title": "分类列表", "path": "/api/front/article/category/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"child\": {\n                \"type\": \"array\",\n                \"items\": {\n                  \"originalRef\": \"CategoryTreeVo\",\n                  \"$ref\": \"#/definitions/CategoryTreeVo\"\n                }\n              },\n              \"extra\": {\n                \"type\": \"string\",\n                \"description\": \"扩展字段\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"name\": {\n                \"type\": \"string\",\n                \"description\": \"分类名称\"\n              },\n              \"path\": {\n                \"type\": \"string\",\n                \"description\": \"路径\"\n              },\n              \"pid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"父级ID\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"status\": {\n                \"type\": \"boolean\",\n                \"description\": \"状态, 0正常，1失效\"\n              },\n              \"type\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置\"\n              },\n              \"url\": {\n                \"type\": \"string\",\n                \"description\": \"地址\"\n              }\n            },\n            \"title\": \"CategoryTreeVo\",\n            \"$$ref\": \"#/definitions/CategoryTreeVo\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«CategoryTreeVo»\",\n      \"$$ref\": \"#/definitions/CommonPage«CategoryTreeVo»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«CategoryTreeVo»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«CategoryTreeVo»»\"\n}", "project_id": 56, "catid": 4973, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/article/list/{cid}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7214, "method": "GET", "title": "分页列表", "path": "/api/front/article/list/{cid}", "req_params": [{"_id": "5f35eb92bc7e8e560914fc00", "name": "cid", "desc": "cid"}], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb92bc7e8e560914fc02", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb92bc7e8e560914fc01", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"adminId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"管理员id\"\n              },\n              \"author\": {\n                \"type\": \"string\",\n                \"description\": \"文章作者\"\n              },\n              \"categoryName\": {\n                \"type\": \"string\",\n                \"description\": \"分类\"\n              },\n              \"cid\": {\n                \"type\": \"string\",\n                \"description\": \"分类id\"\n              },\n              \"content\": {\n                \"type\": \"string\",\n                \"description\": \"文章内容\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"hide\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否隐藏\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"文章管理ID\"\n              },\n              \"imageInput\": {\n                \"type\": \"array\",\n                \"description\": \"文章图片 前端用\",\n                \"items\": {\n                  \"type\": \"string\"\n                }\n              },\n              \"imageInputs\": {\n                \"type\": \"string\",\n                \"description\": \"文章图片 后端用\"\n              },\n              \"isBanner\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否轮播图(小程序)\"\n              },\n              \"isHot\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否热门(小程序)\"\n              },\n              \"mediaId\": {\n                \"type\": \"string\",\n                \"description\": \"微信素材媒体id\"\n              },\n              \"merId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商户id\"\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品关联id\"\n              },\n              \"shareSynopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享简介\"\n              },\n              \"shareTitle\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享标题\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"status\": {\n                \"type\": \"boolean\",\n                \"description\": \"状态\"\n              },\n              \"synopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章简介\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"文章标题\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"更新时间\"\n              },\n              \"url\": {\n                \"type\": \"string\",\n                \"description\": \"原文链接\"\n              },\n              \"visit\": {\n                \"type\": \"string\",\n                \"description\": \"浏览次数\"\n              }\n            },\n            \"title\": \"ArticleVo对象\",\n            \"description\": \"文章管理表\",\n            \"$$ref\": \"#/definitions/ArticleVo对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«ArticleVo对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«ArticleVo对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«ArticleVo对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«ArticleVo对象»»\"\n}", "project_id": 56, "catid": 4973, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/article/hot/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7210, "method": "GET", "title": "热门列表", "path": "/api/front/article/hot/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"adminId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"管理员id\"\n              },\n              \"author\": {\n                \"type\": \"string\",\n                \"description\": \"文章作者\"\n              },\n              \"categoryName\": {\n                \"type\": \"string\",\n                \"description\": \"分类\"\n              },\n              \"cid\": {\n                \"type\": \"string\",\n                \"description\": \"分类id\"\n              },\n              \"content\": {\n                \"type\": \"string\",\n                \"description\": \"文章内容\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"hide\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否隐藏\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"文章管理ID\"\n              },\n              \"imageInput\": {\n                \"type\": \"array\",\n                \"description\": \"文章图片 前端用\",\n                \"items\": {\n                  \"type\": \"string\"\n                }\n              },\n              \"imageInputs\": {\n                \"type\": \"string\",\n                \"description\": \"文章图片 后端用\"\n              },\n              \"isBanner\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否轮播图(小程序)\"\n              },\n              \"isHot\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否热门(小程序)\"\n              },\n              \"mediaId\": {\n                \"type\": \"string\",\n                \"description\": \"微信素材媒体id\"\n              },\n              \"merId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商户id\"\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品关联id\"\n              },\n              \"shareSynopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享简介\"\n              },\n              \"shareTitle\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享标题\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"status\": {\n                \"type\": \"boolean\",\n                \"description\": \"状态\"\n              },\n              \"synopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章简介\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"文章标题\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"更新时间\"\n              },\n              \"url\": {\n                \"type\": \"string\",\n                \"description\": \"原文链接\"\n              },\n              \"visit\": {\n                \"type\": \"string\",\n                \"description\": \"浏览次数\"\n              }\n            },\n            \"title\": \"ArticleVo对象\",\n            \"description\": \"文章管理表\",\n            \"$$ref\": \"#/definitions/ArticleVo对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«ArticleVo对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«ArticleVo对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«ArticleVo对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«ArticleVo对象»»\"\n}", "project_id": 56, "catid": 4973, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/article/info", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7212, "method": "GET", "title": "详情", "path": "/api/front/article/info", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb92bc7e8e560914fbff", "name": "id", "desc": "文章ID"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"adminId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"管理员id\"\n        },\n        \"author\": {\n          \"type\": \"string\",\n          \"description\": \"文章作者\"\n        },\n        \"categoryName\": {\n          \"type\": \"string\",\n          \"description\": \"分类\"\n        },\n        \"cid\": {\n          \"type\": \"string\",\n          \"description\": \"分类id\"\n        },\n        \"content\": {\n          \"type\": \"string\",\n          \"description\": \"文章内容\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"hide\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否隐藏\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"文章管理ID\"\n        },\n        \"imageInput\": {\n          \"type\": \"array\",\n          \"description\": \"文章图片 前端用\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"imageInputs\": {\n          \"type\": \"string\",\n          \"description\": \"文章图片 后端用\"\n        },\n        \"isBanner\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否轮播图(小程序)\"\n        },\n        \"isHot\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否热门(小程序)\"\n        },\n        \"mediaId\": {\n          \"type\": \"string\",\n          \"description\": \"微信素材媒体id\"\n        },\n        \"merId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"商户id\"\n        },\n        \"productId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"商品关联id\"\n        },\n        \"shareSynopsis\": {\n          \"type\": \"string\",\n          \"description\": \"文章分享简介\"\n        },\n        \"shareTitle\": {\n          \"type\": \"string\",\n          \"description\": \"文章分享标题\"\n        },\n        \"sort\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"排序\"\n        },\n        \"status\": {\n          \"type\": \"boolean\",\n          \"description\": \"状态\"\n        },\n        \"synopsis\": {\n          \"type\": \"string\",\n          \"description\": \"文章简介\"\n        },\n        \"title\": {\n          \"type\": \"string\",\n          \"description\": \"文章标题\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"更新时间\"\n        },\n        \"url\": {\n          \"type\": \"string\",\n          \"description\": \"原文链接\"\n        },\n        \"visit\": {\n          \"type\": \"string\",\n          \"description\": \"浏览次数\"\n        }\n      },\n      \"title\": \"ArticleVo对象\",\n      \"description\": \"文章管理表\",\n      \"$$ref\": \"#/definitions/ArticleVo对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«ArticleVo对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«ArticleVo对象»\"\n}", "project_id": 56, "catid": 4973, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/article/banner/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7206, "method": "GET", "title": "轮播列表", "path": "/api/front/article/banner/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"adminId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"管理员id\"\n              },\n              \"author\": {\n                \"type\": \"string\",\n                \"description\": \"文章作者\"\n              },\n              \"categoryName\": {\n                \"type\": \"string\",\n                \"description\": \"分类\"\n              },\n              \"cid\": {\n                \"type\": \"string\",\n                \"description\": \"分类id\"\n              },\n              \"content\": {\n                \"type\": \"string\",\n                \"description\": \"文章内容\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"hide\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否隐藏\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"文章管理ID\"\n              },\n              \"imageInput\": {\n                \"type\": \"array\",\n                \"description\": \"文章图片 前端用\",\n                \"items\": {\n                  \"type\": \"string\"\n                }\n              },\n              \"imageInputs\": {\n                \"type\": \"string\",\n                \"description\": \"文章图片 后端用\"\n              },\n              \"isBanner\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否轮播图(小程序)\"\n              },\n              \"isHot\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否热门(小程序)\"\n              },\n              \"mediaId\": {\n                \"type\": \"string\",\n                \"description\": \"微信素材媒体id\"\n              },\n              \"merId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商户id\"\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品关联id\"\n              },\n              \"shareSynopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享简介\"\n              },\n              \"shareTitle\": {\n                \"type\": \"string\",\n                \"description\": \"文章分享标题\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"status\": {\n                \"type\": \"boolean\",\n                \"description\": \"状态\"\n              },\n              \"synopsis\": {\n                \"type\": \"string\",\n                \"description\": \"文章简介\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"文章标题\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"更新时间\"\n              },\n              \"url\": {\n                \"type\": \"string\",\n                \"description\": \"原文链接\"\n              },\n              \"visit\": {\n                \"type\": \"string\",\n                \"description\": \"浏览次数\"\n              }\n            },\n            \"title\": \"ArticleVo对象\",\n            \"description\": \"文章管理表\",\n            \"$$ref\": \"#/definitions/ArticleVo对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«ArticleVo对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«ArticleVo对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«ArticleVo对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«ArticleVo对象»»\"\n}", "project_id": 56, "catid": 4973, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "物流公司", "desc": "Express Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/logistics", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7270, "method": "GET", "title": "列表", "path": "/api/front/logistics", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"code\": {\n                \"type\": \"string\",\n                \"description\": \"快递公司简称\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"快递公司id\"\n              },\n              \"isShow\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否显示\"\n              },\n              \"name\": {\n                \"type\": \"string\",\n                \"description\": \"快递公司全称\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              }\n            },\n            \"title\": \"Express对象\",\n            \"description\": \"快递公司表\",\n            \"$$ref\": \"#/definitions/Express对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«Express对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«Express对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«Express对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«Express对象»»\"\n}", "project_id": 56, "catid": 4982, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "用户 -- 充值", "desc": "User Recharge Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/recharge/transferIn", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7326, "method": "POST", "title": "余额转入", "path": "/api/front/recharge/transferIn", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc53", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc54", "name": "price", "desc": "price"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "project_id": 56, "catid": 4991, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/recharge/index", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7322, "method": "GET", "title": "充值额度选择", "path": "/api/front/recharge/index", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"giveMoney\": {\n          \"type\": \"string\",\n          \"description\": \"赠送金额\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"充值模板id\"\n        },\n        \"price\": {\n          \"type\": \"string\",\n          \"description\": \"充值金额\"\n        }\n      },\n      \"title\": \"UserRechargeResponse对象\",\n      \"description\": \"c\",\n      \"$$ref\": \"#/definitions/UserRechargeResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserRechargeResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserRechargeResponse对象»\"\n}", "project_id": 56, "catid": 4991, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/recharge/wechat", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7328, "method": "POST", "title": "公众号充值", "path": "/api/front/recharge/wechat", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc55", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"appId\": {\n          \"type\": \"string\",\n          \"description\": \"调用接口提交的公众账号ID\"\n        },\n        \"h5PayUrl\": {\n          \"type\": \"string\",\n          \"description\": \"H5支付的跳转url\"\n        },\n        \"nonceStr\": {\n          \"type\": \"string\",\n          \"description\": \"随机字符串\"\n        },\n        \"package\": {\n          \"type\": \"string\",\n          \"description\": \"订单详情扩展字符串\"\n        },\n        \"paySign\": {\n          \"type\": \"string\",\n          \"description\": \"签名\"\n        },\n        \"prepayId\": {\n          \"type\": \"string\",\n          \"description\": \"微信生成的预支付回话标识，用于后续接口调用中使用，该值有效期为2小时,针对H5支付此参数无特殊用途\"\n        },\n        \"signType\": {\n          \"type\": \"string\",\n          \"description\": \"签名方式\"\n        },\n        \"timeStamp\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"当前的时间，其他详见时间戳规则\"\n        }\n      },\n      \"title\": \"UserRechargePaymentResponse对象\",\n      \"description\": \"充值返回对象\",\n      \"$$ref\": \"#/definitions/UserRechargePaymentResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserRechargePaymentResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserRechargePaymentResponse对象»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"from\": {\n      \"type\": \"string\",\n      \"description\": \"来源 | public =  微信公众号, weixinh5 =微信H5支付, routine = 小程序\"\n    },\n    \"payType\": {\n      \"type\": \"string\",\n      \"description\": \"支付方式| weixin = 微信，alipay = 支付宝\"\n    },\n    \"price\": {\n      \"type\": \"number\",\n      \"description\": \"充值金额\"\n    },\n    \"rechar_id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"选择金额组合数据id\"\n    }\n  },\n  \"title\": \"UserRechargeRequest对象\",\n  \"description\": \"充值\",\n  \"$$ref\": \"#/definitions/UserRechargeRequest对象\"\n}", "project_id": 56, "catid": 4991, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/recharge/routine", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7324, "method": "POST", "title": "小程序充值", "path": "/api/front/recharge/routine", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc52", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«string,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«string,object»»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"from\": {\n      \"type\": \"string\",\n      \"description\": \"来源 | public =  微信公众号, weixinh5 =微信H5支付, routine = 小程序\"\n    },\n    \"payType\": {\n      \"type\": \"string\",\n      \"description\": \"支付方式| weixin = 微信，alipay = 支付宝\"\n    },\n    \"price\": {\n      \"type\": \"number\",\n      \"description\": \"充值金额\"\n    },\n    \"rechar_id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"选择金额组合数据id\"\n    }\n  },\n  \"title\": \"UserRechargeRequest对象\",\n  \"description\": \"充值\",\n  \"$$ref\": \"#/definitions/UserRechargeRequest对象\"\n}", "project_id": 56, "catid": 4991, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "用户 -- 地址", "desc": "User Address Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/address/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7202, "method": "POST", "title": "保存", "path": "/api/front/address/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb92bc7e8e560914fbfc", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在市\"\n        },\n        \"cityId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"城市id\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"detail\": {\n          \"type\": \"string\",\n          \"description\": \"收货人详细地址\"\n        },\n        \"district\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在区\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户地址id\"\n        },\n        \"isDefault\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否默认\"\n        },\n        \"isDel\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否删除\"\n        },\n        \"latitude\": {\n          \"type\": \"string\",\n          \"description\": \"纬度\"\n        },\n        \"longitude\": {\n          \"type\": \"string\",\n          \"description\": \"经度\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"收货人电话\"\n        },\n        \"postCode\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"邮编\"\n        },\n        \"province\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在省\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"收货人姓名\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        }\n      },\n      \"title\": \"UserAddress对象\",\n      \"description\": \"用户地址表\",\n      \"$$ref\": \"#/definitions/UserAddress对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserAddress对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserAddress对象»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"address\",\n    \"detail\",\n    \"isDefault\",\n    \"phone\",\n    \"realName\"\n  ],\n  \"properties\": {\n    \"address\": {\n      \"type\": \"object\",\n      \"required\": [\n        \"city\",\n        \"district\",\n        \"province\"\n      ],\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在市\"\n        },\n        \"cityId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"城市id\"\n        },\n        \"district\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在区\"\n        },\n        \"province\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在省\"\n        }\n      },\n      \"title\": \"UserAddressCityRequest对象\",\n      \"description\": \"用户地址城市\",\n      \"$$ref\": \"#/definitions/UserAddressCityRequest对象\"\n    },\n    \"detail\": {\n      \"type\": \"string\",\n      \"description\": \"收货人详细地址\"\n    },\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"用户地址id\"\n    },\n    \"isDefault\": {\n      \"type\": \"boolean\",\n      \"example\": false,\n      \"description\": \"是否默认\"\n    },\n    \"phone\": {\n      \"type\": \"string\",\n      \"description\": \"收货人电话\"\n    },\n    \"realName\": {\n      \"type\": \"string\",\n      \"description\": \"收货人姓名\"\n    }\n  },\n  \"title\": \"UserAddressRequest对象\",\n  \"description\": \"用户地址\",\n  \"$$ref\": \"#/definitions/UserAddressRequest对象\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/address/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7204, "method": "GET", "title": "列表", "path": "/api/front/address/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb92bc7e8e560914fbfe", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb92bc7e8e560914fbfd", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"city\": {\n                \"type\": \"string\",\n                \"description\": \"收货人所在市\"\n              },\n              \"cityId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"城市id\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"detail\": {\n                \"type\": \"string\",\n                \"description\": \"收货人详细地址\"\n              },\n              \"district\": {\n                \"type\": \"string\",\n                \"description\": \"收货人所在区\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户地址id\"\n              },\n              \"isDefault\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否默认\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否删除\"\n              },\n              \"latitude\": {\n                \"type\": \"string\",\n                \"description\": \"纬度\"\n              },\n              \"longitude\": {\n                \"type\": \"string\",\n                \"description\": \"经度\"\n              },\n              \"phone\": {\n                \"type\": \"string\",\n                \"description\": \"收货人电话\"\n              },\n              \"postCode\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"邮编\"\n              },\n              \"province\": {\n                \"type\": \"string\",\n                \"description\": \"收货人所在省\"\n              },\n              \"realName\": {\n                \"type\": \"string\",\n                \"description\": \"收货人姓名\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户id\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              }\n            },\n            \"title\": \"UserAddress对象\",\n            \"description\": \"用户地址表\",\n            \"$$ref\": \"#/definitions/UserAddress对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserAddress对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserAddress对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserAddress对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserAddress对象»»\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/address/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7198, "method": "POST", "title": "删除", "path": "/api/front/address/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb92bc7e8e560914fbfa", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"用户地址id\"\n    }\n  },\n  \"title\": \"UserAddressDelRequest对象\",\n  \"description\": \"用户地址\",\n  \"$$ref\": \"#/definitions/UserAddressDelRequest对象\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/address/detail/{id}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7200, "method": "GET", "title": "获取单个地址", "path": "/api/front/address/detail/{id}", "req_params": [{"_id": "5f35eb92bc7e8e560914fbfb", "name": "id", "desc": "id"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在市\"\n        },\n        \"cityId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"城市id\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"detail\": {\n          \"type\": \"string\",\n          \"description\": \"收货人详细地址\"\n        },\n        \"district\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在区\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户地址id\"\n        },\n        \"isDefault\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否默认\"\n        },\n        \"isDel\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否删除\"\n        },\n        \"latitude\": {\n          \"type\": \"string\",\n          \"description\": \"纬度\"\n        },\n        \"longitude\": {\n          \"type\": \"string\",\n          \"description\": \"经度\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"收货人电话\"\n        },\n        \"postCode\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"邮编\"\n        },\n        \"province\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在省\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"收货人姓名\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        }\n      },\n      \"title\": \"UserAddress对象\",\n      \"description\": \"用户地址表\",\n      \"$$ref\": \"#/definitions/UserAddress对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserAddress对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserAddress对象»\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/address/default", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7194, "method": "GET", "title": "获取默认地址", "path": "/api/front/address/default", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在市\"\n        },\n        \"cityId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"城市id\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"detail\": {\n          \"type\": \"string\",\n          \"description\": \"收货人详细地址\"\n        },\n        \"district\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在区\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户地址id\"\n        },\n        \"isDefault\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否默认\"\n        },\n        \"isDel\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否删除\"\n        },\n        \"latitude\": {\n          \"type\": \"string\",\n          \"description\": \"纬度\"\n        },\n        \"longitude\": {\n          \"type\": \"string\",\n          \"description\": \"经度\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"收货人电话\"\n        },\n        \"postCode\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"邮编\"\n        },\n        \"province\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在省\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"收货人姓名\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        }\n      },\n      \"title\": \"UserAddress对象\",\n      \"description\": \"用户地址表\",\n      \"$$ref\": \"#/definitions/UserAddress对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserAddress对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserAddress对象»\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/address/default/set", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7196, "method": "POST", "title": "设置默认地址", "path": "/api/front/address/default/set", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb92bc7e8e560914fbf9", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在市\"\n        },\n        \"cityId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"城市id\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"detail\": {\n          \"type\": \"string\",\n          \"description\": \"收货人详细地址\"\n        },\n        \"district\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在区\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户地址id\"\n        },\n        \"isDefault\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否默认\"\n        },\n        \"isDel\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否删除\"\n        },\n        \"latitude\": {\n          \"type\": \"string\",\n          \"description\": \"纬度\"\n        },\n        \"longitude\": {\n          \"type\": \"string\",\n          \"description\": \"经度\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"收货人电话\"\n        },\n        \"postCode\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"邮编\"\n        },\n        \"province\": {\n          \"type\": \"string\",\n          \"description\": \"收货人所在省\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"收货人姓名\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        }\n      },\n      \"title\": \"UserAddress对象\",\n      \"description\": \"用户地址表\",\n      \"$$ref\": \"#/definitions/UserAddress对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserAddress对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserAddress对象»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"用户地址id\"\n    }\n  },\n  \"title\": \"UserAddressDelRequest对象\",\n  \"description\": \"用户地址\",\n  \"$$ref\": \"#/definitions/UserAddressDelRequest对象\"\n}", "project_id": 56, "catid": 5000, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "用户 -- 点赞/收藏", "desc": "User Collect Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/collect/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7240, "method": "POST", "title": "取消收藏产品", "path": "/api/front/collect/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc18", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"category\": {\n      \"type\": \"string\",\n      \"description\": \"产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)\"\n    },\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"商品ID\"\n    }\n  },\n  \"title\": \"UserCollectRequest对象\",\n  \"description\": \"商品点赞和收藏表\",\n  \"$$ref\": \"#/definitions/UserCollectRequest对象\"\n}", "project_id": 56, "catid": 5009, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/collect/all", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7238, "method": "POST", "title": "批量收藏", "path": "/api/front/collect/all", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc17", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"category\": {\n      \"type\": \"string\",\n      \"description\": \"产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)\"\n    },\n    \"id\": {\n      \"type\": \"array\",\n      \"description\": \"商品ID\",\n      \"items\": {\n        \"type\": \"integer\",\n        \"format\": \"int32\"\n      }\n    }\n  },\n  \"title\": \"StoreProductRelationRequest对象\",\n  \"description\": \"商品点赞和收藏表\",\n  \"$$ref\": \"#/definitions/StoreProductRelationRequest对象\"\n}", "project_id": 56, "catid": 5009, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/collect/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7236, "method": "POST", "title": "添加收藏产品", "path": "/api/front/collect/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc16", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"category\": {\n      \"type\": \"string\",\n      \"description\": \"产品类型|store=普通产品,product_seckill=秒杀产品(默认 普通产品 store)\"\n    },\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"商品ID\"\n    }\n  },\n  \"title\": \"UserCollectRequest对象\",\n  \"description\": \"商品点赞和收藏表\",\n  \"$$ref\": \"#/definitions/UserCollectRequest对象\"\n}", "project_id": 56, "catid": 5009, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/collect/user", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7242, "method": "GET", "title": "获取收藏产品", "path": "/api/front/collect/user", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb93bc7e8e560914fc1a", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb93bc7e8e560914fc19", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"activity\": {\n                \"type\": \"string\",\n                \"description\": \"活动显示排序1=秒杀，2=砍价，3=拼团\"\n              },\n              \"addTime\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"添加时间\"\n              },\n              \"barCode\": {\n                \"type\": \"string\",\n                \"description\": \"商品条码（一维码）\"\n              },\n              \"browse\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"浏览量\"\n              },\n              \"cateId\": {\n                \"type\": \"string\",\n                \"description\": \"分类id\"\n              },\n              \"codePath\": {\n                \"type\": \"string\",\n                \"description\": \"商品二维码地址(用户小程序海报)\"\n              },\n              \"cost\": {\n                \"type\": \"number\",\n                \"description\": \"成本价\"\n              },\n              \"ficti\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"虚拟销量\"\n              },\n              \"giveIntegral\": {\n                \"type\": \"number\",\n                \"description\": \"获得积分\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"isBargain\": {\n                \"type\": \"boolean\",\n                \"description\": \"砍价状态 0未开启 1开启\"\n              },\n              \"isBenefit\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否优惠\"\n              },\n              \"isBest\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否精品\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否删除\"\n              },\n              \"isGood\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否优品推荐\"\n              },\n              \"isHot\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否热卖\"\n              },\n              \"isNew\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否新品\"\n              },\n              \"isPostage\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否包邮\"\n              },\n              \"isSeckill\": {\n                \"type\": \"boolean\",\n                \"description\": \"秒杀状态 0 未开启 1已开启\"\n              },\n              \"isShow\": {\n                \"type\": \"boolean\",\n                \"description\": \"状态（0：未上架，1：上架）\"\n              },\n              \"isSub\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否单独分佣\"\n              },\n              \"keyword\": {\n                \"type\": \"string\",\n                \"description\": \"关键字\"\n              },\n              \"merId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n              },\n              \"merUse\": {\n                \"type\": \"boolean\",\n                \"description\": \"商户是否代理 0不可代理1可代理\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"postage\": {\n                \"type\": \"number\",\n                \"description\": \"邮费\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sliderImage\": {\n                \"type\": \"string\",\n                \"description\": \"轮播图\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"soureLink\": {\n                \"type\": \"string\",\n                \"description\": \"淘宝京东1688类型\"\n              },\n              \"specType\": {\n                \"type\": \"boolean\",\n                \"description\": \"规格 0单 1多\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeInfo\": {\n                \"type\": \"string\",\n                \"description\": \"商品简介\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"tempId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"运费模板ID\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              },\n              \"videoLink\": {\n                \"type\": \"string\",\n                \"description\": \"主图视频链接\"\n              },\n              \"vipPrice\": {\n                \"type\": \"number\",\n                \"description\": \"会员价格\"\n              }\n            },\n            \"title\": \"StoreProduct对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/StoreProduct对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«StoreProduct对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«StoreProduct对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«StoreProduct对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«StoreProduct对象»»\"\n}", "project_id": 56, "catid": 5009, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}]}, {"index": 0, "name": "用户 -- 用户中心", "desc": "User Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/user/level/grade", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7362, "method": "GET", "title": "会员等级列表", "path": "/api/front/user/level/grade", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"discount\": {\n                \"type\": \"number\",\n                \"description\": \"享受折扣\"\n              },\n              \"experience\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"购买金额|经验达到\"\n              },\n              \"grade\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"会员等级\"\n              },\n              \"icon\": {\n                \"type\": \"string\",\n                \"description\": \"会员图标\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"会员卡背景\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否删除.1=删除,0=未删除\"\n              },\n              \"isShow\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否显示 1=显示,0=隐藏\"\n              },\n              \"memo\": {\n                \"type\": \"string\",\n                \"description\": \"说明\"\n              },\n              \"name\": {\n                \"type\": \"string\",\n                \"description\": \"会员名称\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              }\n            },\n            \"title\": \"SystemUserLevel对象\",\n            \"description\": \"设置用户等级表\",\n            \"$$ref\": \"#/definitions/SystemUserLevel对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«SystemUserLevel对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«SystemUserLevel对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«SystemUserLevel对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«SystemUserLevel对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/brokerage_rank", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7218, "method": "GET", "title": "佣金排行", "path": "/api/front/brokerage_rank", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb92bc7e8e560914fc06", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb92bc7e8e560914fc05", "name": "page", "desc": "页码"}, {"required": "1", "_id": "5f35eb92bc7e8e560914fc04", "name": "type", "desc": "type"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"account\": {\n            \"type\": \"string\",\n            \"description\": \"用户账号\"\n          },\n          \"addIp\": {\n            \"type\": \"string\",\n            \"description\": \"添加ip\"\n          },\n          \"addres\": {\n            \"type\": \"string\",\n            \"description\": \"详细地址\"\n          },\n          \"adminid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"管理员编号 \"\n          },\n          \"avatar\": {\n            \"type\": \"string\",\n            \"description\": \"用户头像\"\n          },\n          \"birthday\": {\n            \"type\": \"string\",\n            \"description\": \"生日\"\n          },\n          \"brokeragePrice\": {\n            \"type\": \"number\",\n            \"description\": \"佣金金额\"\n          },\n          \"cardId\": {\n            \"type\": \"string\",\n            \"description\": \"身份证号码\"\n          },\n          \"cleanTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"最后一次登录时间\"\n          },\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"experience\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户剩余经验\"\n          },\n          \"groupId\": {\n            \"type\": \"string\",\n            \"description\": \"用户分组id\"\n          },\n          \"integral\": {\n            \"type\": \"number\",\n            \"description\": \"用户剩余积分\"\n          },\n          \"isPromoter\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否为推广员\"\n          },\n          \"lastIp\": {\n            \"type\": \"string\",\n            \"description\": \"最后一次登录ip\"\n          },\n          \"lastLoginTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"最后一次登录时间\"\n          },\n          \"level\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"等级\"\n          },\n          \"loginType\": {\n            \"type\": \"string\",\n            \"description\": \"用户登陆类型，h5,wechat,routine\"\n          },\n          \"mark\": {\n            \"type\": \"string\",\n            \"description\": \"用户备注\"\n          },\n          \"nickname\": {\n            \"type\": \"string\",\n            \"description\": \"用户昵称\"\n          },\n          \"nowMoney\": {\n            \"type\": \"number\",\n            \"description\": \"用户余额\"\n          },\n          \"partnerId\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"合伙人id\"\n          },\n          \"path\": {\n            \"type\": \"string\",\n            \"description\": \"用户推广等级\"\n          },\n          \"payCount\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户购买次数\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"description\": \"手机号码\"\n          },\n          \"realName\": {\n            \"type\": \"string\",\n            \"description\": \"真实姓名\"\n          },\n          \"signNum\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"连续签到天数\"\n          },\n          \"spreadCount\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"下级人数\"\n          },\n          \"spreadTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"推广员关联时间\"\n          },\n          \"spreadUid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"推广人id\"\n          },\n          \"status\": {\n            \"type\": \"boolean\",\n            \"description\": \"1为正常，0为禁止\"\n          },\n          \"subscribe\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否关注公众号\"\n          },\n          \"tagId\": {\n            \"type\": \"string\",\n            \"description\": \"用户标签id\"\n          },\n          \"uid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户id\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"userType\": {\n            \"type\": \"string\",\n            \"description\": \"用户类型\"\n          }\n        },\n        \"title\": \"User对象\",\n        \"description\": \"用户表\",\n        \"$$ref\": \"#/definitions/User对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«User对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«User对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/user/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7358, "method": "POST", "title": "修改个人资料", "path": "/api/front/user/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb98bc7e8e560914fc70", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"avatar\": {\n      \"type\": \"string\",\n      \"description\": \"用户头像\"\n    },\n    \"nickname\": {\n      \"type\": \"string\",\n      \"description\": \"用户昵称\"\n    }\n  },\n  \"title\": \"UserEditRequest对象\",\n  \"description\": \"修改个人资料\",\n  \"$$ref\": \"#/definitions/UserEditRequest对象\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/brokerageRankNumber", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7356, "method": "GET", "title": "当前用户在佣金排行第几名", "path": "/api/front/user/brokerageRankNumber", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "1", "_id": "5f35eb98bc7e8e560914fc6f", "name": "type", "desc": "type"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«int»\",\n  \"$$ref\": \"#/definitions/CommonResult«int»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/userinfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7384, "method": "GET", "title": "当前登录用户信息", "path": "/api/front/userinfo", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"account\": {\n          \"type\": \"string\",\n          \"description\": \"用户账号\"\n        },\n        \"addIp\": {\n          \"type\": \"string\",\n          \"description\": \"添加ip\"\n        },\n        \"addres\": {\n          \"type\": \"string\",\n          \"description\": \"详细地址\"\n        },\n        \"adminid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"管理员编号 \"\n        },\n        \"avatar\": {\n          \"type\": \"string\",\n          \"description\": \"用户头像\"\n        },\n        \"birthday\": {\n          \"type\": \"string\",\n          \"description\": \"生日\"\n        },\n        \"brokeragePrice\": {\n          \"type\": \"number\",\n          \"description\": \"佣金金额\"\n        },\n        \"cardId\": {\n          \"type\": \"string\",\n          \"description\": \"身份证号码\"\n        },\n        \"cleanTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"最后一次登录时间\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"experience\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户剩余经验\"\n        },\n        \"groupId\": {\n          \"type\": \"string\",\n          \"description\": \"用户分组id\"\n        },\n        \"integral\": {\n          \"type\": \"number\",\n          \"description\": \"用户剩余积分\"\n        },\n        \"isPromoter\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否为推广员\"\n        },\n        \"lastIp\": {\n          \"type\": \"string\",\n          \"description\": \"最后一次登录ip\"\n        },\n        \"lastLoginTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"最后一次登录时间\"\n        },\n        \"level\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"等级\"\n        },\n        \"loginType\": {\n          \"type\": \"string\",\n          \"description\": \"用户登陆类型，h5,wechat,routine\"\n        },\n        \"mark\": {\n          \"type\": \"string\",\n          \"description\": \"用户备注\"\n        },\n        \"nickname\": {\n          \"type\": \"string\",\n          \"description\": \"用户昵称\"\n        },\n        \"nowMoney\": {\n          \"type\": \"number\",\n          \"description\": \"用户余额\"\n        },\n        \"partnerId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"合伙人id\"\n        },\n        \"path\": {\n          \"type\": \"string\",\n          \"description\": \"用户推广等级\"\n        },\n        \"payCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户购买次数\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"手机号码\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"真实姓名\"\n        },\n        \"signNum\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"连续签到天数\"\n        },\n        \"spreadCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"下级人数\"\n        },\n        \"spreadTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"推广员关联时间\"\n        },\n        \"spreadUid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"推广人id\"\n        },\n        \"status\": {\n          \"type\": \"boolean\",\n          \"description\": \"1为正常，0为禁止\"\n        },\n        \"subscribe\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否关注公众号\"\n        },\n        \"tagId\": {\n          \"type\": \"string\",\n          \"description\": \"用户标签id\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"userType\": {\n          \"type\": \"string\",\n          \"description\": \"用户类型\"\n        }\n      },\n      \"title\": \"User对象\",\n      \"description\": \"用户表\",\n      \"$$ref\": \"#/definitions/User对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«User对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«User对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/register/reset", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7330, "method": "POST", "title": "手机号修改密码", "path": "/api/front/register/reset", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc56", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"account\",\n    \"captcha\",\n    \"password\"\n  ],\n  \"properties\": {\n    \"account\": {\n      \"type\": \"string\",\n      \"description\": \"手机号\"\n    },\n    \"captcha\": {\n      \"type\": \"string\",\n      \"description\": \"验证码\"\n    },\n    \"password\": {\n      \"type\": \"string\",\n      \"description\": \"密码\"\n    }\n  },\n  \"title\": \"PasswordRequest对象\",\n  \"description\": \"修改密码\",\n  \"$$ref\": \"#/definitions/PasswordRequest对象\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/rank", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7320, "method": "GET", "title": "推广人排行", "path": "/api/front/rank", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb97bc7e8e560914fc51", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb97bc7e8e560914fc50", "name": "page", "desc": "页码"}, {"required": "1", "_id": "5f35eb97bc7e8e560914fc4f", "name": "type", "desc": "type"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"account\": {\n            \"type\": \"string\",\n            \"description\": \"用户账号\"\n          },\n          \"addIp\": {\n            \"type\": \"string\",\n            \"description\": \"添加ip\"\n          },\n          \"addres\": {\n            \"type\": \"string\",\n            \"description\": \"详细地址\"\n          },\n          \"adminid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"管理员编号 \"\n          },\n          \"avatar\": {\n            \"type\": \"string\",\n            \"description\": \"用户头像\"\n          },\n          \"birthday\": {\n            \"type\": \"string\",\n            \"description\": \"生日\"\n          },\n          \"brokeragePrice\": {\n            \"type\": \"number\",\n            \"description\": \"佣金金额\"\n          },\n          \"cardId\": {\n            \"type\": \"string\",\n            \"description\": \"身份证号码\"\n          },\n          \"cleanTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"最后一次登录时间\"\n          },\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"experience\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户剩余经验\"\n          },\n          \"groupId\": {\n            \"type\": \"string\",\n            \"description\": \"用户分组id\"\n          },\n          \"integral\": {\n            \"type\": \"number\",\n            \"description\": \"用户剩余积分\"\n          },\n          \"isPromoter\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否为推广员\"\n          },\n          \"lastIp\": {\n            \"type\": \"string\",\n            \"description\": \"最后一次登录ip\"\n          },\n          \"lastLoginTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"最后一次登录时间\"\n          },\n          \"level\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"等级\"\n          },\n          \"loginType\": {\n            \"type\": \"string\",\n            \"description\": \"用户登陆类型，h5,wechat,routine\"\n          },\n          \"mark\": {\n            \"type\": \"string\",\n            \"description\": \"用户备注\"\n          },\n          \"nickname\": {\n            \"type\": \"string\",\n            \"description\": \"用户昵称\"\n          },\n          \"nowMoney\": {\n            \"type\": \"number\",\n            \"description\": \"用户余额\"\n          },\n          \"partnerId\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"合伙人id\"\n          },\n          \"path\": {\n            \"type\": \"string\",\n            \"description\": \"用户推广等级\"\n          },\n          \"payCount\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户购买次数\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"description\": \"手机号码\"\n          },\n          \"realName\": {\n            \"type\": \"string\",\n            \"description\": \"真实姓名\"\n          },\n          \"signNum\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"连续签到天数\"\n          },\n          \"spreadCount\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"下级人数\"\n          },\n          \"spreadTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"推广员关联时间\"\n          },\n          \"spreadUid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"推广人id\"\n          },\n          \"status\": {\n            \"type\": \"boolean\",\n            \"description\": \"1为正常，0为禁止\"\n          },\n          \"subscribe\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否关注公众号\"\n          },\n          \"tagId\": {\n            \"type\": \"string\",\n            \"description\": \"用户标签id\"\n          },\n          \"uid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"用户id\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"userType\": {\n            \"type\": \"string\",\n            \"description\": \"用户类型\"\n          }\n        },\n        \"title\": \"User对象\",\n        \"description\": \"用户表\",\n        \"$$ref\": \"#/definitions/User对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«User对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«User对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/spread/count/{type}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7344, "method": "GET", "title": "推广佣金/提现总和", "path": "/api/front/spread/count/{type}", "req_params": [{"_id": "5f35eb97bc7e8e560914fc61", "name": "type", "desc": "类型 佣金类型3=佣金,4=提现"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"number\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«Map«string,bigdecimal»»\",\n  \"$$ref\": \"#/definitions/CommonResult«Map«string,bigdecimal»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/spread/commission/{type}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7342, "method": "GET", "title": "推广佣金明细", "path": "/api/front/spread/commission/{type}", "req_params": [{"_id": "5f35eb97bc7e8e560914fc5e", "name": "type", "desc": "类型 佣金类型|0=全部,1=消费,2=充值,3=返佣,4=提现"}], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb97bc7e8e560914fc60", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb97bc7e8e560914fc5f", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"date\": {\n                \"type\": \"string\",\n                \"description\": \"月份\"\n              },\n              \"list\": {\n                \"type\": \"array\",\n                \"description\": \"数据\",\n                \"items\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"add_time\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\",\n                      \"description\": \"创建时间\"\n                    },\n                    \"balance\": {\n                      \"type\": \"number\",\n                      \"description\": \"剩余\"\n                    },\n                    \"category\": {\n                      \"type\": \"string\",\n                      \"description\": \"明细种类\"\n                    },\n                    \"id\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"用户账单id\"\n                    },\n                    \"linkId\": {\n                      \"type\": \"string\",\n                      \"description\": \"关联id\"\n                    },\n                    \"mark\": {\n                      \"type\": \"string\",\n                      \"description\": \"备注\"\n                    },\n                    \"number\": {\n                      \"type\": \"number\",\n                      \"description\": \"明细数字\"\n                    },\n                    \"pm\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"0 = 支出 1 = 获得\"\n                    },\n                    \"status\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"0 = 带确定 1 = 有效 -1 = 无效\"\n                    },\n                    \"title\": {\n                      \"type\": \"string\",\n                      \"description\": \"账单标题\"\n                    },\n                    \"type\": {\n                      \"type\": \"string\",\n                      \"description\": \"明细类型\"\n                    },\n                    \"uid\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"用户uid\"\n                    },\n                    \"updateTime\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\",\n                      \"description\": \"创建时间\"\n                    }\n                  },\n                  \"title\": \"UserBill对象\",\n                  \"description\": \"用户账单表\",\n                  \"$$ref\": \"#/definitions/UserBill对象\"\n                }\n              }\n            },\n            \"title\": \"UserSpreadCommissionResponse对象\",\n            \"description\": \"推广佣金明细\",\n            \"$$ref\": \"#/definitions/UserSpreadCommissionResponse对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserSpreadCommissionResponse对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserSpreadCommissionResponse对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserSpreadCommissionResponse对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserSpreadCommissionResponse对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/commission", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7244, "method": "GET", "title": "推广数据接口(昨天的佣金 累计提现金额 当前佣金)", "path": "/api/front/commission", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"commissionCount\": {\n          \"type\": \"number\",\n          \"description\": \"当前佣金\"\n        },\n        \"extractCount\": {\n          \"type\": \"number\",\n          \"description\": \"累计提现金额\"\n        },\n        \"lastDayCount\": {\n          \"type\": \"number\",\n          \"description\": \"昨天的佣金\"\n        }\n      },\n      \"title\": \"UserCommissionResponse对象\",\n      \"description\": \"推广佣金明细\",\n      \"$$ref\": \"#/definitions/UserCommissionResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserCommissionResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserCommissionResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/user/spread/banner", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7380, "method": "GET", "title": "推广海报图", "path": "/api/front/user/spread/banner", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb99bc7e8e560914fc7c", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb99bc7e8e560914fc7b", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"id\"\n          },\n          \"pic\": {\n            \"type\": \"string\",\n            \"description\": \"背景图\"\n          },\n          \"title\": {\n            \"type\": \"string\",\n            \"description\": \"名称\"\n          }\n        },\n        \"title\": \"UserSpreadBannerResponse对象\",\n        \"description\": \"用户推广海报\",\n        \"$$ref\": \"#/definitions/UserSpreadBannerResponse对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«UserSpreadBannerResponse对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«UserSpreadBannerResponse对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/spread/people", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7348, "method": "GET", "title": "推广用户", "path": "/api/front/spread/people", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc69", "name": "grade", "desc": "推荐人类型|0=一级|1=二级"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc68", "name": "isAsc", "desc": "排序值 DESC ASC"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc67", "name": "keyword", "desc": "搜索关键字"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc66", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc65", "name": "page", "desc": "页码"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc64", "name": "sortKey", "desc": "排序, 排序|childCount=团队排序,numberCount=金额排序,orderCount=订单排序"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"avatar\": {\n          \"type\": \"string\",\n          \"description\": \"用户头像\"\n        },\n        \"childCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"推广人数\"\n        },\n        \"nickname\": {\n          \"type\": \"string\",\n          \"description\": \"用户昵称\"\n        },\n        \"numberCount\": {\n          \"type\": \"number\",\n          \"description\": \"订单金额\"\n        },\n        \"orderCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"订单数量\"\n        },\n        \"time\": {\n          \"type\": \"string\",\n          \"description\": \"添加时间\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户编号\"\n        }\n      },\n      \"title\": \"UserSpreadPeopleResponse对象\",\n      \"description\": \"推广人信息\",\n      \"$$ref\": \"#/definitions/UserSpreadPeopleResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserSpreadPeopleResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserSpreadPeopleResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/spread/order", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7346, "method": "GET", "title": "推广订单", "path": "/api/front/spread/order", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb97bc7e8e560914fc63", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb97bc7e8e560914fc62", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"count\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\",\n          \"description\": \"累计推广订单\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"description\": \"推广人列表\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"child\": {\n                \"type\": \"array\",\n                \"description\": \"推广订单信息\",\n                \"items\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"avatar\": {\n                      \"type\": \"string\",\n                      \"description\": \"用户头像\"\n                    },\n                    \"nickname\": {\n                      \"type\": \"string\",\n                      \"description\": \"用户昵称\"\n                    },\n                    \"number\": {\n                      \"type\": \"number\",\n                      \"description\": \"返佣金额\"\n                    },\n                    \"orderId\": {\n                      \"type\": \"string\",\n                      \"description\": \"订单号\"\n                    },\n                    \"time\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\",\n                      \"description\": \"返佣时间\"\n                    },\n                    \"type\": {\n                      \"type\": \"string\",\n                      \"description\": \"订单显示类型\"\n                    }\n                  },\n                  \"title\": \"UserSpreadOrderItemChildResponse对象\",\n                  \"description\": \"推广订单信息子集\",\n                  \"$$ref\": \"#/definitions/UserSpreadOrderItemChildResponse对象\"\n                }\n              },\n              \"count\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"推广条数\"\n              },\n              \"time\": {\n                \"type\": \"string\",\n                \"description\": \"推广年月\"\n              }\n            },\n            \"title\": \"UserSpreadOrderItemResponse对象\",\n            \"description\": \"推广订单信息\",\n            \"$$ref\": \"#/definitions/UserSpreadOrderItemResponse对象\"\n          }\n        }\n      },\n      \"title\": \"UserSpreadOrderResponse对象\",\n      \"description\": \"推广订单\",\n      \"$$ref\": \"#/definitions/UserSpreadOrderResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserSpreadOrderResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserSpreadOrderResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/extract/cash", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7258, "method": "POST", "title": "提现申请", "path": "/api/front/extract/cash", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb94bc7e8e560914fc22", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"alipayCode\": {\n      \"type\": \"string\",\n      \"description\": \"支付宝账号\"\n    },\n    \"bankname\": {\n      \"type\": \"string\",\n      \"description\": \"提现银行名称\"\n    },\n    \"cardum\": {\n      \"type\": \"string\",\n      \"description\": \"银行卡\"\n    },\n    \"extractType\": {\n      \"type\": \"string\",\n      \"description\": \"提现方式| alipay=支付宝,bank=银行卡,weixin=微信\"\n    },\n    \"money\": {\n      \"type\": \"number\",\n      \"description\": \"提现金额\"\n    },\n    \"name\": {\n      \"type\": \"string\",\n      \"description\": \"姓名\"\n    },\n    \"wechat\": {\n      \"type\": \"string\",\n      \"description\": \"微信号\"\n    }\n  },\n  \"title\": \"UserExtractRequest对象\",\n  \"description\": \"用户提现\",\n  \"$$ref\": \"#/definitions/UserExtractRequest对象\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/extract/bank", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7256, "method": "GET", "title": "提现银行/提现最低金额", "path": "/api/front/extract/bank", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"brokenCommission\": {\n          \"type\": \"number\",\n          \"description\": \"冻结佣金\"\n        },\n        \"brokenDay\": {\n          \"type\": \"string\",\n          \"description\": \"冻结天数\"\n        },\n        \"commissionCount\": {\n          \"type\": \"number\",\n          \"description\": \"可提现佣金\"\n        },\n        \"extractBank\": {\n          \"type\": \"array\",\n          \"description\": \"提现银行\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"minPrice\": {\n          \"type\": \"string\",\n          \"description\": \"提现最低金额\"\n        }\n      },\n      \"title\": \"UserExtractCashResponse对象\",\n      \"description\": \"提现银行/提现最低金额\",\n      \"$$ref\": \"#/definitions/UserExtractCashResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserExtractCashResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserExtractCashResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/user/balance", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7354, "method": "GET", "title": "用户资金统计", "path": "/api/front/user/balance", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"nowMoney\": {\n          \"type\": \"number\",\n          \"description\": \"当前总资金\"\n        },\n        \"orderStatusSum\": {\n          \"type\": \"number\",\n          \"description\": \"累计消费\"\n        },\n        \"recharge\": {\n          \"type\": \"number\",\n          \"description\": \"累计充值\"\n        }\n      },\n      \"title\": \"UserBalanceResponse对象\",\n      \"description\": \"用户资金统计\",\n      \"$$ref\": \"#/definitions/UserBalanceResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserBalanceResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserBalanceResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/integral/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7264, "method": "GET", "title": "积分记录", "path": "/api/front/integral/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb94bc7e8e560914fc27", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb94bc7e8e560914fc26", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"add_time\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"balance\": {\n                \"type\": \"number\",\n                \"description\": \"剩余\"\n              },\n              \"category\": {\n                \"type\": \"string\",\n                \"description\": \"明细种类\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户账单id\"\n              },\n              \"linkId\": {\n                \"type\": \"string\",\n                \"description\": \"关联id\"\n              },\n              \"mark\": {\n                \"type\": \"string\",\n                \"description\": \"备注\"\n              },\n              \"number\": {\n                \"type\": \"number\",\n                \"description\": \"明细数字\"\n              },\n              \"pm\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"0 = 支出 1 = 获得\"\n              },\n              \"status\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"0 = 带确定 1 = 有效 -1 = 无效\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"账单标题\"\n              },\n              \"type\": {\n                \"type\": \"string\",\n                \"description\": \"明细类型\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户uid\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              }\n            },\n            \"title\": \"UserBill对象\",\n            \"description\": \"用户账单表\",\n            \"$$ref\": \"#/definitions/UserBill对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserBill对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserBill对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserBill对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserBill对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/user/expList", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7360, "method": "GET", "title": "经验记录", "path": "/api/front/user/expList", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc72", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc71", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"add_time\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"balance\": {\n                \"type\": \"number\",\n                \"description\": \"剩余\"\n              },\n              \"category\": {\n                \"type\": \"string\",\n                \"description\": \"明细种类\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户账单id\"\n              },\n              \"linkId\": {\n                \"type\": \"string\",\n                \"description\": \"关联id\"\n              },\n              \"mark\": {\n                \"type\": \"string\",\n                \"description\": \"备注\"\n              },\n              \"number\": {\n                \"type\": \"number\",\n                \"description\": \"明细数字\"\n              },\n              \"pm\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"0 = 支出 1 = 获得\"\n              },\n              \"status\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"0 = 带确定 1 = 有效 -1 = 无效\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"账单标题\"\n              },\n              \"type\": {\n                \"type\": \"string\",\n                \"description\": \"明细类型\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户uid\"\n              },\n              \"updateTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              }\n            },\n            \"title\": \"UserBill对象\",\n            \"description\": \"用户账单表\",\n            \"$$ref\": \"#/definitions/UserBill对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserBill对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserBill对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserBill对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserBill对象»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/binding", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7216, "method": "POST", "title": "绑定手机号", "path": "/api/front/binding", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb92bc7e8e560914fc03", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"account\",\n    \"captcha\"\n  ],\n  \"properties\": {\n    \"account\": {\n      \"type\": \"string\",\n      \"description\": \"手机号\"\n    },\n    \"captcha\": {\n      \"type\": \"string\",\n      \"description\": \"验证码\"\n    }\n  },\n  \"title\": \"UserBindingRequest对象\",\n  \"description\": \"绑定手机号\",\n  \"$$ref\": \"#/definitions/UserBindingRequest对象\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/menu/user", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7274, "method": "GET", "title": "获取个人中心菜单", "path": "/api/front/menu/user", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"object\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«HashMap«string,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«HashMap«string,object»»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/user", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7352, "method": "GET", "title": "获取个人资料", "path": "/api/front/user", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"account\": {\n          \"type\": \"string\",\n          \"description\": \"用户账号\"\n        },\n        \"addIp\": {\n          \"type\": \"string\",\n          \"description\": \"添加ip\"\n        },\n        \"addres\": {\n          \"type\": \"string\",\n          \"description\": \"详细地址\"\n        },\n        \"adminid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"管理员编号 \"\n        },\n        \"avatar\": {\n          \"type\": \"string\",\n          \"description\": \"用户头像\"\n        },\n        \"birthday\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"生日\"\n        },\n        \"brokeragePrice\": {\n          \"type\": \"number\",\n          \"description\": \"佣金金额\"\n        },\n        \"cardId\": {\n          \"type\": \"string\",\n          \"description\": \"身份证号码\"\n        },\n        \"cleanTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"最后一次登录时间\"\n        },\n        \"couponCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户优惠券数量\"\n        },\n        \"createTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"experience\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户剩余经验\"\n        },\n        \"groupId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户分组id\"\n        },\n        \"integral\": {\n          \"type\": \"number\",\n          \"description\": \"用户剩余积分\"\n        },\n        \"isPromoter\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否为推广员\"\n        },\n        \"lastIp\": {\n          \"type\": \"string\",\n          \"description\": \"最后一次登录ip\"\n        },\n        \"lastLoginTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"最后一次登录时间\"\n        },\n        \"level\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"等级\"\n        },\n        \"loginType\": {\n          \"type\": \"string\",\n          \"description\": \"用户登陆类型，h5,wechat,routine\"\n        },\n        \"mark\": {\n          \"type\": \"string\",\n          \"description\": \"用户备注\"\n        },\n        \"nickname\": {\n          \"type\": \"string\",\n          \"description\": \"用户昵称\"\n        },\n        \"nowMoney\": {\n          \"type\": \"number\",\n          \"description\": \"用户余额\"\n        },\n        \"orderStatusNum\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"noBuy\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"未支付订单数量\"\n            },\n            \"noPink\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"拼团的订单数量\"\n            },\n            \"noPostage\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"未发货订单数量\"\n            },\n            \"noRefund\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"退款的订单数量\"\n            },\n            \"noReply\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"未评论订单数量\"\n            },\n            \"noTake\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"未收货订单数量\"\n            }\n          },\n          \"title\": \"UserCenterOrderStatusNumResponse对象\",\n          \"description\": \"个人中心 -- 订单状态数量\",\n          \"$$ref\": \"#/definitions/UserCenterOrderStatusNumResponse对象\"\n        },\n        \"partnerId\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"合伙人id\"\n        },\n        \"payCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户购买次数\"\n        },\n        \"phone\": {\n          \"type\": \"string\",\n          \"description\": \"手机号码\"\n        },\n        \"pwd\": {\n          \"type\": \"string\",\n          \"description\": \"用户密码\"\n        },\n        \"realName\": {\n          \"type\": \"string\",\n          \"description\": \"真实姓名\"\n        },\n        \"signNum\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"连续签到天数\"\n        },\n        \"spreadCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"下级人数\"\n        },\n        \"spreadTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"推广员关联时间\"\n        },\n        \"spreadUid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"推广元id\"\n        },\n        \"status\": {\n          \"type\": \"boolean\",\n          \"description\": \"1为正常，0为禁止\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"updateTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"创建时间\"\n        },\n        \"userType\": {\n          \"type\": \"string\",\n          \"description\": \"用户类型\"\n        },\n        \"vip\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否会员\"\n        },\n        \"vipIcon\": {\n          \"type\": \"string\",\n          \"description\": \"会员图标\"\n        },\n        \"vipName\": {\n          \"type\": \"string\",\n          \"description\": \"会员名称\"\n        }\n      },\n      \"title\": \"UserCenterResponse对象\",\n      \"description\": \"个人中心\",\n      \"$$ref\": \"#/definitions/UserCenterResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserCenterResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserCenterResponse对象»\"\n}", "project_id": 56, "catid": 5018, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}]}, {"index": 0, "name": "用户 -- 登录注册", "desc": "Login Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/sendCode", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7338, "method": "POST", "title": " 发送短信", "path": "/api/front/sendCode", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc5c", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb97bc7e8e560914fc5d", "name": "phone", "desc": "手机号码"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 5027, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/login/mobile", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7268, "method": "POST", "title": "手机号登录接口", "path": "/api/front/login/mobile", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb94bc7e8e560914fc29", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"expiresTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"用户登录密钥到期时间\"\n        },\n        \"token\": {\n          \"type\": \"string\",\n          \"description\": \"用户登录密钥\"\n        },\n        \"user\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"account\": {\n              \"type\": \"string\",\n              \"description\": \"用户账号\"\n            },\n            \"addIp\": {\n              \"type\": \"string\",\n              \"description\": \"添加ip\"\n            },\n            \"addres\": {\n              \"type\": \"string\",\n              \"description\": \"详细地址\"\n            },\n            \"adminid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"管理员编号 \"\n            },\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"birthday\": {\n              \"type\": \"string\",\n              \"description\": \"生日\"\n            },\n            \"brokeragePrice\": {\n              \"type\": \"number\",\n              \"description\": \"佣金金额\"\n            },\n            \"cardId\": {\n              \"type\": \"string\",\n              \"description\": \"身份证号码\"\n            },\n            \"cleanTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"experience\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户剩余经验\"\n            },\n            \"groupId\": {\n              \"type\": \"string\",\n              \"description\": \"用户分组id\"\n            },\n            \"integral\": {\n              \"type\": \"number\",\n              \"description\": \"用户剩余积分\"\n            },\n            \"isPromoter\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否为推广员\"\n            },\n            \"lastIp\": {\n              \"type\": \"string\",\n              \"description\": \"最后一次登录ip\"\n            },\n            \"lastLoginTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"level\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"等级\"\n            },\n            \"loginType\": {\n              \"type\": \"string\",\n              \"description\": \"用户登陆类型，h5,wechat,routine\"\n            },\n            \"mark\": {\n              \"type\": \"string\",\n              \"description\": \"用户备注\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"nowMoney\": {\n              \"type\": \"number\",\n              \"description\": \"用户余额\"\n            },\n            \"partnerId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"合伙人id\"\n            },\n            \"path\": {\n              \"type\": \"string\",\n              \"description\": \"用户推广等级\"\n            },\n            \"payCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户购买次数\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"手机号码\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"真实姓名\"\n            },\n            \"signNum\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"连续签到天数\"\n            },\n            \"spreadCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"下级人数\"\n            },\n            \"spreadTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"推广员关联时间\"\n            },\n            \"spreadUid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"推广人id\"\n            },\n            \"status\": {\n              \"type\": \"boolean\",\n              \"description\": \"1为正常，0为禁止\"\n            },\n            \"subscribe\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否关注公众号\"\n            },\n            \"tagId\": {\n              \"type\": \"string\",\n              \"description\": \"用户标签id\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"userType\": {\n              \"type\": \"string\",\n              \"description\": \"用户类型\"\n            }\n          },\n          \"title\": \"User对象\",\n          \"description\": \"用户表\",\n          \"$$ref\": \"#/definitions/User对象\"\n        }\n      },\n      \"title\": \"LoginResponse\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/LoginResponse\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«LoginResponse»\",\n  \"$$ref\": \"#/definitions/CommonResult«LoginResponse»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"account\",\n    \"captcha\"\n  ],\n  \"properties\": {\n    \"account\": {\n      \"type\": \"string\",\n      \"description\": \"手机号\"\n    },\n    \"captcha\": {\n      \"type\": \"string\",\n      \"description\": \"验证码\"\n    },\n    \"spread\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"推广人id\"\n    }\n  },\n  \"title\": \"LoginMobileRequest对象\",\n  \"description\": \"手机号注册\",\n  \"$$ref\": \"#/definitions/LoginMobileRequest对象\"\n}", "project_id": 56, "catid": 5027, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/login", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7266, "method": "POST", "title": "账号密码登录", "path": "/api/front/login", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb94bc7e8e560914fc28", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"expiresTime\": {\n          \"type\": \"string\",\n          \"format\": \"date-time\",\n          \"description\": \"用户登录密钥到期时间\"\n        },\n        \"token\": {\n          \"type\": \"string\",\n          \"description\": \"用户登录密钥\"\n        },\n        \"user\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"account\": {\n              \"type\": \"string\",\n              \"description\": \"用户账号\"\n            },\n            \"addIp\": {\n              \"type\": \"string\",\n              \"description\": \"添加ip\"\n            },\n            \"addres\": {\n              \"type\": \"string\",\n              \"description\": \"详细地址\"\n            },\n            \"adminid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"管理员编号 \"\n            },\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"birthday\": {\n              \"type\": \"string\",\n              \"description\": \"生日\"\n            },\n            \"brokeragePrice\": {\n              \"type\": \"number\",\n              \"description\": \"佣金金额\"\n            },\n            \"cardId\": {\n              \"type\": \"string\",\n              \"description\": \"身份证号码\"\n            },\n            \"cleanTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"experience\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户剩余经验\"\n            },\n            \"groupId\": {\n              \"type\": \"string\",\n              \"description\": \"用户分组id\"\n            },\n            \"integral\": {\n              \"type\": \"number\",\n              \"description\": \"用户剩余积分\"\n            },\n            \"isPromoter\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否为推广员\"\n            },\n            \"lastIp\": {\n              \"type\": \"string\",\n              \"description\": \"最后一次登录ip\"\n            },\n            \"lastLoginTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"level\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"等级\"\n            },\n            \"loginType\": {\n              \"type\": \"string\",\n              \"description\": \"用户登陆类型，h5,wechat,routine\"\n            },\n            \"mark\": {\n              \"type\": \"string\",\n              \"description\": \"用户备注\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"nowMoney\": {\n              \"type\": \"number\",\n              \"description\": \"用户余额\"\n            },\n            \"partnerId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"合伙人id\"\n            },\n            \"path\": {\n              \"type\": \"string\",\n              \"description\": \"用户推广等级\"\n            },\n            \"payCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户购买次数\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"手机号码\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"真实姓名\"\n            },\n            \"signNum\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"连续签到天数\"\n            },\n            \"spreadCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"下级人数\"\n            },\n            \"spreadTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"推广员关联时间\"\n            },\n            \"spreadUid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"推广人id\"\n            },\n            \"status\": {\n              \"type\": \"boolean\",\n              \"description\": \"1为正常，0为禁止\"\n            },\n            \"subscribe\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否关注公众号\"\n            },\n            \"tagId\": {\n              \"type\": \"string\",\n              \"description\": \"用户标签id\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"userType\": {\n              \"type\": \"string\",\n              \"description\": \"用户类型\"\n            }\n          },\n          \"title\": \"User对象\",\n          \"description\": \"用户表\",\n          \"$$ref\": \"#/definitions/User对象\"\n        }\n      },\n      \"title\": \"LoginResponse\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/LoginResponse\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«LoginResponse»\",\n  \"$$ref\": \"#/definitions/CommonResult«LoginResponse»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"account\",\n    \"password\"\n  ],\n  \"properties\": {\n    \"account\": {\n      \"type\": \"string\",\n      \"example\": ***********,\n      \"description\": \"手机号\"\n    },\n    \"password\": {\n      \"type\": \"string\",\n      \"example\": \"Abc123\",\n      \"description\": \"密码\"\n    }\n  },\n  \"title\": \"LoginRequest对象\",\n  \"description\": \"手机快速登录\",\n  \"$$ref\": \"#/definitions/LoginRequest对象\"\n}", "project_id": 56, "catid": 5027, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/logout", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7272, "method": "GET", "title": "退出", "path": "/api/front/logout", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "project_id": 56, "catid": 5027, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}, {"index": 0, "name": "用户 -- 签到", "desc": "User Sign Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/user/sign/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7374, "method": "GET", "title": "分页列表", "path": "/api/front/user/sign/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc77", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc76", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"createDay\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"签到日期\"\n              },\n              \"number\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"获得积分\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"签到说明\"\n              }\n            },\n            \"title\": \"UserSign对象\",\n            \"description\": \"签到记录表\",\n            \"$$ref\": \"#/definitions/UserSign对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserSign对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserSign对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserSign对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserSign对象»»\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/sign/month", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7376, "method": "GET", "title": "分页列表", "path": "/api/front/user/sign/month", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb98bc7e8e560914fc79", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb98bc7e8e560914fc78", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"createDay\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"签到日期\"\n              },\n              \"number\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"获得积分\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"签到说明\"\n              }\n            },\n            \"title\": \"UserSign对象\",\n            \"description\": \"签到记录表\",\n            \"$$ref\": \"#/definitions/UserSign对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«UserSign对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«UserSign对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«UserSign对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«UserSign对象»»\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/sign/integral", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7372, "method": "GET", "title": "签到", "path": "/api/front/user/sign/integral", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"day\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"第几天\"\n        },\n        \"experience\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"经验\"\n        },\n        \"id\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"integral\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"积分\"\n        },\n        \"title\": {\n          \"type\": \"string\",\n          \"description\": \"显示文字\"\n        }\n      },\n      \"title\": \"SystemGroupDataSignConfigVo对象\",\n      \"description\": \"签到记录\",\n      \"$$ref\": \"#/definitions/SystemGroupDataSignConfigVo对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«SystemGroupDataSignConfigVo对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«SystemGroupDataSignConfigVo对象»\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/sign/user", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7378, "method": "POST", "title": "签到用户信息", "path": "/api/front/user/sign/user", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb99bc7e8e560914fc7a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"avatar\": {\n          \"type\": \"string\",\n          \"description\": \"用户头像\"\n        },\n        \"deductionIntegral\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"累计抵扣积分\"\n        },\n        \"integral\": {\n          \"type\": \"number\",\n          \"description\": \"用户剩余积分\"\n        },\n        \"isDaySign\": {\n          \"type\": \"boolean\",\n          \"description\": \"今天是否签到\"\n        },\n        \"isPromoter\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否为推广员\"\n        },\n        \"isYesterdaySign\": {\n          \"type\": \"boolean\",\n          \"description\": \"昨天是否签到\"\n        },\n        \"nickname\": {\n          \"type\": \"string\",\n          \"description\": \"用户昵称\"\n        },\n        \"nowMoney\": {\n          \"type\": \"number\",\n          \"description\": \"用户余额\"\n        },\n        \"payCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户购买次数\"\n        },\n        \"signNum\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"连续签到天数\"\n        },\n        \"spreadCount\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"下级人数\"\n        },\n        \"sumIntegral\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"累计总积分\"\n        },\n        \"sumSignDay\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"累计签到次数\"\n        },\n        \"uid\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"用户id\"\n        },\n        \"yesterdayIntegral\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\",\n          \"description\": \"昨天累计积分\"\n        }\n      },\n      \"title\": \"UserSignInfoResponse对象\",\n      \"description\": \"修改个人资料\",\n      \"$$ref\": \"#/definitions/UserSignInfoResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«UserSignInfoResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«UserSignInfoResponse对象»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"all\": {\n      \"type\": \"boolean\",\n      \"description\": \"是否统计签到和统计积分使用情况|1=是,0=否\"\n    },\n    \"integral\": {\n      \"type\": \"boolean\",\n      \"description\": \"是否统计积分使用情况|1=是,0=否\"\n    },\n    \"sign\": {\n      \"type\": \"boolean\",\n      \"description\": \"是否统计签到|1=是,0=否\"\n    }\n  },\n  \"title\": \"UserSignInfoRequest对象\",\n  \"description\": \"个人签到信息\",\n  \"$$ref\": \"#/definitions/UserSignInfoRequest对象\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369241, "up_time": 1597369241, "__v": 0}, {"query_path": {"path": "/api/front/user/sign/get", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7370, "method": "GET", "title": "详情", "path": "/api/front/user/sign/get", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"object\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«HashMap«string,object»»\",\n  \"$$ref\": \"#/definitions/CommonResult«HashMap«string,object»»\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}, {"query_path": {"path": "/api/front/user/sign/config", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7368, "method": "GET", "title": "配置", "path": "/api/front/user/sign/config", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"limit\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"day\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"第几天\"\n              },\n              \"experience\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"经验\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"integral\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"积分\"\n              },\n              \"title\": {\n                \"type\": \"string\",\n                \"description\": \"显示文字\"\n              }\n            },\n            \"title\": \"SystemGroupDataSignConfigVo对象\",\n            \"description\": \"签到记录\",\n            \"$$ref\": \"#/definitions/SystemGroupDataSignConfigVo对象\"\n          }\n        },\n        \"page\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        },\n        \"total\": {\n          \"type\": \"integer\",\n          \"format\": \"int64\"\n        },\n        \"totalPage\": {\n          \"type\": \"integer\",\n          \"format\": \"int32\"\n        }\n      },\n      \"title\": \"CommonPage«SystemGroupDataSignConfigVo对象»\",\n      \"$$ref\": \"#/definitions/CommonPage«SystemGroupDataSignConfigVo对象»\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«CommonPage«SystemGroupDataSignConfigVo对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«CommonPage«SystemGroupDataSignConfigVo对象»»\"\n}", "project_id": 56, "catid": 5036, "uid": 93, "add_time": 1597369240, "up_time": 1597369240, "__v": 0}]}, {"index": 0, "name": "营销 -- 优惠券", "desc": "User Coupon Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/coupon/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7246, "method": "GET", "title": "我的优惠券", "path": "/api/front/coupon/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"primaryKey\"\n        ],\n        \"properties\": {\n          \"avatar\": {\n            \"type\": \"string\",\n            \"description\": \"用户头像\"\n          },\n          \"cid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"兑换的项目id\"\n          },\n          \"couponId\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"优惠券发布id\"\n          },\n          \"createTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"创建时间\"\n          },\n          \"endTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"过期时间\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"id\"\n          },\n          \"isValid\": {\n            \"type\": \"boolean\",\n            \"description\": \"用户头像\"\n          },\n          \"minPrice\": {\n            \"type\": \"number\",\n            \"description\": \"最低消费多少金额可用优惠券\"\n          },\n          \"money\": {\n            \"type\": \"number\",\n            \"description\": \"优惠券的面值\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"优惠券名称\"\n          },\n          \"nickname\": {\n            \"type\": \"string\",\n            \"description\": \"用户昵称\"\n          },\n          \"primaryKey\": {\n            \"type\": \"string\",\n            \"description\": \"主键id 商品id/分类id\"\n          },\n          \"startTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"开始使用时间\"\n          },\n          \"status\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"状态（0：未使用，1：已使用, 2:已失效）\"\n          },\n          \"type\": {\n            \"type\": \"string\",\n            \"description\": \"获取方式\"\n          },\n          \"uid\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"领取人id\"\n          },\n          \"updateTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"更新时间\"\n          },\n          \"useTime\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"使用时间\"\n          },\n          \"useType\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\",\n            \"description\": \"使用类型 1 全场通用, 2 商品券, 3 品类券\"\n          }\n        },\n        \"title\": \"StoreCouponUserResponse对象\",\n        \"description\": \"优惠券记录表\",\n        \"$$ref\": \"#/definitions/StoreCouponUserResponse对象\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«StoreCouponUserResponse对象»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«StoreCouponUserResponse对象»»\"\n}", "project_id": 56, "catid": 5045, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/coupon/receive/batch", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7250, "method": "POST", "title": "批量领券", "path": "/api/front/coupon/receive/batch", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc1c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"couponId\": {\n      \"type\": \"array\",\n      \"description\": \"优惠券id\",\n      \"items\": {\n        \"type\": \"integer\",\n        \"format\": \"int32\"\n      }\n    }\n  },\n  \"title\": \"UserCouponReceiveRequest对象\",\n  \"description\": \"用户领券\",\n  \"$$ref\": \"#/definitions/UserCouponReceiveRequest对象\"\n}", "project_id": 56, "catid": 5045, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}, {"query_path": {"path": "/api/front/coupon/receive", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7248, "method": "POST", "title": "领券", "path": "/api/front/coupon/receive", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb93bc7e8e560914fc1b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"string\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«string»\",\n  \"$$ref\": \"#/definitions/CommonResult«string»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"couponId\": {\n      \"type\": \"array\",\n      \"description\": \"优惠券id\",\n      \"items\": {\n        \"type\": \"integer\",\n        \"format\": \"int32\"\n      }\n    }\n  },\n  \"title\": \"UserCouponReceiveRequest对象\",\n  \"description\": \"用户领券\",\n  \"$$ref\": \"#/definitions/UserCouponReceiveRequest对象\"\n}", "project_id": 56, "catid": 5045, "uid": 93, "add_time": 1597369235, "up_time": 1597369235, "__v": 0}]}, {"index": 0, "name": "订单", "desc": "Store Order Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/order/again", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7276, "method": "POST", "title": "再次下单", "path": "/api/front/order/again", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc2a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"nui\": {\n      \"type\": \"string\",\n      \"description\": \"订单id\"\n    }\n  },\n  \"title\": \"OrderAgainRequest\",\n  \"$$ref\": \"#/definitions/OrderAgainRequest\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7290, "method": "POST", "title": "删除订单", "path": "/api/front/order/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc35", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc36", "name": "id", "desc": "id"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/product", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7300, "method": "POST", "title": "待评价商品信息查询", "path": "/api/front/order/product", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc3d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"orderId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"订单id\"\n    },\n    \"uni\": {\n      \"type\": \"string\",\n      \"description\": \"商品attrid\"\n    }\n  },\n  \"title\": \"GetProductReply\",\n  \"$$ref\": \"#/definitions/GetProductReply\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/pay", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7298, "method": "POST", "title": "支付", "path": "/api/front/order/pay", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc3c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"from\": {\n      \"type\": \"string\",\n      \"description\": \"支付平台\"\n    },\n    \"paytype\": {\n      \"type\": \"string\",\n      \"description\": \"支付类型\"\n    },\n    \"uni\": {\n      \"type\": \"string\",\n      \"description\": \"订单id\"\n    }\n  },\n  \"title\": \"OrderPayRequest对象\",\n  \"description\": \"订单支付\",\n  \"$$ref\": \"#/definitions/OrderPayRequest对象\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/express/{orderId}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7294, "method": "GET", "title": "物流信息查询", "path": "/api/front/order/express/{orderId}", "req_params": [{"_id": "5f35eb96bc7e8e560914fc38", "name": "orderId", "desc": "orderId"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/create/{key}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7286, "method": "POST", "title": "生成订单", "path": "/api/front/order/create/{key}", "req_params": [{"_id": "5f35eb95bc7e8e560914fc33", "name": "key", "desc": "key"}], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc34", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"isNew\"\n  ],\n  \"properties\": {\n    \"addressId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"收货地址id\"\n    },\n    \"bargainId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"combinationId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"couponId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"formId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"from\": {\n      \"type\": \"string\"\n    },\n    \"isNew\": {\n      \"type\": \"boolean\",\n      \"description\": \"是否为立即购买\"\n    },\n    \"mark\": {\n      \"type\": \"string\"\n    },\n    \"payType\": {\n      \"type\": \"string\"\n    },\n    \"phone\": {\n      \"type\": \"string\",\n      \"description\": \"手机号码\"\n    },\n    \"pinkId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"realName\": {\n      \"type\": \"string\",\n      \"description\": \"真实名称\"\n    },\n    \"seckillId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"shippingType\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"快递类型\"\n    },\n    \"storeId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\"\n    },\n    \"useIntegral\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"title\": \"OrderCreateRequest对象\",\n  \"description\": \"创建订单参数\",\n  \"$$ref\": \"#/definitions/OrderCreateRequest对象\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/confirm", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7284, "method": "POST", "title": "确认订单", "path": "/api/front/order/confirm", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc30", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc32", "name": "cartIds", "desc": "cartIds"}, {"required": "1", "_id": "5f35eb95bc7e8e560914fc31", "name": "isNew", "desc": "isNew"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"addressInfo\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": {\n              \"type\": \"string\",\n              \"description\": \"收货人所在市\"\n            },\n            \"cityId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"城市id\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"detail\": {\n              \"type\": \"string\",\n              \"description\": \"收货人详细地址\"\n            },\n            \"district\": {\n              \"type\": \"string\",\n              \"description\": \"收货人所在区\"\n            },\n            \"id\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户地址id\"\n            },\n            \"isDefault\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否默认\"\n            },\n            \"isDel\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否删除\"\n            },\n            \"latitude\": {\n              \"type\": \"string\",\n              \"description\": \"纬度\"\n            },\n            \"longitude\": {\n              \"type\": \"string\",\n              \"description\": \"经度\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"收货人电话\"\n            },\n            \"postCode\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"邮编\"\n            },\n            \"province\": {\n              \"type\": \"string\",\n              \"description\": \"收货人所在省\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"收货人姓名\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            }\n          },\n          \"title\": \"UserAddress对象\",\n          \"description\": \"用户地址表\",\n          \"$$ref\": \"#/definitions/UserAddress对象\"\n        },\n        \"cartInfo\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"addTime\": {\n                \"type\": \"string\"\n              },\n              \"attrStatus\": {\n                \"type\": \"boolean\",\n                \"description\": \"商品是否有效\"\n              },\n              \"bargainId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"砍价id\"\n              },\n              \"brokerage\": {\n                \"type\": \"number\",\n                \"description\": \"一级分佣\"\n              },\n              \"brokerageTwo\": {\n                \"type\": \"number\",\n                \"description\": \"二级分佣\"\n              },\n              \"cartNum\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品数量\"\n              },\n              \"combinationId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"拼团id\"\n              },\n              \"costPrice\": {\n                \"type\": \"number\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int64\",\n                \"description\": \"购物车表ID\"\n              },\n              \"isNew\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否为立即购买\"\n              },\n              \"isReply\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"productAttrUnique\": {\n                \"type\": \"string\",\n                \"description\": \"商品属性\"\n              },\n              \"productId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品ID\"\n              },\n              \"productInfo\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"attrInfo\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"attrValue\": {\n                        \"type\": \"string\",\n                        \"description\": \"产品属性值和属性名对应关系\"\n                      },\n                      \"barCode\": {\n                        \"type\": \"string\",\n                        \"description\": \"商品条码\"\n                      },\n                      \"brokerage\": {\n                        \"type\": \"number\",\n                        \"description\": \"一级返佣\"\n                      },\n                      \"brokerageTwo\": {\n                        \"type\": \"number\",\n                        \"description\": \"二级返佣\"\n                      },\n                      \"cost\": {\n                        \"type\": \"number\",\n                        \"description\": \"成本价\"\n                      },\n                      \"id\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"attrId\"\n                      },\n                      \"image\": {\n                        \"type\": \"string\",\n                        \"description\": \"图片\"\n                      },\n                      \"otPrice\": {\n                        \"type\": \"number\",\n                        \"description\": \"原价\"\n                      },\n                      \"price\": {\n                        \"type\": \"number\",\n                        \"description\": \"属性金额\"\n                      },\n                      \"productId\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"商品ID\"\n                      },\n                      \"quota\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动限购数量\"\n                      },\n                      \"quotaShow\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动限购数量显示\"\n                      },\n                      \"sales\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"销量\"\n                      },\n                      \"stock\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"属性对应的库存\"\n                      },\n                      \"suk\": {\n                        \"type\": \"string\",\n                        \"description\": \"商品属性索引值 (attr_value|attr_value[|....])\"\n                      },\n                      \"type\": {\n                        \"type\": \"integer\",\n                        \"format\": \"int32\",\n                        \"description\": \"活动类型 0=商品，1=秒杀，2=砍价，3=拼团\"\n                      },\n                      \"unique\": {\n                        \"type\": \"string\",\n                        \"description\": \"唯一值\"\n                      },\n                      \"volume\": {\n                        \"type\": \"number\",\n                        \"description\": \"体积\"\n                      },\n                      \"weight\": {\n                        \"type\": \"number\",\n                        \"description\": \"重量\"\n                      }\n                    },\n                    \"title\": \"StoreProductAttrValue对象\",\n                    \"description\": \"商品属性值表\",\n                    \"$$ref\": \"#/definitions/StoreProductAttrValue对象\"\n                  },\n                  \"barCode\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品条码（一维码）\"\n                  },\n                  \"cateId\": {\n                    \"type\": \"string\",\n                    \"description\": \"分类id\"\n                  },\n                  \"cost\": {\n                    \"type\": \"number\",\n                    \"description\": \"成本价\"\n                  },\n                  \"giveIntegral\": {\n                    \"type\": \"number\",\n                    \"description\": \"获得积分\"\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"isPostage\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否包邮\"\n                  },\n                  \"isSub\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"是否单独分佣\"\n                  },\n                  \"keyword\": {\n                    \"type\": \"string\",\n                    \"description\": \"关键字\"\n                  },\n                  \"merId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"postage\": {\n                    \"type\": \"number\",\n                    \"description\": \"邮费\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sliderImage\": {\n                    \"type\": \"string\",\n                    \"description\": \"轮播图\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeInfo\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品简介\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"tempId\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"运费模板ID\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  },\n                  \"vipPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"会员价格\"\n                  }\n                },\n                \"title\": \"StoreProductCartProductInfoResponse对象\",\n                \"description\": \"商品信息，购物车列表使用\",\n                \"$$ref\": \"#/definitions/StoreProductCartProductInfoResponse对象\"\n              },\n              \"seckillId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"秒杀商品ID\"\n              },\n              \"truePrice\": {\n                \"type\": \"number\"\n              },\n              \"trueStock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"type\": {\n                \"type\": \"string\",\n                \"description\": \"类型\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户ID\"\n              },\n              \"vipTruePrice\": {\n                \"type\": \"number\"\n              }\n            },\n            \"title\": \"StoreCartResponse\",\n            \"description\": \"购物车ListResponse\",\n            \"$$ref\": \"#/definitions/StoreCartResponse\"\n          }\n        },\n        \"deduction\": {\n          \"type\": \"boolean\"\n        },\n        \"integralRatio\": {\n          \"type\": \"string\"\n        },\n        \"offlinePayStatus\": {\n          \"type\": \"string\"\n        },\n        \"offlinePostage\": {\n          \"type\": \"string\"\n        },\n        \"orderKey\": {\n          \"type\": \"string\"\n        },\n        \"other\": {\n          \"type\": \"object\",\n          \"additionalProperties\": {\n            \"type\": \"object\"\n          }\n        },\n        \"payWeixinOpen\": {\n          \"type\": \"string\"\n        },\n        \"priceGroup\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"costPrice\": {\n              \"type\": \"number\"\n            },\n            \"couponPrice\": {\n              \"type\": \"number\"\n            },\n            \"deductionPrice\": {\n              \"type\": \"number\"\n            },\n            \"payPostage\": {\n              \"type\": \"number\"\n            },\n            \"payPrice\": {\n              \"type\": \"number\"\n            },\n            \"storeFreePostage\": {\n              \"type\": \"number\"\n            },\n            \"storePostage\": {\n              \"type\": \"number\"\n            },\n            \"totalPrice\": {\n              \"type\": \"number\"\n            },\n            \"usedIntegral\": {\n              \"type\": \"number\"\n            },\n            \"vipPrice\": {\n              \"type\": \"number\"\n            }\n          },\n          \"title\": \"PriceGroupResponse\",\n          \"$$ref\": \"#/definitions/PriceGroupResponse\"\n        },\n        \"storeSelfMention\": {\n          \"type\": \"string\"\n        },\n        \"systemStore\": {\n          \"type\": \"string\"\n        },\n        \"usableCoupon\": {\n          \"type\": \"object\",\n          \"required\": [\n            \"primaryKey\"\n          ],\n          \"properties\": {\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"cid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"兑换的项目id\"\n            },\n            \"couponId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"优惠券发布id\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"endTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"过期时间\"\n            },\n            \"id\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"id\"\n            },\n            \"isValid\": {\n              \"type\": \"boolean\",\n              \"description\": \"用户头像\"\n            },\n            \"minPrice\": {\n              \"type\": \"number\",\n              \"description\": \"最低消费多少金额可用优惠券\"\n            },\n            \"money\": {\n              \"type\": \"number\",\n              \"description\": \"优惠券的面值\"\n            },\n            \"name\": {\n              \"type\": \"string\",\n              \"description\": \"优惠券名称\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"primaryKey\": {\n              \"type\": \"string\",\n              \"description\": \"主键id 商品id/分类id\"\n            },\n            \"startTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"开始使用时间\"\n            },\n            \"status\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"状态（0：未使用，1：已使用, 2:已失效）\"\n            },\n            \"type\": {\n              \"type\": \"string\",\n              \"description\": \"获取方式\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"领取人id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"更新时间\"\n            },\n            \"useTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"使用时间\"\n            },\n            \"useType\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"使用类型 1 全场通用, 2 商品券, 3 品类券\"\n            }\n          },\n          \"title\": \"StoreCouponUserResponse对象\",\n          \"description\": \"优惠券记录表\",\n          \"$$ref\": \"#/definitions/StoreCouponUserResponse对象\"\n        },\n        \"userInfo\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"account\": {\n              \"type\": \"string\",\n              \"description\": \"用户账号\"\n            },\n            \"addIp\": {\n              \"type\": \"string\",\n              \"description\": \"添加ip\"\n            },\n            \"addres\": {\n              \"type\": \"string\",\n              \"description\": \"详细地址\"\n            },\n            \"adminid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"管理员编号 \"\n            },\n            \"avatar\": {\n              \"type\": \"string\",\n              \"description\": \"用户头像\"\n            },\n            \"birthday\": {\n              \"type\": \"string\",\n              \"description\": \"生日\"\n            },\n            \"brokeragePrice\": {\n              \"type\": \"number\",\n              \"description\": \"佣金金额\"\n            },\n            \"cardId\": {\n              \"type\": \"string\",\n              \"description\": \"身份证号码\"\n            },\n            \"cleanTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"createTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"experience\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户剩余经验\"\n            },\n            \"groupId\": {\n              \"type\": \"string\",\n              \"description\": \"用户分组id\"\n            },\n            \"integral\": {\n              \"type\": \"number\",\n              \"description\": \"用户剩余积分\"\n            },\n            \"isPromoter\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否为推广员\"\n            },\n            \"lastIp\": {\n              \"type\": \"string\",\n              \"description\": \"最后一次登录ip\"\n            },\n            \"lastLoginTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"最后一次登录时间\"\n            },\n            \"level\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"等级\"\n            },\n            \"loginType\": {\n              \"type\": \"string\",\n              \"description\": \"用户登陆类型，h5,wechat,routine\"\n            },\n            \"mark\": {\n              \"type\": \"string\",\n              \"description\": \"用户备注\"\n            },\n            \"nickname\": {\n              \"type\": \"string\",\n              \"description\": \"用户昵称\"\n            },\n            \"nowMoney\": {\n              \"type\": \"number\",\n              \"description\": \"用户余额\"\n            },\n            \"partnerId\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"合伙人id\"\n            },\n            \"path\": {\n              \"type\": \"string\",\n              \"description\": \"用户推广等级\"\n            },\n            \"payCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户购买次数\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"手机号码\"\n            },\n            \"realName\": {\n              \"type\": \"string\",\n              \"description\": \"真实姓名\"\n            },\n            \"signNum\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"连续签到天数\"\n            },\n            \"spreadCount\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"下级人数\"\n            },\n            \"spreadTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"推广员关联时间\"\n            },\n            \"spreadUid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"推广人id\"\n            },\n            \"status\": {\n              \"type\": \"boolean\",\n              \"description\": \"1为正常，0为禁止\"\n            },\n            \"subscribe\": {\n              \"type\": \"boolean\",\n              \"description\": \"是否关注公众号\"\n            },\n            \"tagId\": {\n              \"type\": \"string\",\n              \"description\": \"用户标签id\"\n            },\n            \"uid\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\",\n              \"description\": \"用户id\"\n            },\n            \"updateTime\": {\n              \"type\": \"string\",\n              \"format\": \"date-time\",\n              \"description\": \"创建时间\"\n            },\n            \"userType\": {\n              \"type\": \"string\",\n              \"description\": \"用户类型\"\n            }\n          },\n          \"title\": \"User对象\",\n          \"description\": \"用户表\",\n          \"$$ref\": \"#/definitions/User对象\"\n        },\n        \"yuePayStatus\": {\n          \"type\": \"string\"\n        }\n      },\n      \"title\": \"ConfirmOrderResponse\",\n      \"$$ref\": \"#/definitions/ConfirmOrderResponse\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«ConfirmOrderResponse»\",\n  \"$$ref\": \"#/definitions/CommonResult«ConfirmOrderResponse»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/computed/{key}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7282, "method": "POST", "title": "计算价格", "path": "/api/front/order/computed/{key}", "req_params": [{"_id": "5f35eb95bc7e8e560914fc2e", "name": "key", "desc": "key"}], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc2f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"addressId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"地址id\"\n    },\n    \"couponId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"优惠券id\"\n    },\n    \"payType\": {\n      \"type\": \"string\",\n      \"description\": \"支付类型\"\n    },\n    \"shippingType\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"快递类型\"\n    },\n    \"useIntegral\": {\n      \"type\": \"boolean\",\n      \"description\": \"抵扣积分\"\n    }\n  },\n  \"title\": \"OrderComputedRequest\",\n  \"$$ref\": \"#/definitions/OrderComputedRequest\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7296, "method": "GET", "title": "订单列表", "path": "/api/front/order/list", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb96bc7e8e560914fc3b", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb96bc7e8e560914fc3a", "name": "page", "desc": "页码"}, {"required": "1", "_id": "5f35eb96bc7e8e560914fc39", "name": "type", "desc": "评价等级|0=未支付,1=待发货,2=待收货,3=待评价,4=已完成"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"addTime\": {\n            \"type\": \"string\"\n          },\n          \"cartInfo\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"info\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"addTime\": {\n                      \"type\": \"string\"\n                    },\n                    \"attrStatus\": {\n                      \"type\": \"boolean\",\n                      \"description\": \"商品是否有效\"\n                    },\n                    \"bargainId\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"砍价id\"\n                    },\n                    \"brokerage\": {\n                      \"type\": \"number\",\n                      \"description\": \"一级分佣\"\n                    },\n                    \"brokerageTwo\": {\n                      \"type\": \"number\",\n                      \"description\": \"二级分佣\"\n                    },\n                    \"cartNum\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"商品数量\"\n                    },\n                    \"combinationId\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"拼团id\"\n                    },\n                    \"costPrice\": {\n                      \"type\": \"number\"\n                    },\n                    \"id\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int64\",\n                      \"description\": \"购物车表ID\"\n                    },\n                    \"isNew\": {\n                      \"type\": \"boolean\",\n                      \"description\": \"是否为立即购买\"\n                    },\n                    \"isReply\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    },\n                    \"productAttrUnique\": {\n                      \"type\": \"string\",\n                      \"description\": \"商品属性\"\n                    },\n                    \"productId\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"商品ID\"\n                    },\n                    \"productInfo\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"attrInfo\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"attrValue\": {\n                              \"type\": \"string\",\n                              \"description\": \"产品属性值和属性名对应关系\"\n                            },\n                            \"barCode\": {\n                              \"type\": \"string\",\n                              \"description\": \"商品条码\"\n                            },\n                            \"brokerage\": {\n                              \"type\": \"number\",\n                              \"description\": \"一级返佣\"\n                            },\n                            \"brokerageTwo\": {\n                              \"type\": \"number\",\n                              \"description\": \"二级返佣\"\n                            },\n                            \"cost\": {\n                              \"type\": \"number\",\n                              \"description\": \"成本价\"\n                            },\n                            \"id\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"attrId\"\n                            },\n                            \"image\": {\n                              \"type\": \"string\",\n                              \"description\": \"图片\"\n                            },\n                            \"otPrice\": {\n                              \"type\": \"number\",\n                              \"description\": \"原价\"\n                            },\n                            \"price\": {\n                              \"type\": \"number\",\n                              \"description\": \"属性金额\"\n                            },\n                            \"productId\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"商品ID\"\n                            },\n                            \"quota\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"活动限购数量\"\n                            },\n                            \"quotaShow\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"活动限购数量显示\"\n                            },\n                            \"sales\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"销量\"\n                            },\n                            \"stock\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"属性对应的库存\"\n                            },\n                            \"suk\": {\n                              \"type\": \"string\",\n                              \"description\": \"商品属性索引值 (attr_value|attr_value[|....])\"\n                            },\n                            \"type\": {\n                              \"type\": \"integer\",\n                              \"format\": \"int32\",\n                              \"description\": \"活动类型 0=商品，1=秒杀，2=砍价，3=拼团\"\n                            },\n                            \"unique\": {\n                              \"type\": \"string\",\n                              \"description\": \"唯一值\"\n                            },\n                            \"volume\": {\n                              \"type\": \"number\",\n                              \"description\": \"体积\"\n                            },\n                            \"weight\": {\n                              \"type\": \"number\",\n                              \"description\": \"重量\"\n                            }\n                          },\n                          \"title\": \"StoreProductAttrValue对象\",\n                          \"description\": \"商品属性值表\",\n                          \"$$ref\": \"#/definitions/StoreProductAttrValue对象\"\n                        },\n                        \"barCode\": {\n                          \"type\": \"string\",\n                          \"description\": \"商品条码（一维码）\"\n                        },\n                        \"cateId\": {\n                          \"type\": \"string\",\n                          \"description\": \"分类id\"\n                        },\n                        \"cost\": {\n                          \"type\": \"number\",\n                          \"description\": \"成本价\"\n                        },\n                        \"giveIntegral\": {\n                          \"type\": \"number\",\n                          \"description\": \"获得积分\"\n                        },\n                        \"id\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"商品id\"\n                        },\n                        \"image\": {\n                          \"type\": \"string\",\n                          \"description\": \"商品图片\"\n                        },\n                        \"isPostage\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否包邮\"\n                        },\n                        \"isSub\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否单独分佣\"\n                        },\n                        \"keyword\": {\n                          \"type\": \"string\",\n                          \"description\": \"关键字\"\n                        },\n                        \"merId\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)\"\n                        },\n                        \"otPrice\": {\n                          \"type\": \"number\",\n                          \"description\": \"市场价\"\n                        },\n                        \"postage\": {\n                          \"type\": \"number\",\n                          \"description\": \"邮费\"\n                        },\n                        \"price\": {\n                          \"type\": \"number\",\n                          \"description\": \"商品价格\"\n                        },\n                        \"sales\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"销量\"\n                        },\n                        \"sliderImage\": {\n                          \"type\": \"string\",\n                          \"description\": \"轮播图\"\n                        },\n                        \"sort\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"排序\"\n                        },\n                        \"stock\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"库存\"\n                        },\n                        \"storeInfo\": {\n                          \"type\": \"string\",\n                          \"description\": \"商品简介\"\n                        },\n                        \"storeName\": {\n                          \"type\": \"string\",\n                          \"description\": \"商品名称\"\n                        },\n                        \"tempId\": {\n                          \"type\": \"integer\",\n                          \"format\": \"int32\",\n                          \"description\": \"运费模板ID\"\n                        },\n                        \"unitName\": {\n                          \"type\": \"string\",\n                          \"description\": \"单位名\"\n                        },\n                        \"vipPrice\": {\n                          \"type\": \"number\",\n                          \"description\": \"会员价格\"\n                        }\n                      },\n                      \"title\": \"StoreProductCartProductInfoResponse对象\",\n                      \"description\": \"商品信息，购物车列表使用\",\n                      \"$$ref\": \"#/definitions/StoreProductCartProductInfoResponse对象\"\n                    },\n                    \"seckillId\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"秒杀商品ID\"\n                    },\n                    \"truePrice\": {\n                      \"type\": \"number\"\n                    },\n                    \"trueStock\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    },\n                    \"type\": {\n                      \"type\": \"string\",\n                      \"description\": \"类型\"\n                    },\n                    \"uid\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\",\n                      \"description\": \"用户ID\"\n                    },\n                    \"vipTruePrice\": {\n                      \"type\": \"number\"\n                    }\n                  },\n                  \"title\": \"StoreCartResponse\",\n                  \"description\": \"购物车ListResponse\",\n                  \"$$ref\": \"#/definitions/StoreCartResponse\"\n                },\n                \"orderId\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\",\n                  \"description\": \"订单id\"\n                },\n                \"productId\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\",\n                  \"description\": \"商品ID\"\n                },\n                \"unique\": {\n                  \"type\": \"string\",\n                  \"description\": \"唯一id\"\n                }\n              },\n              \"title\": \"StoreOrderInfoVo对象\",\n              \"description\": \"订单购物详情表\",\n              \"$$ref\": \"#/definitions/StoreOrderInfoVo对象\"\n            }\n          },\n          \"offlinePayStatus\": {\n            \"type\": \"integer\",\n            \"format\": \"int32\"\n          },\n          \"payTime\": {\n            \"type\": \"string\"\n          },\n          \"status\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"deliveryType\": {\n                \"type\": \"string\"\n              },\n              \"msg\": {\n                \"type\": \"string\"\n              },\n              \"payType\": {\n                \"type\": \"string\"\n              },\n              \"title\": {\n                \"type\": \"string\"\n              },\n              \"type\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            },\n            \"title\": \"OrderAgainItemVo\",\n            \"$$ref\": \"#/definitions/OrderAgainItemVo\"\n          },\n          \"statusPic\": {\n            \"type\": \"string\"\n          },\n          \"storeOrder\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"backIntegral\": {\n                \"type\": \"number\",\n                \"description\": \"给用户退了多少积分\"\n              },\n              \"bargainId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"砍价id\"\n              },\n              \"clerkId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"店员id\"\n              },\n              \"combinationId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"拼团商品id0一般商品\"\n              },\n              \"cost\": {\n                \"type\": \"number\",\n                \"description\": \"成本价\"\n              },\n              \"couponId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"优惠券id\"\n              },\n              \"couponPrice\": {\n                \"type\": \"number\",\n                \"description\": \"优惠券金额\"\n              },\n              \"createTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"创建时间\"\n              },\n              \"deductionPrice\": {\n                \"type\": \"number\",\n                \"description\": \"抵扣金额\"\n              },\n              \"deliveryId\": {\n                \"type\": \"string\",\n                \"description\": \"快递单号/手机号\"\n              },\n              \"deliveryName\": {\n                \"type\": \"string\",\n                \"description\": \"快递名称/送货人姓名\"\n              },\n              \"deliveryType\": {\n                \"type\": \"string\",\n                \"description\": \"发货类型\"\n              },\n              \"freightPrice\": {\n                \"type\": \"number\",\n                \"description\": \"运费金额\"\n              },\n              \"gainIntegral\": {\n                \"type\": \"number\",\n                \"description\": \"消费赚取积分\"\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"订单ID\"\n              },\n              \"isChannel\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"支付渠道(0微信公众号1微信小程序)\"\n              },\n              \"isDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"是否删除\"\n              },\n              \"isMerCheck\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              },\n              \"isRemind\": {\n                \"type\": \"boolean\",\n                \"description\": \"消息提醒\"\n              },\n              \"isSystemDel\": {\n                \"type\": \"boolean\",\n                \"description\": \"后台是否删除\"\n              },\n              \"mark\": {\n                \"type\": \"string\",\n                \"description\": \"备注\"\n              },\n              \"merId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商户ID\"\n              },\n              \"orderId\": {\n                \"type\": \"string\",\n                \"description\": \"订单号\"\n              },\n              \"paid\": {\n                \"type\": \"boolean\",\n                \"description\": \"支付状态\"\n              },\n              \"payPostage\": {\n                \"type\": \"number\",\n                \"description\": \"支付邮费\"\n              },\n              \"payPrice\": {\n                \"type\": \"number\",\n                \"description\": \"实际支付金额\"\n              },\n              \"payTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"支付时间\"\n              },\n              \"payType\": {\n                \"type\": \"string\",\n                \"description\": \"支付方式\"\n              },\n              \"pinkId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"拼团id 0没有拼团\"\n              },\n              \"realName\": {\n                \"type\": \"string\",\n                \"description\": \"用户姓名\"\n              },\n              \"refundPrice\": {\n                \"type\": \"number\",\n                \"description\": \"退款金额\"\n              },\n              \"refundReason\": {\n                \"type\": \"string\",\n                \"description\": \"不退款的理由\"\n              },\n              \"refundReasonTime\": {\n                \"type\": \"string\",\n                \"format\": \"date-time\",\n                \"description\": \"退款时间\"\n              },\n              \"refundReasonWap\": {\n                \"type\": \"string\",\n                \"description\": \"前台退款原因\"\n              },\n              \"refundReasonWapExplain\": {\n                \"type\": \"string\",\n                \"description\": \"退款用户说明\"\n              },\n              \"refundReasonWapImg\": {\n                \"type\": \"string\",\n                \"description\": \"退款图片\"\n              },\n              \"refundStatus\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"0 未退款 1 申请中 2 已退款\"\n              },\n              \"remark\": {\n                \"type\": \"string\",\n                \"description\": \"管理员备注\"\n              },\n              \"seckillId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"秒杀商品ID\"\n              },\n              \"shippingType\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"配送方式 1=快递 ，2=门店自提\"\n              },\n              \"status\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货，待评价；3：已完成；）\"\n              },\n              \"storeId\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"门店id\"\n              },\n              \"totalNum\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"订单商品总数\"\n              },\n              \"totalPostage\": {\n                \"type\": \"number\",\n                \"description\": \"邮费\"\n              },\n              \"totalPrice\": {\n                \"type\": \"number\",\n                \"description\": \"订单总价\"\n              },\n              \"uid\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"用户id\"\n              },\n              \"unique\": {\n                \"type\": \"string\",\n                \"description\": \"唯一id(md5加密)类似id\"\n              },\n              \"useIntegral\": {\n                \"type\": \"number\",\n                \"description\": \"使用积分\"\n              },\n              \"userAddress\": {\n                \"type\": \"string\",\n                \"description\": \"详细地址\"\n              },\n              \"userPhone\": {\n                \"type\": \"string\",\n                \"description\": \"用户电话\"\n              },\n              \"verifyCode\": {\n                \"type\": \"string\",\n                \"description\": \"核销码\"\n              }\n            },\n            \"title\": \"StoreOrder对象\",\n            \"description\": \"订单表\",\n            \"$$ref\": \"#/definitions/StoreOrder对象\"\n          }\n        },\n        \"title\": \"OrderAgainVo\",\n        \"$$ref\": \"#/definitions/OrderAgainVo\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«OrderAgainVo»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«OrderAgainVo»»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/cancel", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7278, "method": "POST", "title": "订单取消", "path": "/api/front/order/cancel", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc2b", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc2c", "name": "id", "desc": "id"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/data", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7288, "method": "GET", "title": "订单头部数量", "path": "/api/front/order/data", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/take", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7308, "method": "POST", "title": "订单收货", "path": "/api/front/order/take", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc40", "name": "Content-Type", "value": "application/json"}], "req_query": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc41", "name": "id", "desc": "id"}], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/detail/{orderId}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7292, "method": "GET", "title": "订单详情", "path": "/api/front/order/detail/{orderId}", "req_params": [{"_id": "5f35eb95bc7e8e560914fc37", "name": "orderId", "desc": "orderId"}], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/refund/reason", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7304, "method": "GET", "title": "订单退款理由", "path": "/api/front/order/refund/reason", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/refund", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7302, "method": "POST", "title": "订单退款申请", "path": "/api/front/order/refund", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc3e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"id\",\n    \"text\"\n  ],\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"订单id\"\n    },\n    \"refund_reason_wap_explain\": {\n      \"type\": \"string\",\n      \"description\": \"备注说明\"\n    },\n    \"refund_reason_wap_img\": {\n      \"type\": \"string\",\n      \"description\": \"退款凭证(多个图片请用,(英文逗号)隔开)\"\n    },\n    \"text\": {\n      \"type\": \"string\",\n      \"description\": \"退款原因\"\n    }\n  },\n  \"title\": \"OrderRefundApplyRequest对象\",\n  \"description\": \"订单申请退款\",\n  \"$$ref\": \"#/definitions/OrderRefundApplyRequest对象\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}, {"query_path": {"path": "/api/front/order/comment", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7280, "method": "POST", "title": "评价订单", "path": "/api/front/order/comment", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb95bc7e8e560914fc2d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"boolean\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«boolean»\",\n  \"$$ref\": \"#/definitions/CommonResult«boolean»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"comment\",\n    \"pics\",\n    \"productId\",\n    \"productScore\",\n    \"serviceScore\"\n  ],\n  \"properties\": {\n    \"comment\": {\n      \"type\": \"string\",\n      \"description\": \"评论内容\"\n    },\n    \"oid\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"订单ID， 移动端必须传递此参数\"\n    },\n    \"pics\": {\n      \"type\": \"string\",\n      \"description\": \"评论图片\"\n    },\n    \"productId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"商品id\"\n    },\n    \"productScore\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"example\": 5,\n      \"description\": \"商品分数\"\n    },\n    \"serviceScore\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"example\": 5,\n      \"description\": \"服务分数\"\n    },\n    \"unique\": {\n      \"type\": \"string\",\n      \"description\": \"商品 属性id\"\n    },\n    \"userId\": {\n      \"type\": \"integer\",\n      \"format\": \"int32\",\n      \"description\": \"用户id， 后端必须传递此参数\"\n    }\n  },\n  \"title\": \"StoreProductReplyAddRequest对象\",\n  \"description\": \"评论表\",\n  \"$$ref\": \"#/definitions/StoreProductReplyAddRequest对象\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369237, "up_time": 1597369237, "__v": 0}, {"query_path": {"path": "/api/front/order/refund/verify", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7306, "method": "POST", "title": "退款订单验证", "path": "/api/front/order/refund/verify", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "5f35eb96bc7e8e560914fc3f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«object»\",\n  \"$$ref\": \"#/definitions/CommonResult«object»\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"refund_reason_wap_explain\": {\n      \"type\": \"string\",\n      \"description\": \"退款备注说明\"\n    },\n    \"refund_reason_wap_img\": {\n      \"type\": \"string\",\n      \"description\": \"退款凭证图片\"\n    },\n    \"text\": {\n      \"type\": \"string\",\n      \"description\": \"退款原因\"\n    },\n    \"uni\": {\n      \"type\": \"string\",\n      \"description\": \"待退款订单\"\n    }\n  },\n  \"title\": \"OrderRefundVerifyRequest\",\n  \"$$ref\": \"#/definitions/OrderRefundVerifyRequest\"\n}", "project_id": 56, "catid": 5054, "uid": 93, "add_time": 1597369238, "up_time": 1597369238, "__v": 0}]}, {"index": 0, "name": "首页", "desc": "Index Controller", "add_time": 1597369233, "up_time": 1597369233, "list": [{"query_path": {"path": "/api/front/share", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7340, "method": "GET", "title": "分享配置", "path": "/api/front/share", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«HashMap«string,string»»\",\n  \"$$ref\": \"#/definitions/CommonResult«HashMap«string,string»»\"\n}", "project_id": 56, "catid": 5063, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/search/keyword", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7336, "method": "GET", "title": "热门搜索", "path": "/api/front/search/keyword", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"title\": \"HashMap«string,object»\",\n        \"additionalProperties\": {\n          \"type\": \"object\"\n        },\n        \"$$ref\": \"#/definitions/HashMap«string,object»\"\n      }\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«List«HashMap«string,object»»»\",\n  \"$$ref\": \"#/definitions/CommonResult«List«HashMap«string,object»»»\"\n}", "project_id": 56, "catid": 5063, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/groom/list/{type}", "params": []}, "edit_uid": 0, "status": "undone", "type": "var", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7260, "method": "GET", "title": "首页产品的轮播图和产品信息", "path": "/api/front/groom/list/{type}", "req_params": [{"_id": "5f35eb94bc7e8e560914fc23", "name": "type", "desc": "类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】"}], "req_body_form": [], "req_headers": [], "req_query": [{"required": "0", "_id": "5f35eb94bc7e8e560914fc25", "name": "limit", "desc": "每页数量"}, {"required": "0", "_id": "5f35eb94bc7e8e560914fc24", "name": "page", "desc": "页码"}], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"activity\": {\n          \"type\": \"array\",\n          \"description\": \"活动区域图片\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"banner\": {\n          \"type\": \"array\",\n          \"description\": \"首页banner滚动图\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"benefit\": {\n          \"type\": \"array\",\n          \"description\": \"首页促销单品\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"couponList\": {\n          \"type\": \"array\",\n          \"description\": \"优惠券\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"explosiveMoney\": {\n          \"type\": \"array\",\n          \"description\": \"首页超值爆款\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"info\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"bastBanner\": {\n              \"type\": \"array\",\n              \"description\": \"首页精品推荐图片\",\n              \"items\": {\n                \"type\": \"object\",\n                \"title\": \"HashMap«string,object»\",\n                \"additionalProperties\": {\n                  \"type\": \"object\"\n                },\n                \"$$ref\": \"#/definitions/HashMap«string,object»\"\n              }\n            },\n            \"bastInfo\": {\n              \"type\": \"string\",\n              \"description\": \"精品推荐简介\"\n            },\n            \"bastList\": {\n              \"type\": \"array\",\n              \"description\": \"精品推荐\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"cateId\": {\n                    \"type\": \"array\",\n                    \"description\": \"分类id\",\n                    \"items\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    }\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  }\n                },\n                \"title\": \"ProductResponse对象\",\n                \"description\": \"商品表\",\n                \"$$ref\": \"#/definitions/ProductResponse对象\"\n              }\n            },\n            \"bastNumber\": {\n              \"type\": \"string\",\n              \"description\": \"精品推荐个数\"\n            },\n            \"fastInfo\": {\n              \"type\": \"string\",\n              \"description\": \"快速选择简介\"\n            },\n            \"fastList\": {\n              \"type\": \"array\",\n              \"description\": \"分类\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"extra\": {\n                    \"type\": \"string\",\n                    \"description\": \"扩展字段\"\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\"\n                  },\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"分类名称\"\n                  },\n                  \"path\": {\n                    \"type\": \"string\",\n                    \"description\": \"路径\"\n                  },\n                  \"pid\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"父级ID\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"status\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"状态, 0正常，1失效\"\n                  },\n                  \"type\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"类型ID | 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置 \"\n                  },\n                  \"url\": {\n                    \"type\": \"string\",\n                    \"description\": \"地址\"\n                  }\n                },\n                \"title\": \"Category对象\",\n                \"description\": \"分类表\",\n                \"$$ref\": \"#/definitions/Category对象\"\n              }\n            },\n            \"fastNumber\": {\n              \"type\": \"string\",\n              \"description\": \"快速选择分类个数\"\n            },\n            \"firstInfo\": {\n              \"type\": \"string\",\n              \"description\": \"首发新品简介\"\n            },\n            \"firstList\": {\n              \"type\": \"array\",\n              \"description\": \"首发新品\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"cateId\": {\n                    \"type\": \"array\",\n                    \"description\": \"分类id\",\n                    \"items\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    }\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  }\n                },\n                \"title\": \"ProductResponse对象\",\n                \"description\": \"商品表\",\n                \"$$ref\": \"#/definitions/ProductResponse对象\"\n              }\n            },\n            \"firstNumber\": {\n              \"type\": \"string\",\n              \"description\": \"首发新品个数\"\n            },\n            \"promotionNumber\": {\n              \"type\": \"string\",\n              \"description\": \"首页促销单品\"\n            },\n            \"salesInfo\": {\n              \"type\": \"string\",\n              \"description\": \"促销单品简介\"\n            }\n          },\n          \"title\": \"IndexInfoItemResponse对象\",\n          \"description\": \"用户登录返回数据\",\n          \"$$ref\": \"#/definitions/IndexInfoItemResponse对象\"\n        },\n        \"likeInfo\": {\n          \"type\": \"array\",\n          \"description\": \"热门榜单\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"description\": \"商品\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"logoUrl\": {\n          \"type\": \"string\",\n          \"description\": \"企业logo\"\n        },\n        \"lovely\": {\n          \"type\": \"object\",\n          \"description\": \"首发新品广告图\",\n          \"additionalProperties\": {\n            \"type\": \"object\"\n          }\n        },\n        \"menus\": {\n          \"type\": \"array\",\n          \"description\": \"导航模块\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"roll\": {\n          \"type\": \"array\",\n          \"description\": \"新闻简报消息滚动\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"subscribe\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否关注\"\n        }\n      },\n      \"title\": \"IndexInfoResponse对象\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/IndexInfoResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«IndexInfoResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«IndexInfoResponse对象»\"\n}", "project_id": 56, "catid": 5063, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/front/index", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 7262, "method": "GET", "title": "首页数据", "path": "/api/front/index", "req_params": [], "req_body_form": [], "req_headers": [], "req_query": [], "req_body_type": "raw", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"activity\": {\n          \"type\": \"array\",\n          \"description\": \"活动区域图片\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"banner\": {\n          \"type\": \"array\",\n          \"description\": \"首页banner滚动图\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"benefit\": {\n          \"type\": \"array\",\n          \"description\": \"首页促销单品\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"couponList\": {\n          \"type\": \"array\",\n          \"description\": \"优惠券\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"explosiveMoney\": {\n          \"type\": \"array\",\n          \"description\": \"首页超值爆款\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"info\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"bastBanner\": {\n              \"type\": \"array\",\n              \"description\": \"首页精品推荐图片\",\n              \"items\": {\n                \"type\": \"object\",\n                \"title\": \"HashMap«string,object»\",\n                \"additionalProperties\": {\n                  \"type\": \"object\"\n                },\n                \"$$ref\": \"#/definitions/HashMap«string,object»\"\n              }\n            },\n            \"bastInfo\": {\n              \"type\": \"string\",\n              \"description\": \"精品推荐简介\"\n            },\n            \"bastList\": {\n              \"type\": \"array\",\n              \"description\": \"精品推荐\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"cateId\": {\n                    \"type\": \"array\",\n                    \"description\": \"分类id\",\n                    \"items\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    }\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  }\n                },\n                \"title\": \"ProductResponse对象\",\n                \"description\": \"商品表\",\n                \"$$ref\": \"#/definitions/ProductResponse对象\"\n              }\n            },\n            \"bastNumber\": {\n              \"type\": \"string\",\n              \"description\": \"精品推荐个数\"\n            },\n            \"fastInfo\": {\n              \"type\": \"string\",\n              \"description\": \"快速选择简介\"\n            },\n            \"fastList\": {\n              \"type\": \"array\",\n              \"description\": \"分类\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"extra\": {\n                    \"type\": \"string\",\n                    \"description\": \"扩展字段\"\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\"\n                  },\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"分类名称\"\n                  },\n                  \"path\": {\n                    \"type\": \"string\",\n                    \"description\": \"路径\"\n                  },\n                  \"pid\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"父级ID\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"status\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"状态, 0正常，1失效\"\n                  },\n                  \"type\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"类型ID | 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置 \"\n                  },\n                  \"url\": {\n                    \"type\": \"string\",\n                    \"description\": \"地址\"\n                  }\n                },\n                \"title\": \"Category对象\",\n                \"description\": \"分类表\",\n                \"$$ref\": \"#/definitions/Category对象\"\n              }\n            },\n            \"fastNumber\": {\n              \"type\": \"string\",\n              \"description\": \"快速选择分类个数\"\n            },\n            \"firstInfo\": {\n              \"type\": \"string\",\n              \"description\": \"首发新品简介\"\n            },\n            \"firstList\": {\n              \"type\": \"array\",\n              \"description\": \"首发新品\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"cateId\": {\n                    \"type\": \"array\",\n                    \"description\": \"分类id\",\n                    \"items\": {\n                      \"type\": \"integer\",\n                      \"format\": \"int32\"\n                    }\n                  },\n                  \"id\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"商品id\"\n                  },\n                  \"image\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品图片\"\n                  },\n                  \"otPrice\": {\n                    \"type\": \"number\",\n                    \"description\": \"市场价\"\n                  },\n                  \"price\": {\n                    \"type\": \"number\",\n                    \"description\": \"商品价格\"\n                  },\n                  \"sales\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"销量\"\n                  },\n                  \"sort\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"排序\"\n                  },\n                  \"stock\": {\n                    \"type\": \"integer\",\n                    \"format\": \"int32\",\n                    \"description\": \"库存\"\n                  },\n                  \"storeName\": {\n                    \"type\": \"string\",\n                    \"description\": \"商品名称\"\n                  },\n                  \"unitName\": {\n                    \"type\": \"string\",\n                    \"description\": \"单位名\"\n                  }\n                },\n                \"title\": \"ProductResponse对象\",\n                \"description\": \"商品表\",\n                \"$$ref\": \"#/definitions/ProductResponse对象\"\n              }\n            },\n            \"firstNumber\": {\n              \"type\": \"string\",\n              \"description\": \"首发新品个数\"\n            },\n            \"promotionNumber\": {\n              \"type\": \"string\",\n              \"description\": \"首页促销单品\"\n            },\n            \"salesInfo\": {\n              \"type\": \"string\",\n              \"description\": \"促销单品简介\"\n            }\n          },\n          \"title\": \"IndexInfoItemResponse对象\",\n          \"description\": \"用户登录返回数据\",\n          \"$$ref\": \"#/definitions/IndexInfoItemResponse对象\"\n        },\n        \"likeInfo\": {\n          \"type\": \"array\",\n          \"description\": \"热门榜单\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"list\": {\n          \"type\": \"array\",\n          \"description\": \"商品\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"cateId\": {\n                \"type\": \"array\",\n                \"description\": \"分类id\",\n                \"items\": {\n                  \"type\": \"integer\",\n                  \"format\": \"int32\"\n                }\n              },\n              \"id\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"商品id\"\n              },\n              \"image\": {\n                \"type\": \"string\",\n                \"description\": \"商品图片\"\n              },\n              \"otPrice\": {\n                \"type\": \"number\",\n                \"description\": \"市场价\"\n              },\n              \"price\": {\n                \"type\": \"number\",\n                \"description\": \"商品价格\"\n              },\n              \"sales\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"销量\"\n              },\n              \"sort\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"排序\"\n              },\n              \"stock\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\",\n                \"description\": \"库存\"\n              },\n              \"storeName\": {\n                \"type\": \"string\",\n                \"description\": \"商品名称\"\n              },\n              \"unitName\": {\n                \"type\": \"string\",\n                \"description\": \"单位名\"\n              }\n            },\n            \"title\": \"ProductResponse对象\",\n            \"description\": \"商品表\",\n            \"$$ref\": \"#/definitions/ProductResponse对象\"\n          }\n        },\n        \"logoUrl\": {\n          \"type\": \"string\",\n          \"description\": \"企业logo\"\n        },\n        \"lovely\": {\n          \"type\": \"object\",\n          \"description\": \"首发新品广告图\",\n          \"additionalProperties\": {\n            \"type\": \"object\"\n          }\n        },\n        \"menus\": {\n          \"type\": \"array\",\n          \"description\": \"导航模块\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"roll\": {\n          \"type\": \"array\",\n          \"description\": \"新闻简报消息滚动\",\n          \"items\": {\n            \"type\": \"object\",\n            \"title\": \"HashMap«string,object»\",\n            \"additionalProperties\": {\n              \"type\": \"object\"\n            },\n            \"$$ref\": \"#/definitions/HashMap«string,object»\"\n          }\n        },\n        \"subscribe\": {\n          \"type\": \"boolean\",\n          \"description\": \"是否关注\"\n        }\n      },\n      \"title\": \"IndexInfoResponse对象\",\n      \"description\": \"用户登录返回数据\",\n      \"$$ref\": \"#/definitions/IndexInfoResponse对象\"\n    },\n    \"message\": {\n      \"type\": \"string\"\n    }\n  },\n  \"title\": \"CommonResult«IndexInfoResponse对象»\",\n  \"$$ref\": \"#/definitions/CommonResult«IndexInfoResponse对象»\"\n}", "project_id": 56, "catid": 5063, "uid": 93, "add_time": **********, "up_time": **********, "__v": 0}]}]