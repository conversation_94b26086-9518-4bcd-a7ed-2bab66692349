# 直播间管理功能使用指南

## 功能概述

直播间管理系统提供了完整的直播间运营管理功能，包括直播间管理、商品管理和消息管理三大核心模块。

## 功能模块

### 1. 直播间管理 (`/live`)

#### 1.1 功能特性
- **直播间列表**: 查看所有直播间的状态和基本信息
- **创建直播间**: 创建新的直播间
- **编辑直播间**: 修改直播间基本信息
- **开播管理**: 一键开始/结束直播
- **删除直播间**: 删除不需要的直播间

#### 1.2 操作说明
1. **查看直播间列表**
   - 显示直播间ID、名称、主播、状态、在线人数等信息
   - 支持按直播间名称搜索

2. **创建直播间**
   - 点击"创建直播间"按钮
   - 填写直播间名称、描述、主播信息等
   - 系统自动生成唯一的直播间ID

3. **开播/下播**
   - 未开播状态：显示"开播"按钮
   - 直播中状态：显示"下播"按钮
   - 已结束状态：可重新开播

4. **商品管理**
   - 点击"商品管理"进入商品管理页面
   - 管理直播间的商品卡片

5. **消息管理**
   - 点击"消息管理"进入消息管理页面
   - 查看和管理直播间聊天消息

### 2. 商品管理 (`/live/products/:roomId`)

#### 2.1 功能特性
- **商品列表**: 查看直播间内所有商品
- **添加商品**: 从现有商品库中选择添加
- **移除商品**: 从直播间移除商品
- **商品排序**: 调整商品显示顺序
- **状态管理**: 设置推荐、热卖状态
- **讲解管理**: 开始/结束商品讲解

#### 2.2 操作说明
1. **查看商品列表**
   - 显示商品图片、名称、价格、库存、销量等信息
   - 显示商品状态标签（讲解中、推荐、热卖）

2. **添加商品**
   - 点击"添加商品"按钮
   - 在弹窗中搜索和选择商品
   - 支持批量添加多个商品

3. **商品排序**
   - 直接修改排序数字
   - 数字越大排序越靠前
   - 实时保存排序设置

4. **商品讲解**
   - 点击"开始讲解"开始讲解商品
   - 同时只能有一个商品处于讲解状态
   - 开始新讲解会自动结束之前的讲解

5. **状态管理**
   - 通过"更多"下拉菜单设置推荐/热卖状态
   - 推荐商品显示橙色"推荐"标签
   - 热卖商品显示绿色"热卖"标签

6. **移除商品**
   - 通过"更多"菜单选择"移除商品"
   - 确认后从直播间移除（不删除商品本身）

### 3. 消息管理 (`/live/messages/:roomId`)

#### 3.1 功能特性
- **消息列表**: 查看直播间所有聊天消息
- **消息筛选**: 按消息类型筛选
- **消息搜索**: 按用户名或内容搜索
- **删除消息**: 单条或批量删除消息
- **清空消息**: 一键清空所有消息

#### 3.2 操作说明
1. **查看消息列表**
   - 显示用户头像、用户名、消息类型、内容、时间
   - 按时间倒序排列（最新消息在前）

2. **消息筛选**
   - 按消息类型筛选：全部、文本消息、系统消息、商品推荐
   - 按关键词搜索用户名或消息内容

3. **删除消息**
   - 单条删除：点击消息行的"删除"按钮
   - 批量删除：选择多条消息后点击"批量删除"
   - 清空消息：点击"清空消息"删除所有消息

## API接口说明

### 直播间管理接口
```
GET    /api/admin/live/room/list          - 获取直播间列表
GET    /api/admin/live/room/info/{roomId} - 获取直播间详情
POST   /api/admin/live/room/create        - 创建直播间
POST   /api/admin/live/room/update        - 更新直播间
DELETE /api/admin/live/room/delete/{roomId} - 删除直播间
POST   /api/admin/live/room/start/{roomId}  - 开始直播
POST   /api/admin/live/room/stop/{roomId}   - 结束直播
```

### 商品管理接口
```
GET  /api/admin/live/product/list/{roomId}      - 获取直播间商品列表
GET  /api/admin/live/product/available/{roomId} - 获取可添加商品列表
POST /api/admin/live/product/add                - 添加商品到直播间
POST /api/admin/live/product/remove             - 从直播间移除商品
POST /api/admin/live/product/sort               - 更新商品排序
POST /api/admin/live/product/recommend          - 更新推荐状态
POST /api/admin/live/product/hot                - 更新热卖状态
POST /api/admin/live/product/startExplain       - 开始讲解商品
POST /api/admin/live/product/stopExplain        - 结束讲解商品
```

### 消息管理接口
```
GET    /api/admin/live/message/list/{roomId}    - 获取消息列表
DELETE /api/admin/live/message/delete/{messageId} - 删除消息
POST   /api/admin/live/message/batchDelete      - 批量删除消息
POST   /api/admin/live/message/clear/{roomId}   - 清空直播间消息
```

## 权限配置

### 权限列表
```
admin:live:room:list     - 查看直播间列表
admin:live:room:info     - 查看直播间详情
admin:live:room:create   - 创建直播间
admin:live:room:update   - 更新直播间
admin:live:room:delete   - 删除直播间
admin:live:room:start    - 开始直播
admin:live:room:stop     - 结束直播

admin:live:product:list      - 查看商品列表
admin:live:product:available - 查看可添加商品
admin:live:product:add       - 添加商品
admin:live:product:remove    - 移除商品
admin:live:product:sort      - 商品排序
admin:live:product:recommend - 设置推荐状态
admin:live:product:hot       - 设置热卖状态
admin:live:product:explain   - 商品讲解管理

admin:live:message:list   - 查看消息列表
admin:live:message:delete - 删除消息
admin:live:message:clear  - 清空消息
```

## 使用流程

### 典型使用流程
1. **创建直播间**
   - 填写直播间基本信息
   - 设置主播信息

2. **配置商品**
   - 添加要推广的商品
   - 设置商品排序和状态
   - 准备商品讲解内容

3. **开始直播**
   - 点击"开播"按钮
   - 直播间状态变为"直播中"

4. **直播过程管理**
   - 实时查看在线人数
   - 管理聊天消息
   - 开始/结束商品讲解

5. **结束直播**
   - 点击"下播"按钮
   - 查看直播数据统计

## 注意事项

### 1. 商品讲解规则
- 同时只能有一个商品处于讲解状态
- 开始新讲解会自动结束之前的讲解
- 讲解状态会实时同步到前端用户

### 2. 消息管理
- 删除的消息无法恢复
- 清空操作会删除所有历史消息
- 建议定期备份重要消息

### 3. 权限控制
- 不同角色需要配置相应权限
- 建议按职责分配最小权限
- 定期审查权限配置

### 4. 性能优化
- 大量消息时建议分页查看
- 定期清理无用消息
- 监控系统性能指标

这个管理系统为直播运营提供了完整的后台管理功能，支持高效的直播间运营和商品管理。
