# 直播间商品管理优化文档

## 优化概述

按照秒杀创建页面的模式，重构了直播间商品管理的添加商品功能，实现了两步式添加流程，并增强了商品列表的管理功能。

## 核心优化内容

### 1. 两步式添加商品流程

#### 第一步：选择直播间商品
- **选择方式**: 使用`$modalGoodList`模态框选择商品（与秒杀创建页面一致）
- **支持多选**: 可以一次选择多个商品
- **已选展示**: 实时显示已选商品列表
- **移除功能**: 可以移除不需要的已选商品

```javascript
changeGood() {
  const _this = this
  this.$modalGoodList(function(rows) {
    // 处理选中的商品
    if (Array.isArray(rows)) {
      _this.selectedProducts = rows.map(row => ({
        id: row.id,
        image: row.image,
        storeName: row.storeName,
        price: row.price,
        otPrice: row.otPrice,
        stock: row.stock,
        sales: row.sales || 0
      }))
    }
  }, 'many', _this.selectedProducts) // 'many' 表示多选模式
}
```

#### 第二步：编辑商品基本属性
- **可编辑字段**:
  - 商品名称（必填）
  - 直播价格（必填，最小0.01）
  - 直播库存（必填，不超过原库存）
  - 排序权重（可选，默认0）

- **默认设置**:
  - 商品名称：使用原商品名称
  - 直播价格：使用原商品价格
  - 直播库存：默认不超过100，且不超过原库存
  - 商品状态：默认下架状态

### 2. 商品列表管理增强

#### 价格和库存实时编辑
```vue
<!-- 价格编辑 -->
<el-input-number
  v-model="row.productPrice"
  :min="0.01"
  :max="99999.99"
  :precision="2"
  size="mini"
  @change="handlePriceChange(row)"
/>

<!-- 库存编辑 -->
<el-input-number
  v-model="row.stock"
  :min="0"
  :max="row.maxStock || 9999"
  size="mini"
  @change="handleStockChange(row)"
/>
```

#### 状态管理优化
- **上架/下架状态**: 清晰显示商品当前状态
- **讲解状态**: 显示是否正在讲解
- **推荐/热卖标签**: 显示商品特殊状态

#### 操作按钮重新设计
```vue
<!-- 上架/下架 -->
<el-button v-if="row.status === 0" type="success" size="mini" @click="handleToggleStatus(row)">
  上架
</el-button>
<el-button v-if="row.status === 1" type="warning" size="mini" @click="handleToggleStatus(row)">
  下架
</el-button>

<!-- 讲解/结束讲解 -->
<el-button v-if="!row.isExplaining && row.status === 1" type="primary" size="mini" @click="handleStartExplain(row)">
  讲解
</el-button>
<el-button v-if="row.isExplaining" type="danger" size="mini" @click="handleStopExplain(row)">
  结束讲解
</el-button>

<!-- 更多操作 -->
<el-dropdown @command="handleCommand" trigger="click">
  <el-button type="info" size="mini">
    更多<i class="el-icon-arrow-down el-icon--right"></i>
  </el-button>
  <el-dropdown-menu slot="dropdown">
    <el-dropdown-item :command="{action: 'recommend', row: row}">
      {{ row.isRecommend ? '取消推荐' : '设为推荐' }}
    </el-dropdown-item>
    <el-dropdown-item :command="{action: 'hot', row: row}">
      {{ row.isHot ? '取消热卖' : '设为热卖' }}
    </el-dropdown-item>
    <el-dropdown-item :command="{action: 'remove', row: row}" divided>
      移除商品
    </el-dropdown-item>
  </el-dropdown-menu>
</el-dropdown>
```

### 3. 自动排序功能

#### 排序规则
- **优先级排序**: 按照sort字段自动排序
- **数字越大排序越靠前**: 与系统其他模块保持一致
- **实时更新**: 修改排序后立即生效

#### 排序实现
```javascript
handleSortChange(row) {
  updateProductSort(row.id, row.sort).then(() => {
    this.$message.success('排序更新成功')
    // 重新排序列表
    this.getList()
  })
}
```

### 4. 新增API接口

#### 商品属性更新接口
```javascript
// 更新商品价格
export function updateProductPrice(id, price) {
  return request({
    url: '/admin/live/product/updatePrice',
    method: 'post',
    data: { id, price }
  })
}

// 更新商品库存
export function updateProductStock(id, stock) {
  return request({
    url: '/admin/live/product/updateStock',
    method: 'post',
    data: { id, stock }
  })
}

// 更新商品状态（上架/下架）
export function updateProductStatus(id, status) {
  return request({
    url: '/admin/live/product/updateStatus',
    method: 'post',
    data: { id, status }
  })
}
```

#### 添加商品接口优化
```javascript
// 支持批量添加和属性设置
export function addProduct(data) {
  return request({
    url: '/admin/live/product/add',
    method: 'post',
    data: {
      roomId: data.roomId,
      productId: data.productId,
      productName: data.productName,
      productPrice: data.productPrice,
      stock: data.stock,
      sort: data.sort,
      status: data.status // 默认下架状态
    }
  })
}
```

## 用户体验优化

### 1. 界面设计
- **步骤条导航**: 清晰显示当前进度
- **卡片式布局**: 美观的商品信息展示
- **响应式设计**: 适配不同屏幕尺寸

### 2. 交互体验
- **实时反馈**: 操作后立即显示结果
- **防误操作**: 重要操作需要确认
- **批量操作**: 支持一次添加多个商品

### 3. 数据验证
- **必填字段检查**: 确保数据完整性
- **数值范围验证**: 防止无效数据
- **重复添加检测**: 避免重复添加同一商品

## 业务规则

### 1. 商品状态管理
- **新添加商品**: 默认为下架状态
- **讲解权限**: 只有上架商品才能开始讲解
- **同时讲解限制**: 同时只能有一个商品处于讲解状态

### 2. 库存管理
- **直播库存**: 不能超过商品原始库存
- **实时更新**: 支持随时调整库存数量
- **库存保护**: 最小库存为0

### 3. 价格管理
- **价格范围**: 最小0.01元，最大99999.99元
- **精度控制**: 支持两位小数
- **实时生效**: 价格修改立即生效

## 技术实现要点

### 1. 组件复用
- **商品选择器**: 复用系统现有的`$modalGoodList`组件
- **表单验证**: 使用Element UI的表单验证
- **步骤控制**: 参考秒杀创建页面的实现

### 2. 状态管理
- **响应式数据**: 使用Vue的响应式系统
- **状态同步**: 确保前端状态与后端一致
- **错误处理**: 完善的错误处理机制

### 3. 性能优化
- **按需加载**: 只在需要时加载商品数据
- **防抖处理**: 避免频繁的API调用
- **缓存策略**: 合理使用数据缓存

## 使用流程

### 添加商品流程
1. 点击"添加商品"按钮
2. 第一步：点击"选择商品"按钮，在模态框中选择商品
3. 查看已选商品列表，可移除不需要的商品
4. 点击"下一步"进入编辑阶段
5. 第二步：编辑每个商品的名称、价格、库存、排序
6. 点击"确认添加"完成添加

### 商品管理流程
1. 在商品列表中直接编辑价格和库存
2. 使用"上架/下架"按钮控制商品状态
3. 使用"讲解/结束讲解"按钮控制讲解状态
4. 通过"更多"菜单设置推荐、热卖状态或移除商品
5. 调整排序数字实现商品排序

这个优化方案完全按照秒杀创建页面的模式实现，提供了完整的商品管理功能，大大提升了用户体验和操作效率。
