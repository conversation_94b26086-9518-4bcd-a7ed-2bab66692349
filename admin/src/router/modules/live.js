/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const liveRouter = {
  path: '/live',
  component: Layout,
  redirect: '/live/index',
  name: 'Live',
  meta: {
    title: '直播管理',
    icon: 'video-camera'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/live/index'),
      name: 'LiveIndex',
      meta: { title: '直播间管理', noCache: true }
    },
    {
      path: ':roomId/products',
      component: () => import('@/views/live/products/index.vue'),
      name: 'LiveProducts',
      meta: { title: '商品管理', noCache: true },
      hidden: true
    },
    {
      path: 'messages/:roomId',
      component: () => import('@/views/live/messages'),
      name: 'LiveMessages',
      meta: { title: '消息管理', noCache: true },
      hidden: true
    }
  ]
}

export default liveRouter
