<template>
  <div class="app-container">
    <div class="filter-container">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/live' }">直播管理</el-breadcrumb-item>
        <el-breadcrumb-item>消息管理 - {{ roomId }}</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div style="margin-top: 20px;">
        <el-select
          v-model="listQuery.msgType"
          placeholder="消息类型"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="全部" value="" />
          <el-option label="文本消息" value="1" />
          <el-option label="系统消息" value="2" />
          <el-option label="商品推荐" value="4" />
        </el-select>
        <el-input
          v-model="listQuery.keywords"
          placeholder="请输入用户名或消息内容"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="warning"
          icon="el-icon-refresh"
          @click="getList"
        >
          刷新
        </el-button>
        <el-button
          class="filter-item"
          type="danger"
          icon="el-icon-delete"
          @click="handleClearAll"
        >
          清空消息
        </el-button>
      </div>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="用户头像" width="80px" align="center">
        <template slot-scope="{row}">
          <el-avatar :size="40" :src="row.avatar" icon="el-icon-user-solid" />
        </template>
      </el-table-column>
      <el-table-column label="用户名" width="120px">
        <template slot-scope="{row}">
          <span>{{ row.username || '匿名用户' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消息类型" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.msgType | msgTypeFilter">
            {{ row.msgType | msgTypeTextFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="消息内容" min-width="300px">
        <template slot-scope="{row}">
          <div class="message-content">
            <span v-if="row.msgType === 4" class="product-message">
              <i class="el-icon-goods"></i>
              {{ row.content }}
            </span>
            <span v-else>{{ row.content }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发送时间" width="160px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 批量操作 -->
    <div v-show="selectedMessages.length > 0" class="batch-actions">
      <el-button
        type="danger"
        size="small"
        @click="handleBatchDelete"
      >
        批量删除 ({{ selectedMessages.length }})
      </el-button>
    </div>
  </div>
</template>

<script>
import { getMessageList, deleteMessage, batchDeleteMessage, clearRoomMessage } from '@/api/live'
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'LiveMessageManage',
  components: { Pagination },
  directives: { waves },
  filters: {
    msgTypeFilter(msgType) {
      const typeMap = {
        1: '',
        2: 'info',
        3: 'success',
        4: 'warning'
      }
      return typeMap[msgType]
    },
    msgTypeTextFilter(msgType) {
      const typeMap = {
        1: '文本消息',
        2: '系统消息',
        3: '文本回复',
        4: '商品推荐'
      }
      return typeMap[msgType] || '未知'
    }
  },
  data() {
    return {
      roomId: '',
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        keywords: undefined,
        msgType: undefined
      },
      selectedMessages: []
    }
  },
  created() {
    this.roomId = this.$route.params.roomId
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getMessageList(this.roomId, this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSelectionChange(selection) {
      this.selectedMessages = selection
    },
    handleDelete(row) {
      this.$confirm('确认删除该消息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMessage(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '消息删除成功',
            type: 'success',
            duration: 2000
          })
          const index = this.list.findIndex(v => v.id === row.id)
          this.list.splice(index, 1)
          this.total--
        })
      })
    },
    handleBatchDelete() {
      if (this.selectedMessages.length === 0) {
        this.$message.warning('请选择要删除的消息')
        return
      }

      this.$confirm(`确认删除选中的 ${this.selectedMessages.length} 条消息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const messageIds = this.selectedMessages.map(msg => msg.id)
        batchDeleteMessage(messageIds).then(() => {
          this.$notify({
            title: '成功',
            message: `成功删除 ${this.selectedMessages.length} 条消息`,
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleClearAll() {
      this.$confirm('确认清空该直播间的所有消息? 此操作不可恢复!', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        clearRoomMessage(this.roomId).then(() => {
          this.$notify({
            title: '成功',
            message: '直播间消息已清空',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.message-content {
  max-width: 300px;
  word-break: break-word;
}

.product-message {
  color: #E6A23C;
  font-weight: bold;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
