<template>
  <div class="app-container">
    <div class="filter-container">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/live' }">直播管理</el-breadcrumb-item>
        <el-breadcrumb-item>商品管理 - {{ roomId }}</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div style="margin-top: 20px;">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddProduct"
        >
          添加商品
        </el-button>
        <el-button
          class="filter-item"
          type="warning"
          icon="el-icon-refresh"
          @click="getList"
        >
          刷新
        </el-button>
      </div>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="商品图片" width="100px" align="center">
        <template slot-scope="{row}">
          <img :src="row.productImage" style="width: 60px; height: 60px; object-fit: cover;">
        </template>
      </el-table-column>
      <el-table-column label="商品名称" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="价格" width="100px" align="center">
        <template slot-scope="{row}">
          <span style="color: #f56c6c;">¥{{ row.productPrice }}</span>
          <br>
          <span v-if="row.originalPrice > row.productPrice" style="color: #909399; text-decoration: line-through; font-size: 12px;">
            ¥{{ row.originalPrice }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="库存" width="80px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.stock }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销量" width="80px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.sales }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="100px" align="center" sortable="custom">
        <template slot-scope="{row}">
          <el-input-number
            v-model="row.sort"
            :min="0"
            :max="999"
            size="mini"
            @change="handleSortChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.isExplaining" type="danger" effect="plain">
            讲解中
          </el-tag>
          <el-tag v-if="row.isRecommend" type="warning" effect="plain">
            推荐
          </el-tag>
          <el-tag v-if="row.isHot" type="success" effect="plain">
            热卖
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button
            v-if="!row.isExplaining"
            type="primary"
            size="mini"
            @click="handleStartExplain(row)"
          >
            开始讲解
          </el-button>
          <el-button
            v-if="row.isExplaining"
            type="warning"
            size="mini"
            @click="handleStopExplain(row)"
          >
            结束讲解
          </el-button>
          <el-dropdown @command="handleCommand">
            <el-button type="info" size="mini">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{action: 'recommend', row: row}">
                {{ row.isRecommend ? '取消推荐' : '设为推荐' }}
              </el-dropdown-item>
              <el-dropdown-item :command="{action: 'hot', row: row}">
                {{ row.isHot ? '取消热卖' : '设为热卖' }}
              </el-dropdown-item>
              <el-dropdown-item :command="{action: 'remove', row: row}" divided>
                移除商品
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 添加商品对话框 -->
    <el-dialog title="添加商品" :visible.sync="addProductDialogVisible" width="80%">
      <div class="filter-container">
        <el-input
          v-model="productListQuery.keywords"
          placeholder="请输入商品名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="getAvailableProducts"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="getAvailableProducts"
        >
          搜索
        </el-button>
      </div>

      <el-table
        v-loading="productListLoading"
        :data="availableProducts"
        border
        fit
        highlight-current-row
        style="width: 100%; margin-top: 20px;"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品图片" width="100px" align="center">
          <template slot-scope="{row}">
            <img :src="row.image" style="width: 60px; height: 60px; object-fit: cover;">
          </template>
        </el-table-column>
        <el-table-column label="商品名称" min-width="200px">
          <template slot-scope="{row}">
            <span>{{ row.storeName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="价格" width="100px" align="center">
          <template slot-scope="{row}">
            <span style="color: #f56c6c;">¥{{ row.price }}</span>
            <br>
            <span v-if="row.otPrice > row.price" style="color: #909399; text-decoration: line-through; font-size: 12px;">
              ¥{{ row.otPrice }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="库存" width="80px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.stock }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销量" width="80px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.sales }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="productTotal>0"
        :total="productTotal"
        :page.sync="productListQuery.page"
        :limit.sync="productListQuery.limit"
        @pagination="getAvailableProducts"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="addProductDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmAddProducts" :disabled="selectedProducts.length === 0">
          确认添加 ({{ selectedProducts.length }})
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getProductList, 
  getAvailableProducts, 
  addProduct, 
  removeProduct, 
  updateProductSort,
  updateRecommendStatus,
  updateHotStatus,
  startExplaining,
  stopExplaining
} from '@/api/live'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'LiveProductManage',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      roomId: '',
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20
      },
      addProductDialogVisible: false,
      availableProducts: [],
      productTotal: 0,
      productListLoading: false,
      productListQuery: {
        page: 1,
        limit: 10,
        keywords: undefined
      },
      selectedProducts: []
    }
  },
  created() {
    this.roomId = this.$route.params.roomId
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getProductList(this.roomId, this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleAddProduct() {
      this.addProductDialogVisible = true
      this.getAvailableProducts()
    },
    getAvailableProducts() {
      this.productListLoading = true
      getAvailableProducts(this.roomId, this.productListQuery).then(response => {
        this.availableProducts = response.data.list
        this.productTotal = response.data.total
        this.productListLoading = false
      })
    },
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },
    confirmAddProducts() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请选择要添加的商品')
        return
      }

      const promises = this.selectedProducts.map(product => 
        addProduct(this.roomId, product.id)
      )

      Promise.all(promises).then(() => {
        this.$notify({
          title: '成功',
          message: `成功添加 ${this.selectedProducts.length} 个商品`,
          type: 'success',
          duration: 2000
        })
        this.addProductDialogVisible = false
        this.getList()
      }).catch(() => {
        this.$message.error('添加商品失败')
      })
    },
    handleSortChange(row) {
      updateProductSort(row.id, row.sort).then(() => {
        this.$message.success('排序更新成功')
      })
    },
    handleStartExplain(row) {
      startExplaining(this.roomId, row.productId).then(() => {
        this.$notify({
          title: '成功',
          message: '开始讲解商品',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleStopExplain(row) {
      stopExplaining(this.roomId, row.productId).then(() => {
        this.$notify({
          title: '成功',
          message: '结束讲解商品',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'recommend':
          this.toggleRecommend(row)
          break
        case 'hot':
          this.toggleHot(row)
          break
        case 'remove':
          this.handleRemoveProduct(row)
          break
      }
    },
    toggleRecommend(row) {
      updateRecommendStatus(row.id, !row.isRecommend).then(() => {
        row.isRecommend = !row.isRecommend
        this.$message.success('推荐状态更新成功')
      })
    },
    toggleHot(row) {
      updateHotStatus(row.id, !row.isHot).then(() => {
        row.isHot = !row.isHot
        this.$message.success('热卖状态更新成功')
      })
    },
    handleRemoveProduct(row) {
      this.$confirm('确认移除该商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeProduct(this.roomId, row.productId).then(() => {
          this.$notify({
            title: '成功',
            message: '商品移除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    sortChange(data) {
      const { column, prop, order } = data
      if (prop === 'sort') {
        this.listQuery.sort = order === 'ascending' ? '+sort' : '-sort'
        this.getList()
      }
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
</style>
