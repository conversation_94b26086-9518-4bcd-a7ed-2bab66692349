<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keywords"
        placeholder="请输入直播间名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        创建直播间
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="直播间ID" prop="roomId" align="center" width="120">
        <template slot-scope="{row}">
          <span>{{ row.roomId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="直播间名称" min-width="150px">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleUpdate(row)">{{ row.roomName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主播" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.anchorName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status | statusTextFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="在线人数" width="100px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.onlineCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="160px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.startTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button
            v-if="row.status === 0"
            type="success"
            size="mini"
            @click="handleStartLive(row)"
          >
            开播
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="warning"
            size="mini"
            @click="handleStopLive(row)"
          >
            下播
          </el-button>
          <el-button type="primary" size="mini" @click="handleManageProducts(row)">
            商品管理
          </el-button>
          <el-button type="info" size="mini" @click="handleManageMessages(row)">
            消息管理
          </el-button>
          <el-button
            v-if="row.status !== 1"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 创建/编辑直播间对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="直播间名称" prop="roomName">
          <el-input v-model="temp.roomName" />
        </el-form-item>
        <el-form-item label="直播间描述" prop="roomDesc">
          <el-input
            v-model="temp.roomDesc"
            :autosize="{ minRows: 2, maxRows: 4}"
            type="textarea"
            placeholder="请输入直播间描述"
          />
        </el-form-item>
        <el-form-item label="主播名称" prop="anchorName">
          <el-input v-model="temp.anchorName" />
        </el-form-item>
        <el-form-item label="主播头像" prop="anchorAvatar">
          <el-input v-model="temp.anchorAvatar" placeholder="请输入主播头像URL" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="temp.category" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRoomList, createRoom, updateRoom, deleteRoom, startLive, stopLive } from '@/api/live'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'LiveRoomList',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'warning'
      }
      return statusMap[status]
    },
    statusTextFilter(status) {
      const statusMap = {
        0: '未开播',
        1: '直播中',
        2: '已结束'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        keywords: undefined
      },
      temp: {
        id: undefined,
        roomId: '',
        roomName: '',
        roomDesc: '',
        anchorName: '',
        anchorAvatar: '',
        category: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑直播间',
        create: '创建直播间'
      },
      rules: {
        roomName: [{ required: true, message: '直播间名称是必填项', trigger: 'blur' }],
        anchorName: [{ required: true, message: '主播名称是必填项', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getRoomList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        roomId: '',
        roomName: '',
        roomDesc: '',
        anchorName: '',
        anchorAvatar: '',
        category: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createRoom(this.temp).then(() => {
            this.list.unshift(this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateRoom(tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该直播间?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRoom(row.roomId).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          const index = this.list.findIndex(v => v.id === row.id)
          this.list.splice(index, 1)
        })
      })
    },
    handleStartLive(row) {
      this.$confirm('确认开始直播?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        startLive(row.roomId).then(() => {
          this.$notify({
            title: '成功',
            message: '直播开始成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleStopLive(row) {
      this.$confirm('确认结束直播?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        stopLive(row.roomId).then(() => {
          this.$notify({
            title: '成功',
            message: '直播结束成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleManageProducts(row) {
      this.$router.push({ path: `/live/products/${row.roomId}` })
    },
    handleManageMessages(row) {
      this.$router.push({ path: `/live/messages/${row.roomId}` })
    }
  }
}
</script>
