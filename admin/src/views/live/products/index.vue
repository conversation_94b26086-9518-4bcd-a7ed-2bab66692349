<template>
  <div class="app-container">
    <div class="filter-container">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/live' }">直播管理</el-breadcrumb-item>
        <el-breadcrumb-item>商品管理 - {{ roomId }}</el-breadcrumb-item>
      </el-breadcrumb>

      <div style="margin-top: 20px;">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddProduct"
        >
          添加商品
        </el-button>
        <el-button
          class="filter-item"
          type="warning"
          icon="el-icon-refresh"
          @click="getList"
        >
          刷新
        </el-button>
      </div>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="商品图片" width="100px" align="center">
        <template slot-scope="{row}">
          <img :src="row.productImage" style="width: 60px; height: 60px; object-fit: cover;">
        </template>
      </el-table-column>
      <el-table-column label="商品名称" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="价格" width="120px" align="center">
        <template slot-scope="{row}">
          <el-input-number
            v-model="row.productPrice"
            :min="0.01"
            :max="99999.99"
            :precision="2"
            size="mini"
            style="width: 100px;"
            @change="handlePriceChange(row)"
          />
          <div v-if="row.originalPrice > row.productPrice" style="color: #909399; text-decoration: line-through; font-size: 12px; margin-top: 2px;">
            原价: ¥{{ row.originalPrice }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="库存" width="100px" align="center">
        <template slot-scope="{row}">
          <el-input-number
            v-model="row.stock"
            :min="0"
            :max="row.maxStock || 9999"
            size="mini"
            style="width: 80px;"
            @change="handleStockChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="销量" width="80px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.sales }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="100px" align="center" sortable="custom">
        <template slot-scope="{row}">
          <el-input-number
            v-model="row.sort"
            :min="0"
            :max="999"
            size="mini"
            @change="handleSortChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="150px" align="center">
        <template slot-scope="{row}">
          <div class="status-tags">
            <el-tag v-if="row.status === 1" type="success" size="mini">
              已上架
            </el-tag>
            <el-tag v-else type="info" size="mini">
              已下架
            </el-tag>
            <br style="margin: 2px 0;">
            <el-tag v-if="row.isExplaining" type="danger" effect="plain" size="mini">
              讲解中
            </el-tag>
            <el-tag v-if="row.isRecommend" type="warning" effect="plain" size="mini">
              推荐
            </el-tag>
            <el-tag v-if="row.isHot" type="success" effect="plain" size="mini">
              热卖
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="350px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <!-- 上架/下架 -->
          <el-button
            v-if="row.status === 0"
            type="success"
            size="mini"
            @click="handleToggleStatus(row)"
          >
            上架
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="warning"
            size="mini"
            @click="handleToggleStatus(row)"
          >
            下架
          </el-button>

          <!-- 讲解/结束讲解 -->
          <el-button
            v-if="!row.isExplaining && row.status === 1"
            type="primary"
            size="mini"
            @click="handleStartExplain(row)"
          >
            讲解
          </el-button>
          <el-button
            v-if="row.isExplaining"
            type="danger"
            size="mini"
            @click="handleStopExplain(row)"
          >
            结束讲解
          </el-button>

          <!-- 更多操作 -->
          <el-dropdown @command="handleCommand" trigger="click">
            <el-button type="info" size="mini">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{action: 'recommend', row: row}">
                <i class="el-icon-star-off"></i>
                {{ row.isRecommend ? '取消推荐' : '设为推荐' }}
              </el-dropdown-item>
              <el-dropdown-item :command="{action: 'hot', row: row}">
                <i class="el-icon-hot-water"></i>
                {{ row.isHot ? '取消热卖' : '设为热卖' }}
              </el-dropdown-item>
              <el-dropdown-item :command="{action: 'remove', row: row}" divided>
                <i class="el-icon-delete" style="color: #f56c6c;"></i>
                <span style="color: #f56c6c;">移除商品</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加商品对话框 - 步骤式 -->
    <el-dialog
      :title="currentStep === 0 ? '选择直播间商品' : '编辑商品属性'"
      :visible.sync="addProductDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <!-- 步骤条 -->
      <el-steps :active="currentStep" align-center finish-status="success" style="margin-bottom: 30px;">
        <el-step title="选择直播间商品" />
        <el-step title="编辑商品属性" />
      </el-steps>

      <!-- 第一步：选择商品 -->
      <div v-show="currentStep === 0">
        <div class="select-product-container">
          <div class="select-tip">
            <i class="el-icon-info"></i>
            <span>点击下方按钮选择要添加到直播间的商品，支持多选</span>
          </div>

          <!-- 已选商品展示区域 -->
          <div v-if="selectedProducts.length > 0" class="selected-products">
            <h4>已选商品 ({{ selectedProducts.length }})</h4>
            <div class="selected-product-list">
              <div
                v-for="(product, index) in selectedProducts"
                :key="product.id"
                class="selected-product-item"
              >
                <img :src="product.image" class="product-thumb" />
                <div class="product-info">
                  <div class="product-name">{{ product.storeName }}</div>
                  <div class="product-price">¥{{ product.price }}</div>
                </div>
                <el-button
                  type="text"
                  icon="el-icon-close"
                  @click="removeSelectedProduct(index)"
                  class="remove-btn"
                />
              </div>
            </div>
          </div>

          <!-- 选择商品按钮 -->
          <div class="select-button-container">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="large"
              @click="changeGood"
            >
              {{ selectedProducts.length > 0 ? '继续选择商品' : '选择商品' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 第二步：编辑商品属性 -->
      <div v-show="currentStep === 1">
        <div class="product-edit-container">
          <div v-for="(product, index) in selectedProductsForEdit" :key="product.id" class="product-edit-item">
            <el-card class="product-card" shadow="hover">
              <div class="product-header">
                <img :src="product.image" class="product-image" />
                <div class="product-basic-info">
                  <h4>{{ product.storeName }}</h4>
                  <p class="original-info">原价格: ¥{{ product.price }} | 原库存: {{ product.stock }}</p>
                </div>
              </div>

              <el-form :model="product" label-width="100px" class="product-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="商品名称" required>
                      <el-input
                        v-model="product.editName"
                        placeholder="请输入商品名称"
                        maxlength="100"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="直播价格" required>
                      <el-input-number
                        v-model="product.editPrice"
                        :min="0.01"
                        :max="99999.99"
                        :precision="2"
                        placeholder="请输入直播价格"
                        style="width: 100%;"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="直播库存" required>
                      <el-input-number
                        v-model="product.editStock"
                        :min="0"
                        :max="product.stock"
                        placeholder="请输入直播库存"
                        style="width: 100%;"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="排序权重">
                      <el-input-number
                        v-model="product.editSort"
                        :min="0"
                        :max="999"
                        placeholder="数字越大排序越靠前"
                        style="width: 100%;"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="addProductDialogVisible = false">
          取消
        </el-button>
        <el-button v-if="currentStep > 0" @click="handleStepBack">
          上一步
        </el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          @click="handleStepNext"
          :disabled="selectedProducts.length === 0"
        >
          下一步 ({{ selectedProducts.length }})
        </el-button>
        <el-button
          v-if="currentStep === 1"
          type="primary"
          @click="confirmAddProducts"
          :loading="addProductLoading"
        >
          确认添加
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProductList,
  getAvailableProducts,
  addProduct,
  removeProduct,
  updateProductSort,
  updateProductPrice,
  updateProductStock,
  updateProductStatus,
  updateRecommendStatus,
  updateHotStatus,
  startExplaining,
  stopExplaining
} from '@/api/live'

export default {
  name: 'LiveProductManage',
  data() {
    return {
      roomId: '',
      tableKey: 0,
      list: null,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20
      },
      addProductDialogVisible: false,
      currentStep: 0, // 当前步骤：0-选择商品，1-编辑属性
      selectedProducts: [], // 选中的商品列表
      selectedProductsForEdit: [], // 用于编辑的商品列表
      addProductLoading: false
    }
  },
  created() {
    this.roomId = this.$route.params.roomId
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getProductList(this.roomId, this.listQuery).then(response => {
        this.list = response
        this.listLoading = false
      })
    },
    handleAddProduct() {
      this.addProductDialogVisible = true
      this.currentStep = 0
      this.selectedProducts = []
      this.selectedProductsForEdit = []
    },

    // 使用模态框选择商品（支持多选）
    changeGood() {
      const _this = this
      this.$modalGoodList(function(rows) {
        // rows 是选中的商品数组
        if (Array.isArray(rows)) {
          // 多选模式
          _this.selectedProducts = rows.map(row => ({
            id: row.id,
            image: row.image,
            storeName: row.storeName,
            price: row.price,
            otPrice: row.otPrice,
            stock: row.stock,
            sales: row.sales || 0
          }))
        } else {
          // 单选模式，转换为数组
          const product = {
            id: rows.id,
            image: rows.image,
            storeName: rows.storeName,
            price: rows.price,
            otPrice: rows.otPrice,
            stock: rows.stock,
            sales: rows.sales || 0
          }

          // 检查是否已存在，避免重复添加
          const existIndex = _this.selectedProducts.findIndex(p => p.id === product.id)
          if (existIndex === -1) {
            _this.selectedProducts.push(product)
          } else {
            _this.$message.warning('该商品已经选择过了')
          }
        }
      }, 'many', _this.selectedProducts) // 'many' 表示多选模式
    },

    // 移除已选商品
    removeSelectedProduct(index) {
      this.selectedProducts.splice(index, 1)
    },

    // 步骤控制
    handleStepNext() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请选择要添加的商品')
        return
      }

      // 准备编辑数据
      this.selectedProductsForEdit = this.selectedProducts.map(product => ({
        ...product,
        editName: product.storeName,
        editPrice: product.price,
        editStock: Math.min(product.stock, 100), // 默认直播库存不超过100
        editSort: 0,
        maxStock: product.stock
      }))

      this.currentStep = 1
    },

    handleStepBack() {
      this.currentStep = 0
    },
    confirmAddProducts() {
      // 验证必填字段
      const invalidProducts = this.selectedProductsForEdit.filter(product =>
        !product.editName || !product.editPrice || product.editStock < 0
      )

      if (invalidProducts.length > 0) {
        this.$message.warning('请完善所有商品的基本信息')
        return
      }

      this.addProductLoading = true

      // 构建添加商品的数据
      const addProductPromises = this.selectedProducts.map(product => {
        const productData = {
          roomId: this.roomId,
          productId: product.id,
          productName: product.editName,
          productPrice: product.editPrice,
          stock: product.editStock,
          sort: product.editSort,
          status: 0 // 默认下架状态
        }
        return addProduct(productData)
      })

      Promise.all(addProductPromises).then(() => {
        this.$notify({
          title: '成功',
          message: `成功添加 ${this.selectedProductsForEdit.length} 个商品`,
          type: 'success',
          duration: 2000
        })
        this.addProductDialogVisible = false
        this.currentStep = 0
        this.getList()
      }).catch(error => {
        this.$message.error('添加商品失败: ' + (error.message || '未知错误'))
      }).finally(() => {
        this.addProductLoading = false
      })
    },
    handleSortChange(row) {
      updateProductSort(row.id, row.sort).then(() => {
        this.$message.success('排序更新成功')
        // 重新排序列表
        this.getList()
      })
    },

    // 价格编辑
    handlePriceChange(row) {
      if (row.productPrice <= 0) {
        this.$message.warning('价格必须大于0')
        return
      }

      updateProductPrice(row.id, row.productPrice).then(() => {
        this.$message.success('价格更新成功')
      }).catch(() => {
        this.$message.error('价格更新失败')
        this.getList() // 重新加载数据
      })
    },

    // 库存编辑
    handleStockChange(row) {
      if (row.stock < 0) {
        this.$message.warning('库存不能小于0')
        return
      }

      updateProductStock(row.id, row.stock).then(() => {
        this.$message.success('库存更新成功')
      }).catch(() => {
        this.$message.error('库存更新失败')
        this.getList() // 重新加载数据
      })
    },

    // 上架/下架切换
    handleToggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '上架' : '下架'

      this.$confirm(`确认${statusText}该商品?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateProductStatus(row.id, newStatus).then(() => {
          row.status = newStatus
          this.$message.success(`商品${statusText}成功`)
        }).catch(() => {
          this.$message.error(`商品${statusText}失败`)
        })
      })
    },
    handleStartExplain(row) {
      startExplaining(this.roomId, row.productId).then(() => {
        this.$notify({
          title: '成功',
          message: '开始讲解商品',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleStopExplain(row) {
      stopExplaining(this.roomId, row.productId).then(() => {
        this.$notify({
          title: '成功',
          message: '结束讲解商品',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'recommend':
          this.toggleRecommend(row)
          break
        case 'hot':
          this.toggleHot(row)
          break
        case 'remove':
          this.handleRemoveProduct(row)
          break
      }
    },
    toggleRecommend(row) {
      updateRecommendStatus(row.id, !row.isRecommend).then(() => {
        row.isRecommend = !row.isRecommend
        this.$message.success('推荐状态更新成功')
      })
    },
    toggleHot(row) {
      updateHotStatus(row.id, !row.isHot).then(() => {
        row.isHot = !row.isHot
        this.$message.success('热卖状态更新成功')
      })
    },
    handleRemoveProduct(row) {
      this.$confirm('确认移除该商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeProduct(this.roomId, row.productId).then(() => {
          this.$notify({
            title: '成功',
            message: '商品移除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    sortChange(data) {
      const { column, prop, order } = data
      if (prop === 'sort') {
        this.listQuery.sort = order === 'ascending' ? '+sort' : '-sort'
        this.getList()
      }
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

/* 商品编辑容器样式 */
.product-edit-container {
  max-height: 500px;
  overflow-y: auto;
}

.product-edit-item {
  margin-bottom: 20px;
}

.product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.product-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 15px;
  border: 1px solid #e4e7ed;
}

.product-basic-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.original-info {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.product-form {
  padding: 0 15px 15px;
}

/* 状态标签样式 */
.status-tags {
  line-height: 1.5;
}

.status-tags .el-tag {
  margin: 1px 2px;
}

/* 表格样式优化 */
.el-table .cell {
  padding: 0 5px;
}

/* 输入框样式 */
.el-input-number.is-controls-right .el-input__inner {
  padding-right: 50px;
}

/* 对话框样式 */
.el-dialog__body {
  padding: 10px 20px;
}

/* 步骤条样式 */
.el-steps {
  margin-bottom: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-header {
    flex-direction: column;
    text-align: center;
  }

  .product-image {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
