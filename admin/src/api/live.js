import request from '@/utils/request'

// ==================== 直播间管理 ====================

/**
 * 获取直播间列表
 * @param {Object} params 查询参数
 */
export function getRoomList(params) {
  return request({
    url: '/api/admin/live/room/list',
    method: 'get',
    params
  })
}

/**
 * 获取直播间详情
 * @param {String} roomId 直播间ID
 */
export function getRoomInfo(roomId) {
  return request({
    url: `/api/admin/live/room/info/${roomId}`,
    method: 'get'
  })
}

/**
 * 创建直播间
 * @param {Object} data 直播间数据
 */
export function createRoom(data) {
  return request({
    url: '/api/admin/live/room/create',
    method: 'post',
    data
  })
}

/**
 * 更新直播间信息
 * @param {Object} data 直播间数据
 */
export function updateRoom(data) {
  return request({
    url: '/api/admin/live/room/update',
    method: 'post',
    data
  })
}

/**
 * 删除直播间
 * @param {String} roomId 直播间ID
 */
export function deleteRoom(roomId) {
  return request({
    url: `/api/admin/live/room/delete/${roomId}`,
    method: 'delete'
  })
}

/**
 * 开始直播
 * @param {String} roomId 直播间ID
 */
export function startLive(roomId) {
  return request({
    url: `/api/admin/live/room/start/${roomId}`,
    method: 'post'
  })
}

/**
 * 结束直播
 * @param {String} roomId 直播间ID
 */
export function stopLive(roomId) {
  return request({
    url: `/api/admin/live/room/stop/${roomId}`,
    method: 'post'
  })
}

// ==================== 直播间消息管理 ====================

/**
 * 获取直播间消息列表
 * @param {String} roomId 直播间ID
 * @param {Object} params 查询参数
 */
export function getMessageList(roomId, params) {
  return request({
    url: `/api/admin/live/message/list/${roomId}`,
    method: 'get',
    params
  })
}

/**
 * 删除消息
 * @param {Number} messageId 消息ID
 */
export function deleteMessage(messageId) {
  return request({
    url: `/api/admin/live/message/delete/${messageId}`,
    method: 'delete'
  })
}

/**
 * 批量删除消息
 * @param {Array} messageIds 消息ID数组
 */
export function batchDeleteMessage(messageIds) {
  return request({
    url: '/api/admin/live/message/batchDelete',
    method: 'post',
    data: messageIds
  })
}

/**
 * 清空直播间消息
 * @param {String} roomId 直播间ID
 */
export function clearRoomMessage(roomId) {
  return request({
    url: `/api/admin/live/message/clear/${roomId}`,
    method: 'post'
  })
}

// ==================== 直播间商品管理 ====================

/**
 * 获取直播间商品列表
 * @param {String} roomId 直播间ID
 * @param {Object} params 查询参数
 */
export function getProductList(roomId, params) {
  return request({
    url: `/api/admin/live/product/list/${roomId}`,
    method: 'get',
    params
  })
}

/**
 * 获取可添加的商品列表
 * @param {String} roomId 直播间ID
 * @param {Object} params 查询参数
 */
export function getAvailableProducts(roomId, params) {
  return request({
    url: `/api/admin/live/product/available/${roomId}`,
    method: 'get',
    params
  })
}

/**
 * 添加商品到直播间
 * @param {String} roomId 直播间ID
 * @param {Number} productId 商品ID
 */
export function addProduct(roomId, productId) {
  return request({
    url: '/api/admin/live/product/add',
    method: 'post',
    params: {
      roomId,
      productId
    }
  })
}

/**
 * 从直播间移除商品
 * @param {String} roomId 直播间ID
 * @param {Number} productId 商品ID
 */
export function removeProduct(roomId, productId) {
  return request({
    url: '/api/admin/live/product/remove',
    method: 'post',
    params: {
      roomId,
      productId
    }
  })
}

/**
 * 更新商品排序
 * @param {Number} id 商品ID
 * @param {Number} sort 排序权重
 */
export function updateProductSort(id, sort) {
  return request({
    url: '/api/admin/live/product/sort',
    method: 'post',
    params: {
      id,
      sort
    }
  })
}

/**
 * 更新商品推荐状态
 * @param {Number} id 商品ID
 * @param {Boolean} isRecommend 是否推荐
 */
export function updateRecommendStatus(id, isRecommend) {
  return request({
    url: '/api/admin/live/product/recommend',
    method: 'post',
    params: {
      id,
      isRecommend
    }
  })
}

/**
 * 更新商品热卖状态
 * @param {Number} id 商品ID
 * @param {Boolean} isHot 是否热卖
 */
export function updateHotStatus(id, isHot) {
  return request({
    url: '/api/admin/live/product/hot',
    method: 'post',
    params: {
      id,
      isHot
    }
  })
}

/**
 * 开始讲解商品
 * @param {String} roomId 直播间ID
 * @param {Number} productId 商品ID
 */
export function startExplaining(roomId, productId) {
  return request({
    url: '/api/admin/live/product/startExplain',
    method: 'post',
    params: {
      roomId,
      productId
    }
  })
}

/**
 * 结束讲解商品
 * @param {String} roomId 直播间ID
 * @param {Number} productId 商品ID
 */
export function stopExplaining(roomId, productId) {
  return request({
    url: '/api/admin/live/product/stopExplain',
    method: 'post',
    params: {
      roomId,
      productId
    }
  })
}
