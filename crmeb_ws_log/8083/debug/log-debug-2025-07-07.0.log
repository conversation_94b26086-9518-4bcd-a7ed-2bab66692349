{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.560",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.2.7.RELEASE/spring-boot-starter-websocket-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.2.5.RELEASE/spring-messaging-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.2.5.RELEASE/spring-websocket-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot-starter/2.3.1/rocketmq-v5-client-spring-boot-starter-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot/2.3.1/rocketmq-v5-client-spring-boot-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-client-java/5.0.7/rocketmq-client-java-5.0.7.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/annotations-api/6.0.53/annotations-api-6.0.53.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.790",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.LiveWsApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.918",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.920",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.921",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.921",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.921",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:34.923",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3faf2e7d" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:39.181",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:39.181",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:39.181",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:39.783",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.558",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.559",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.613",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.614",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.614",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.614",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:40.614",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:50.168",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "6 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:51.059",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.s.s.WebSocketHandlerMapping",
                    "message": "Patterns [/chat/{roomId}] in 'webSocketHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:51.551",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:51.582",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:51.713",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:51.745",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.821",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.821",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.821",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.821",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.822",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:56:53.927",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.2.7.RELEASE/spring-boot-starter-websocket-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.2.5.RELEASE/spring-messaging-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.2.5.RELEASE/spring-websocket-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot-starter/2.3.1/rocketmq-v5-client-spring-boot-starter-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot/2.3.1/rocketmq-v5-client-spring-boot-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-client-java/5.0.7/rocketmq-client-java-5.0.7.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/annotations-api/6.0.53/annotations-api-6.0.53.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:13.728",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.2.7.RELEASE/spring-boot-starter-websocket-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.2.5.RELEASE/spring-messaging-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.2.5.RELEASE/spring-websocket-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot-starter/2.3.1/rocketmq-v5-client-spring-boot-starter-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-v5-client-spring-boot/2.3.1/rocketmq-v5-client-spring-boot-2.3.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/apache/rocketmq/rocketmq-client-java/5.0.7/rocketmq-client-java-5.0.7.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/annotations-api/6.0.53/annotations-api-6.0.53.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.010",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.LiveWsApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.146",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.146",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.146",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.147",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.147",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-ws/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:14.148",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@592e843a" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:18.860",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:18.860",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:18.860",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:19.553",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.384",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.385",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.438",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.438",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.439",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.439",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:20.439",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:27.625",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "6 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:28.365",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.s.s.WebSocketHandlerMapping",
                    "message": "Patterns [/chat/{roomId}] in 'webSocketHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:28.704",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:28.720",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:28.837",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:58:28.865",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.741",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.742",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.742",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.742",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.743",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-07 23:59:50.744",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
