{"@timestamp":"2025-07-20T18:26:16.939+08:00","@version":"1","message":"Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.073+08:00","@version":"1","message":"Starting CrmebAdminApplication on cuigxdeMacBook-Pro.local with PID 53501 (/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes started by cuigx in /Users/<USER>/workspace/sysd/crmlive)","logger_name":"com.zbkj.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:17.074+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.zbkj.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:17.074+08:00","@version":"1","message":"Loading source class com.zbkj.admin.CrmebAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.164+08:00","@version":"1","message":"Activated activeProfiles dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.164+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.165+08:00","@version":"1","message":"Profiles already activated, '[dev]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.165+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:17.167+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3174cb09","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:19.314+08:00","@version":"1","message":"No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration.","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-20T18:26:19.631+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:19.638+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:19.686+08:00","@version":"1","message":"Finished Spring Data repository scanning in 19ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.155+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.167+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.172+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@42107318' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.176+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.191+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.697+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:21.698+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:21.698+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:21.797+08:00","@version":"1","message":"Tomcat initialized with port(s): 20010 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.823+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.824+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:21.825+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.34]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:22.297+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:22.307+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:22.308+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:22.308+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 5142 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:23.663+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.665+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.724+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.725+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.725+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.725+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.726+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:23.726+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:33.848+08:00","@version":"1","message":"309 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:33.974+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:34.207+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:34.302+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:34.304+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:34.664+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@38991781, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e987f59, org.springframework.security.web.header.HeaderWriterFilter@108fd5d5, org.springframework.web.filter.CorsFilter@7a274521, org.springframework.web.filter.CorsFilter@7a274521, org.springframework.web.filter.CorsFilter@7a274521, org.springframework.security.web.authentication.logout.LogoutFilter@35d145fb, com.zbkj.admin.filter.JwtAuthenticationTokenFilter@35260785, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ee1c29a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@690c3b1f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c577368, org.springframework.security.web.session.SessionManagementFilter@68a7f77a, org.springframework.security.web.access.ExceptionTranslationFilter@25c98637, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5b58f639]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:34.982+08:00","@version":"1","message":"Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'","logger_name":"org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:35.004+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:35.145+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:35.177+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:36.524+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:36.636+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:36.886+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.658+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.671+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.688+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.707+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.732+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.736+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.791+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.795+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.842+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.851+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:37.901+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.109+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.129+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.175+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.189+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.193+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.207+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.210+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.217+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.225+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.293+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.499+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.562+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.590+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.594+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.601+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.642+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.651+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.664+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.666+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.668+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.701+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.721+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.723+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.762+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.783+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.822+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.895+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.949+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:38.975+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.037+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.065+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.081+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.082+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.090+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.106+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.111+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.119+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.121+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.128+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.131+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.136+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.138+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.140+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.180+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.183+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.198+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.201+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.204+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.209+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.219+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.222+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.230+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.233+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.236+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.240+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.251+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.258+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.267+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.274+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.279+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.285+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.288+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.292+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.294+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.310+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.319+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.323+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.329+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.330+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.344+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.369+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.371+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.375+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.377+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.379+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.388+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.391+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.398+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.401+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.404+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.414+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.418+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.423+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.425+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.435+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.452+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.454+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.455+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.466+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.476+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.483+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.486+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.494+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.501+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.506+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.507+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.508+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.511+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.520+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.523+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.531+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.532+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.534+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.539+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.555+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.561+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.567+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.569+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.571+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.572+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.577+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.583+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.585+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.722+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.727+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.762+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.790+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.800+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.802+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.804+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.808+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.811+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.816+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.819+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.835+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.841+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.844+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.858+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.861+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.866+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.869+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.873+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.874+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.891+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.892+08:00","@version":"1","message":"Generating unique operation named: getUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.897+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.906+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.909+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.911+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.915+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.917+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.919+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:39.920+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.000+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.025+08:00","@version":"1","message":"Generating unique operation named: webHookUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.026+08:00","@version":"1","message":"Generating unique operation named: webHookUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.049+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.069+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.075+08:00","@version":"1","message":"---AsyncWeChatPublicTempMessage task------produce Data with fixed rate task: Execution Time - 20250720","logger_name":"com.zbkj.admin.task.wechat.AsyncWeChatPublicTempMessage","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.077+08:00","@version":"1","message":"---AsyncWeChatProgramTempMessage task------produce Data with fixed rate task: Execution Time - 20250720","logger_name":"com.zbkj.admin.task.wechat.AsyncWeChatProgramTempMessage","thread_name":"crmeb-scheduled-task-pool-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderRefundTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderRefundTask","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderReceiptTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderReceiptTask","thread_name":"crmeb-scheduled-task-pool-10","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderAutoCompleteTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderCompleteTask","thread_name":"crmeb-scheduled-task-pool-4","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderTakeByUser task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.product.ProductStockTask","thread_name":"crmeb-scheduled-task-pool-12","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---CouponOverdueTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.coupon.CouponOverdueTask","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderCompleteTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderCompleteTask","thread_name":"crmeb-scheduled-task-pool-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---IntegralFrozenTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.integral.IntegralFrozenTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---BrokerageFrozenTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderReceiptTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.081+08:00","@version":"1","message":"---OrderPaySuccessTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderPaySuccessTask","thread_name":"crmeb-scheduled-task-pool-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.080+08:00","@version":"1","message":"---OrderAutoCancelTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderAutoCancelTask","thread_name":"crmeb-scheduled-task-pool-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.081+08:00","@version":"1","message":"---OrderCancelTask task------produce Data with fixed rate task: Execution Time - Sun Jul 20 18:26:40 CST 2025","logger_name":"com.zbkj.admin.task.order.OrderCancelTask","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.428+08:00","@version":"1","message":"Tomcat started on port(s): 20010 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:40.442+08:00","@version":"1","message":"Started CrmebAdminApplication in 24.714 seconds (JVM running for 26.667)","logger_name":"com.zbkj.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.121+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"RMI TCP Connection(4)-127.0.0.1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.121+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(4)-127.0.0.1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.122+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(4)-127.0.0.1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:41.136+08:00","@version":"1","message":"TemplateMessageServiceImpl.consumePublic | size:0","logger_name":"com.zbkj.service.service.impl.TemplateMessageServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.136+08:00","@version":"1","message":"OrderTaskServiceImpl.autoCancel | size:0","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.138+08:00","@version":"1","message":"StoreProductServiceImpl.doProductStock | size:0","logger_name":"com.zbkj.service.service.impl.StoreProductServiceImpl","thread_name":"crmeb-scheduled-task-pool-12","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.159+08:00","@version":"1","message":"OrderTaskServiceImpl.orderPaySuccessAfter | size:0","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.163+08:00","@version":"1","message":"OrderTaskServiceImpl.refundApply | size:0","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.164+08:00","@version":"1","message":"OrderTaskServiceImpl.cancelByUser | size:0","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.164+08:00","@version":"1","message":"TemplateMessageServiceImpl.consumeProgram | size:0","logger_name":"com.zbkj.service.service.impl.TemplateMessageServiceImpl","thread_name":"crmeb-scheduled-task-pool-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.164+08:00","@version":"1","message":"OrderTaskServiceImpl.orderReceiving | size:0","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-10","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.164+08:00","@version":"1","message":"OrderTaskServiceImpl.complete | size:2","logger_name":"com.zbkj.service.service.impl.OrderTaskServiceImpl","thread_name":"crmeb-scheduled-task-pool-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.195+08:00","@version":"1","message":"StoreProductServiceImpl.doProductStock | size:0","logger_name":"com.zbkj.service.service.impl.StoreSeckillServiceImpl","thread_name":"crmeb-scheduled-task-pool-12","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.261+08:00","@version":"1","message":"StoreBargainServiceImpl.consumeProductStock | size:0","logger_name":"com.zbkj.service.service.impl.StoreBargainServiceImpl","thread_name":"crmeb-scheduled-task-pool-12","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.311+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(4)-127.0.0.1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:26:41.311+08:00","@version":"1","message":"Completed initialization in 190 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(4)-127.0.0.1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.317+08:00","@version":"1","message":"StoreProductServiceImpl.doProductStock | size:0","logger_name":"com.zbkj.service.service.impl.StoreCombinationServiceImpl","thread_name":"crmeb-scheduled-task-pool-12","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:41.513+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:26:43.345+08:00","@version":"1","message":"Executing SQL query [/* ping */ SELECT 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"RMI TCP Connection(5)-127.0.0.1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:00.005+08:00","@version":"1","message":"---PinkStatusChange------bargain stop status change task: Execution Time - Sun Jul 20 18:27:00 CST 2025","logger_name":"com.zbkj.admin.task.pink.PinkStatusChangeTask","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:06.092+08:00","@version":"1","message":"Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]","thread_name":"http-nio-20010-exec-2","level":"ERROR","level_value":40000,"stack_trace":"org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String \"//\"\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)\n\tat org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)\n\tat org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)\n\tat org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)\n\tat org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\n"}
{"@timestamp":"2025-07-20T18:27:06.753+08:00","@version":"1","message":"GET \"/api/admin/getAdminInfoByToken?token=8143d892ae254763b3737ece79d23724&temp=1753007224\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:06.849+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:06.996+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：33654898 ns，cost：33 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:07.038+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.052+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@209e9b12]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.105+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.360+08:00","@version":"1","message":"GET \"/api/admin/getMenus?temp=1753007227\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.362+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.551+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：188423368 ns，cost：188 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-4","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:07.552+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.553+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@4e6c0035]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.559+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.963+08:00","@version":"1","message":"GET \"/api/admin/statistics/home/<USER>/user?temp=1753007227\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-13","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.963+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_square&temp=1753007227\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.963+08:00","@version":"1","message":"GET \"/api/admin/statistics/home/<USER>/order?temp=1753007227\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.963+08:00","@version":"1","message":"GET \"/api/admin/statistics/home/<USER>", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.964+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.HomeController#indexDate()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.964+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.HomeController#chartUser()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-13","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.964+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.964+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.HomeController#chartOrder()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.968+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753007227\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:07.969+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.384+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.HomeController.chartUser()，prams：[]，cost time：417933308 ns，cost：417 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:08.385+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-13","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.385+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@253d6079]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-13","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.386+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-13","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.391+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.HomeController.chartOrder()，prams：[]，cost time：424945691 ns，cost：424 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:08.394+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.394+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@50fdb6ae]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.395+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.438+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：318376470 ns，cost：318 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:08.439+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.448+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@4f4a44b8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.452+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.471+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：350422963 ns，cost：350 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:08.473+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.473+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@4bed9e9d]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.474+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.927+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.HomeController.indexDate()，prams：[]，cost time：960565410 ns，cost：960 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:08.928+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.928+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@4d2e9253]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:08.930+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.235+08:00","@version":"1","message":"GET \"/api/admin/store/order/statisticsData?page=1&limit=10&temp=1753007234\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.237+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.StoreOrderController#getStaffDetail(StoreOrderStaticsticsRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.237+08:00","@version":"1","message":"GET \"/api/admin/store/order/statistics?temp=1753007234\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.238+08:00","@version":"1","message":"Mapped to com.zbkj.admin.controller.StoreOrderController#getStatistics()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.389+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.StoreOrderController.getStaffDetail(StoreOrderStaticsticsRequest)，prams：[StoreOrderStaticsticsRequest(page=0, limit=10, dateLimit=null, startTime=null, endTime=2025-07-20 18:27:14, storeId=1)]，cost time：46141210 ns，cost：46 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:14.390+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.391+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@5e7600b7]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.393+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.973+08:00","@version":"1","message":"Controller method：CommonResult com.zbkj.admin.controller.StoreOrderController.getStatistics()，prams：[]，cost time：734434351 ns，cost：734 ms","logger_name":"com.zbkj.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-20T18:27:14.975+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.975+08:00","@version":"1","message":"Writing [com.zbkj.common.response.CommonResult@1c31b18e]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-20T18:27:14.983+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
