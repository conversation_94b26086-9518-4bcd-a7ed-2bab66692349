# LiveRoomService方法实现文档

## 概述

本文档描述了为LiveRoomService接口添加的缺失方法及其实现，以支持直播间管理功能。

## 新增方法

### 1. getList(PageParamRequest pageParamRequest)
**功能**: 分页获取直播间列表

**实现逻辑**:
- 使用MyBatis-Plus的分页功能
- 按创建时间倒序排列
- 返回CommonPage格式的分页数据

**使用场景**: 后台管理页面显示直播间列表

### 2. getByRoomId(String roomId)
**功能**: 根据房间ID获取直播间信息

**实现逻辑**:
- 参数验证（非空检查）
- 使用LambdaQueryWrapper查询
- 返回单个LiveRoom对象

**使用场景**: 获取特定直播间的详细信息

### 3. startLive(String roomId)
**功能**: 开始直播

**实现逻辑**:
- 更新直播间状态为1（直播中）
- 设置开始时间为当前时间戳
- 清空结束时间
- 更新修改时间

**业务规则**:
- 只有未开播状态的直播间可以开始直播
- 开始时间使用秒级时间戳

### 4. stopLive(String roomId)
**功能**: 结束直播

**实现逻辑**:
- 更新直播间状态为2（已结束）
- 设置结束时间为当前时间戳
- 更新修改时间

**业务规则**:
- 只有直播中状态的直播间可以结束直播
- 结束后可以重新开播

### 5. create(LiveRoom liveRoom)
**功能**: 创建直播间

**实现逻辑**:
- 自动生成唯一房间ID（如果未提供）
- 设置默认状态为0（未开播）
- 设置默认访问人数为0
- 设置创建和更新时间

**房间ID生成规则**:
```java
"room_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8)
```

### 6. deleteByRoomId(String roomId)
**功能**: 根据房间ID删除直播间

**实现逻辑**:
- 检查直播间是否存在
- 检查直播间状态（直播中不允许删除）
- 执行删除操作
- 记录操作日志

**安全规则**:
- 正在直播中的直播间不允许删除
- 删除前进行存在性检查

## 数据库表结构

### eb_live_room表字段说明

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| id | bigint(20) | 主键ID | AUTO_INCREMENT |
| room_id | varchar(50) | 直播间ID | NOT NULL, UNIQUE |
| anchor_id | bigint(20) | 主播ID | NULL |
| avatar | varchar(500) | 主播头像 | NULL |
| name | varchar(255) | 主播名称 | NULL |
| room_name | varchar(255) | 直播间名称 | NOT NULL |
| room_desc | varchar(1000) | 直播间描述 | NULL |
| anchor_name | varchar(255) | 主播名称 | NULL |
| anchor_avatar | varchar(500) | 主播头像 | NULL |
| category | varchar(100) | 分类 | NULL |
| cover | varchar(500) | 直播封面 | NULL |
| visitor_count | bigint(20) | 访问人数 | 0 |
| online_count | int(11) | 在线人数 | 0 |
| start_time | bigint(20) | 开始时间 | NULL |
| end_time | bigint(20) | 结束时间 | NULL |
| status | int(11) | 状态 | 0 |
| create_time | datetime | 创建时间 | CURRENT_TIMESTAMP |
| update_time | datetime | 更新时间 | CURRENT_TIMESTAMP |

### 状态值说明
- 0: 未开播
- 1: 直播中
- 2: 已结束

## DAO层扩展

### LiveRoomDao新增方法

```java
// 根据房间ID查询
LiveRoom selectByRoomId(String roomId);

// 更新状态
int updateStatusByRoomId(String roomId, Integer status);

// 更新开始时间
int updateStartTimeByRoomId(String roomId, Integer status, Long startTime);

// 更新结束时间
int updateEndTimeByRoomId(String roomId, Integer status, Long endTime);

// 更新在线人数
int updateOnlineCountByRoomId(String roomId, Integer onlineCount);

// 增加访问人数
int incrementVisitorCount(String roomId);

// 获取正在直播的房间
List<LiveRoom> selectLivingRooms();

// 获取热门直播间
List<LiveRoom> selectHotRooms(Integer limit);

// 根据分类获取直播间
List<LiveRoom> selectByCategory(String category);
```

## 错误处理

### 异常处理策略
1. **参数验证**: 对输入参数进行非空和格式检查
2. **业务规则检查**: 验证业务逻辑约束
3. **异常捕获**: 捕获并记录所有异常
4. **日志记录**: 详细记录操作日志和错误信息

### 常见错误场景
1. **房间ID为空**: 返回false，记录警告日志
2. **直播间不存在**: 返回false，记录警告日志
3. **状态不允许操作**: 返回false，记录警告日志
4. **数据库操作失败**: 返回false，记录错误日志

## 使用示例

### 创建直播间
```java
LiveRoom liveRoom = new LiveRoom();
liveRoom.setRoomName("时尚直播间");
liveRoom.setRoomDesc("分享最新时尚资讯");
liveRoom.setAnchorName("主播小美");
liveRoom.setCategory("时尚");

boolean success = liveRoomService.create(liveRoom);
```

### 开始直播
```java
boolean success = liveRoomService.startLive("room_001");
if (success) {
    // 直播开始成功
    // 可以进行后续操作，如通知用户等
}
```

### 获取直播间列表
```java
PageParamRequest pageRequest = new PageParamRequest();
pageRequest.setPage(1);
pageRequest.setLimit(10);

CommonPage<LiveRoom> roomPage = liveRoomService.getList(pageRequest);
List<LiveRoom> rooms = roomPage.getList();
```

## 测试建议

### 单元测试
1. **参数验证测试**: 测试各种无效参数的处理
2. **业务逻辑测试**: 测试状态转换的正确性
3. **异常处理测试**: 测试异常情况的处理

### 集成测试
1. **完整流程测试**: 创建→开播→结束→删除的完整流程
2. **并发测试**: 测试多个直播间同时操作的情况
3. **数据一致性测试**: 验证数据库状态的一致性

### 性能测试
1. **分页查询性能**: 测试大量数据下的分页性能
2. **并发操作性能**: 测试高并发下的操作性能

## 注意事项

### 1. 时间戳处理
- 使用秒级时间戳存储开始和结束时间
- 前端显示时需要转换为毫秒级

### 2. 状态管理
- 严格按照状态转换规则执行
- 避免非法状态转换

### 3. 数据一致性
- 确保相关数据的一致性
- 考虑分布式环境下的数据同步

### 4. 扩展性
- 预留扩展字段
- 考虑未来功能需求

这些方法的实现为直播间管理提供了完整的基础功能，支持后台管理系统的各种操作需求。
