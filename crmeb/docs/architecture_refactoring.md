# 直播间架构重构文档

## 重构背景

### 原有架构问题
1. **循环依赖**: `crmeb-service` 模块依赖 `crmeb-front` 中的WebSocket代码
2. **职责混乱**: Service层包含了WebSocket业务逻辑
3. **分层违反**: 数据访问层直接调用表现层代码

### 架构原则
- **单向依赖**: 上层可以依赖下层，下层不能依赖上层
- **职责分离**: 每个模块只关注自己的核心职责
- **松耦合**: 模块间通过接口交互，减少直接依赖

## 重构后的架构

### 模块分层
```
┌─────────────────────────────────────┐
│           crmeb-front               │
├─────────────────────────────────────┤
│  Controller Layer                   │
│  ├── LiveRoomController             │
│  └── 其他Controller                 │
├─────────────────────────────────────┤
│  Business Logic Layer (biz)         │
│  ├── LiveRoomBizService             │
│  └── 其他BizService                 │
├─────────────────────────────────────┤
│  WebSocket Layer                    │
│  ├── LiveRoomWebSocketHandler       │
│  ├── WebSocketManager               │
│  └── WebSocketConfig                │
└─────────────────────────────────────┘
                  ↓ 依赖
┌─────────────────────────────────────┐
│           crmeb-service             │
├─────────────────────────────────────┤
│  Service Layer                      │
│  ├── LiveRoomService                │
│  ├── LiveRoomMsgService             │
│  └── 其他Service                    │
└─────────────────────────────────────┘
                  ↓ 依赖
┌─────────────────────────────────────┐
│           crmeb-common              │
├─────────────────────────────────────┤
│  Model & Response & Request         │
│  ├── LiveRoom                       │
│  ├── LiveRoomInfoResponse           │
│  └── 其他通用类                     │
└─────────────────────────────────────┘
```

### 职责划分

#### 1. crmeb-service 层
**职责**: 数据持久化和基础数据服务
- 数据库CRUD操作
- 基础数据查询和处理
- 不涉及WebSocket相关逻辑

**示例**:
```java
// LiveRoomService 只提供数据库相关服务
public LiveRoomInfoResponse getRoomInfo(String roomId) {
    // 只从数据库获取基础信息
    // 不处理WebSocket在线人数
}
```

#### 2. crmeb-front/biz 层
**职责**: 业务逻辑整合
- 整合多个Service的数据
- 处理复杂业务逻辑
- 整合WebSocket实时数据

**示例**:
```java
// LiveRoomBizService 整合数据库和WebSocket信息
public LiveRoomInfoResponse getRoomInfo(String roomId) {
    // 1. 获取数据库基础信息
    // 2. 获取WebSocket在线信息
    // 3. 整合返回完整信息
}
```

#### 3. crmeb-front/websocket 层
**职责**: WebSocket连接管理和实时通信
- WebSocket连接管理
- 消息缓存和广播
- 在线人数统计

#### 4. crmeb-front/controller 层
**职责**: HTTP接口提供
- 接收HTTP请求
- 调用biz层服务
- 返回响应结果

## 重构内容

### 1. 移除循环依赖
**修改前**:
```java
// crmeb-service 中的代码
int onlineCount = LiveRoomWebSocketHandler.getRoomOnlineCount(roomId);
```

**修改后**:
```java
// crmeb-service 中只处理数据库数据
roomInfo.setViewCount(liveRoom.getVisitorCount());

// crmeb-front/biz 中整合WebSocket数据
int onlineCount = webSocketManager.getRoomOnlineCount(roomId);
if (onlineCount > 0) {
    response.getRoomInfo().setViewCount(onlineCount);
}
```

### 2. 创建biz层
**新增文件**:
- `LiveRoomBizService.java` - 业务逻辑接口
- `LiveRoomBizServiceImpl.java` - 业务逻辑实现

**功能**:
- 整合数据库信息和WebSocket信息
- 处理复杂业务逻辑
- 为Controller提供完整的业务服务

### 3. 创建WebSocket管理器
**新增文件**:
- `WebSocketManager.java` - WebSocket功能统一接口

**功能**:
- 提供WebSocket相关功能的统一访问接口
- 封装WebSocket实现细节
- 便于后续扩展和维护

### 4. 修改Controller调用链
**修改前**:
```
Controller → Service (直接调用)
```

**修改后**:
```
Controller → BizService → Service + WebSocket
```

## 架构优势

### 1. 清晰的分层
- 每层职责明确
- 依赖关系单向
- 易于理解和维护

### 2. 松耦合设计
- 模块间通过接口交互
- 减少直接依赖
- 便于单元测试

### 3. 易于扩展
- 新增业务逻辑在biz层处理
- WebSocket功能独立管理
- 不影响数据访问层

### 4. 符合设计原则
- 单一职责原则
- 依赖倒置原则
- 开闭原则

## 使用示例

### 获取直播间信息
```java
// Controller层
@RestController
public class LiveRoomController {
    @Autowired
    private LiveRoomBizService liveRoomBizService;
    
    public CommonResult<LiveRoomInfoResponse> getRoomInfo(String roomId) {
        return CommonResult.success(liveRoomBizService.getRoomInfo(roomId));
    }
}

// Biz层
@Service
public class LiveRoomBizServiceImpl implements LiveRoomBizService {
    @Autowired
    private LiveRoomService liveRoomService;
    @Autowired
    private WebSocketManager webSocketManager;
    
    public LiveRoomInfoResponse getRoomInfo(String roomId) {
        // 1. 获取数据库信息
        LiveRoomInfoResponse response = liveRoomService.getRoomInfo(roomId);
        
        // 2. 整合WebSocket信息
        int onlineCount = webSocketManager.getRoomOnlineCount(roomId);
        if (onlineCount > 0) {
            response.getRoomInfo().setViewCount(onlineCount);
        }
        
        return response;
    }
}

// Service层
@Service
public class LiveRoomServiceImpl implements LiveRoomService {
    public LiveRoomInfoResponse getRoomInfo(String roomId) {
        // 只处理数据库相关逻辑
        // 不涉及WebSocket
    }
}
```

## 迁移指南

### 对于新功能开发
1. 数据访问逻辑放在Service层
2. 业务逻辑整合放在Biz层
3. WebSocket相关功能使用WebSocketManager
4. Controller只调用Biz层服务

### 对于现有代码
1. 识别混合了WebSocket和数据库逻辑的代码
2. 将WebSocket逻辑移到Biz层
3. 保持Service层只处理数据库逻辑
4. 更新Controller调用链

## 注意事项

### 1. 依赖方向
- 确保依赖关系始终是单向的
- crmeb-service 不能依赖 crmeb-front
- 使用接口而不是具体实现

### 2. 事务管理
- 数据库事务在Service层管理
- Biz层不直接管理事务
- 复杂事务场景需要特殊处理

### 3. 异常处理
- Service层抛出业务异常
- Biz层处理业务逻辑异常
- Controller层处理HTTP相关异常

### 4. 性能考虑
- Biz层可能涉及多次Service调用
- 注意避免N+1查询问题
- 合理使用缓存机制

## 总结

通过这次架构重构，我们解决了模块间的循环依赖问题，建立了清晰的分层架构。每个模块都有明确的职责，代码更易于维护和扩展。这为后续的功能开发奠定了良好的架构基础。
