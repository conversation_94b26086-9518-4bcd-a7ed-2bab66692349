# 订单统计按门店过滤功能实现文档

## 概述

本文档描述了如何修改订单统计功能，使其根据当前登录用户的门店权限来过滤数据，而不是统计全站数据。

## 需求分析

### 原需求
- `getStatistics` 和 `getStaffDetail` 方法统计全站订单数据

### 新需求
1. 根据当前登录用户获取关联的自提门店（`eb_system_store_staff`表的`store_id`）
2. 只对用户所属门店的订单数据进行统计
3. 如果用户有多个门店，则取请求参数传入的`store_id`
4. 如果没有传入`store_id`，则默认取第一个门店

## 实现方案

### 1. 扩展SystemStoreStaffService

#### 新增接口方法
```java
/**
 * 根据用户ID获取关联的门店ID列表
 * @param uid 用户ID
 * @return 门店ID列表
 */
List<Integer> getStoreIdsByUid(Integer uid);

/**
 * 获取当前登录用户的门店ID（支持多门店）
 * @param requestStoreId 请求参数中的门店ID（可选）
 * @return 门店ID
 */
Integer getCurrentUserStoreId(Integer requestStoreId);
```

#### 实现逻辑
```java
@Override
public List<Integer> getStoreIdsByUid(Integer uid) {
    LambdaQueryWrapper<SystemStoreStaff> wrapper = Wrappers.lambdaQueryWrapper();
    wrapper.eq(SystemStoreStaff::getUid, uid)
           .eq(SystemStoreStaff::getStatus, Constants.COMMON_STATUS_ENABLE);
    
    List<SystemStoreStaff> staffList = list(wrapper);
    return staffList.stream()
                   .map(SystemStoreStaff::getStoreId)
                   .distinct()
                   .collect(Collectors.toList());
}

@Override
public Integer getCurrentUserStoreId(Integer requestStoreId) {
    // 获取当前登录用户ID
    Integer currentUid = getCurrentUserId();
    if (currentUid == null) {
        throw new CrmebException("用户未登录");
    }

    // 获取用户关联的所有门店ID
    List<Integer> storeIds = getStoreIdsByUid(currentUid);
    if (CollUtil.isEmpty(storeIds)) {
        throw new CrmebException("当前用户未关联任何门店");
    }

    // 如果请求参数中指定了门店ID，验证用户是否有权限访问该门店
    if (requestStoreId != null) {
        if (!storeIds.contains(requestStoreId)) {
            throw new CrmebException("无权限访问指定门店数据");
        }
        return requestStoreId;
    }

    // 如果没有指定门店ID，返回第一个门店ID
    return storeIds.get(0);
}
```

### 2. 扩展StoreOrderVerification服务

#### 新增接口方法
```java
/**
 * 根据门店ID获取订单核销数据
 * @param storeId 门店ID
 * @return 核销数据
 */
StoreStaffTopDetail getOrderVerificationDataByStore(Integer storeId);

/**
 * 根据门店ID获取核销月详情
 * @param request 请求参数
 * @param storeId 门店ID
 * @return 月详情
 */
List<StoreStaffDetail> getOrderVerificationDetailByStore(StoreOrderStaticsticsRequest request, Integer storeId);
```

#### 实现特点
- 在所有查询条件中添加 `.eq(StoreOrder::getStoreId, storeId)` 过滤条件
- 保持原有统计逻辑不变，只是增加门店过滤

### 3. 修改Controller接口

#### getStatistics方法
```java
@RequestMapping(value = "/statistics", method = RequestMethod.GET)
public CommonResult<StoreStaffTopDetail> getStatistics(@RequestParam(required = false) Integer storeId) {
    try {
        // 获取当前用户有权限访问的门店ID
        Integer currentStoreId = systemStoreStaffService.getCurrentUserStoreId(storeId);
        return CommonResult.success(storeOrderVerification.getOrderVerificationDataByStore(currentStoreId));
    } catch (Exception e) {
        log.error("获取核销订单头部数据失败", e);
        return CommonResult.failed(e.getMessage());
    }
}
```

#### getStaffDetail方法
```java
@RequestMapping(value = "/statisticsData", method = RequestMethod.GET)
public CommonResult<List<StoreStaffDetail>> getStaffDetail(StoreOrderStaticsticsRequest request, 
                                                           @RequestParam(required = false) Integer storeId) {
    try {
        // 获取当前用户有权限访问的门店ID
        Integer currentStoreId = systemStoreStaffService.getCurrentUserStoreId(storeId);
        return CommonResult.success(storeOrderVerification.getOrderVerificationDetailByStore(request, currentStoreId));
    } catch (Exception e) {
        log.error("获取核销订单月列表数据失败", e);
        return CommonResult.failed(e.getMessage());
    }
}
```

### 4. 数据库查询优化

#### MyBatis XML映射
```xml
<!-- 按门店查询核销详情 月数据 -->
<select id="getOrderVerificationDetailByStore" parameterType="com.zbkj.common.request.StoreOrderStaticsticsRequest"
        resultType="com.zbkj.common.response.StoreStaffDetail">
    select sum(o.`pay_price`) as price, count(o.`id`) as count, DATE_FORMAT(o.`create_time`, '%Y-%m-%d') as time
    from `eb_store_order` o
    where o.`is_del` = 0 and o.`paid` = 1 and o.`refund_status` = 0
    <if test="null != storeId">
        and o.`store_id` = #{storeId}
    </if>
    <if test="null != startTime and startTime != ''">
        and o.create_time >= #{ startTime }
    </if>
    <if test="null != endTime and endTime != ''">
        and o.create_time &lt; #{ endTime }
    </if>
    GROUP by DATE_FORMAT(o.`create_time`, '%Y-%m-%d') order by o.`create_time` desc limit #{ page },#{ limit };
</select>
```

## 使用方式

### 1. API调用示例

#### 不指定门店ID（使用用户默认门店）
```bash
GET /api/admin/store/order/statistics
GET /api/admin/store/order/statisticsData?dateLimit=today&page=1&limit=10
```

#### 指定门店ID
```bash
GET /api/admin/store/order/statistics?storeId=1
GET /api/admin/store/order/statisticsData?storeId=1&dateLimit=today&page=1&limit=10
```

### 2. 权限验证流程

```
1. 获取当前登录用户ID
2. 查询用户关联的门店列表
3. 验证用户是否有门店权限
4. 如果指定了storeId，验证用户是否有该门店权限
5. 如果没有指定storeId，使用第一个门店
6. 执行按门店过滤的统计查询
```

## 数据库表结构

### eb_system_store_staff表
```sql
CREATE TABLE `eb_system_store_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `staff_name` varchar(64) NOT NULL COMMENT '员工姓名',
  `phone` varchar(16) DEFAULT NULL COMMENT '手机号码',
  `verify_status` tinyint(1) DEFAULT '0' COMMENT '核销状态',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `add_time` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `store_id` (`store_id`),
  KEY `status` (`status`)
) COMMENT='门店员工表';
```

### eb_store_order表
```sql
-- 需要确保订单表有store_id字段
ALTER TABLE `eb_store_order` ADD COLUMN `store_id` int(11) DEFAULT NULL COMMENT '门店ID';
ALTER TABLE `eb_store_order` ADD INDEX `idx_store_id` (`store_id`);
```

## 安全考虑

### 1. 权限验证
- 严格验证用户是否有访问指定门店的权限
- 防止用户访问非授权门店的数据

### 2. 异常处理
- 用户未登录时抛出异常
- 用户无门店权限时抛出异常
- 访问非授权门店时抛出异常

### 3. 日志记录
- 记录权限验证失败的日志
- 记录数据查询异常的日志

## 性能优化

### 1. 数据库索引
```sql
-- 门店员工表索引
CREATE INDEX idx_uid_status ON eb_system_store_staff(uid, status);
CREATE INDEX idx_store_id_status ON eb_system_store_staff(store_id, status);

-- 订单表索引
CREATE INDEX idx_store_id_paid_refund ON eb_store_order(store_id, paid, refund_status);
CREATE INDEX idx_store_id_create_time ON eb_store_order(store_id, create_time);
```

### 2. 查询优化
- 使用合适的索引加速查询
- 避免全表扫描
- 合理使用分页

## 测试验证

### 1. 功能测试
- 测试单门店用户的数据统计
- 测试多门店用户的数据统计
- 测试指定门店ID的数据统计
- 测试权限验证功能

### 2. 边界测试
- 测试用户无门店权限的情况
- 测试访问非授权门店的情况
- 测试用户未登录的情况

### 3. 性能测试
- 测试大数据量下的查询性能
- 测试并发访问的性能

## 注意事项

### 1. 数据一致性
- 确保订单表的store_id字段正确设置
- 确保门店员工关系数据的准确性

### 2. 向后兼容
- 保持原有API接口的兼容性
- 新增的storeId参数为可选参数

### 3. 扩展性
- 设计支持未来可能的多租户需求
- 预留扩展其他权限控制的空间

这个实现方案完全满足了按门店过滤订单统计数据的需求，提供了完善的权限控制和安全保障。
