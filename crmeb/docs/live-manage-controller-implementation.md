# LiveManageController 商品管理功能实现文档

## 概述

根据前端live.js中的API接口定义，完善了LiveManageController中的商品管理功能，包括商品添加、移除、上下架、更新商品价格与库存等功能。

## 实现的功能

### 1. 商品添加功能

#### API接口
```java
@RequestMapping(value = "/product/add", method = RequestMethod.POST)
public CommonResult<String> addProduct(@RequestBody AddProductRequest request)
```

#### 请求参数 (AddProductRequest)
```java
public class AddProductRequest {
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;
    
    @NotNull(message = "商品ID不能为空")
    private Integer productId;
    
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0.01")
    private BigDecimal productPrice;
    
    @NotNull(message = "商品库存不能为空")
    @Min(value = 0, message = "商品库存不能小于0")
    private Integer stock;
    
    private Integer sort = 0;  // 排序权重
    private Integer status = 0; // 商品状态，默认下架
}
```

#### 业务逻辑
1. 检查商品是否已存在于直播间
2. 验证商品是否存在于商品库
3. 创建直播间商品记录，支持自定义名称、价格、库存
4. 默认状态为下架，需要手动上架

### 2. 商品价格更新功能

#### API接口
```java
@RequestMapping(value = "/product/updatePrice", method = RequestMethod.POST)
public CommonResult<String> updateProductPrice(@RequestBody UpdateProductPriceRequest request)
```

#### 请求参数 (UpdateProductPriceRequest)
```java
public class UpdateProductPriceRequest {
    @NotNull(message = "商品ID不能为空")
    private Integer id;
    
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格必须大于0.01")
    private BigDecimal price;
}
```

#### 业务逻辑
- 验证价格参数有效性（必须大于0.01）
- 更新直播间商品的价格
- 记录更新时间

### 3. 商品库存更新功能

#### API接口
```java
@RequestMapping(value = "/product/updateStock", method = RequestMethod.POST)
public CommonResult<String> updateProductStock(@RequestBody UpdateProductStockRequest request)
```

#### 请求参数 (UpdateProductStockRequest)
```java
public class UpdateProductStockRequest {
    @NotNull(message = "商品ID不能为空")
    private Integer id;
    
    @NotNull(message = "库存不能为空")
    @Min(value = 0, message = "库存不能小于0")
    private Integer stock;
}
```

#### 业务逻辑
- 验证库存参数有效性（不能小于0）
- 更新直播间商品的库存
- 记录更新时间

### 4. 商品状态更新功能（上架/下架）

#### API接口
```java
@RequestMapping(value = "/product/updateStatus", method = RequestMethod.POST)
public CommonResult<String> updateProductStatus(@RequestBody UpdateProductStatusRequest request)
```

#### 请求参数 (UpdateProductStatusRequest)
```java
public class UpdateProductStatusRequest {
    @NotNull(message = "商品ID不能为空")
    private Integer id;
    
    @NotNull(message = "商品状态不能为空")
    @Min(value = 0, message = "商品状态值不正确")
    @Max(value = 1, message = "商品状态值不正确")
    private Integer status; // 0-下架，1-上架
}
```

#### 业务逻辑
- 验证状态参数（只能是0或1）
- 更新商品上架/下架状态
- 下架时自动结束讲解状态
- 记录更新时间

### 5. 商品移除功能

#### API接口
```java
@RequestMapping(value = "/product/remove", method = RequestMethod.POST)
public CommonResult<String> removeProduct(
    @RequestParam String roomId,
    @RequestParam Integer productId)
```

#### 业务逻辑
- 软删除：将商品状态设置为false
- 不物理删除记录，保留历史数据
- 记录更新时间

## Service层实现

### LiveRoomProductService 新增方法

#### 1. 带详细信息的添加商品方法
```java
boolean addProductToRoom(String roomId, Integer productId, String productName, 
                        BigDecimal productPrice, Integer stock, Integer sort, Integer status)
```

#### 2. 商品价格更新方法
```java
boolean updateProductPrice(Integer id, BigDecimal price)
```

#### 3. 商品库存更新方法
```java
boolean updateProductStock(Integer id, Integer stock)
```

#### 4. 商品状态更新方法
```java
boolean updateProductStatus(Integer id, Integer status)
```

### LiveRoomProductServiceImpl 实现特点

#### 1. 参数验证
- 所有方法都包含完整的参数验证
- 防止无效数据更新

#### 2. 异常处理
- 使用try-catch捕获异常
- 详细的错误日志记录
- 返回明确的成功/失败状态

#### 3. 事务管理
- 关键操作使用@Transactional注解
- 确保数据一致性

#### 4. 业务逻辑
- 添加商品时检查重复
- 下架时自动结束讲解
- 支持自定义商品属性

## 数据库字段映射

### LiveRoomProduct 实体类字段
```java
public class LiveRoomProduct {
    private Integer id;              // 主键ID
    private String roomId;           // 直播间ID
    private Integer productId;       // 商品ID
    private String productName;      // 商品名称
    private String productImage;     // 商品图片
    private BigDecimal productPrice; // 商品价格
    private BigDecimal originalPrice;// 商品原价
    private Integer stock;           // 商品库存
    private Integer sales;           // 商品销量
    private String productInfo;      // 商品简介
    private Boolean isRecommend;     // 是否推荐
    private Boolean isHot;           // 是否热卖
    private Integer sort;            // 排序权重
    private Boolean status;          // 商品状态
    private Boolean isExplaining;    // 是否正在讲解
    private Date explainStartTime;   // 讲解开始时间
    private Date explainEndTime;     // 讲解结束时间
    private String tags;             // 商品标签
    private String discountInfo;     // 优惠信息
    private Date createTime;         // 创建时间
    private Date updateTime;         // 更新时间
}
```

## API调用示例

### 1. 添加商品
```javascript
// 前端调用
const productData = {
  roomId: "room_001",
  productId: 123,
  productName: "直播专享商品",
  productPrice: 99.99,
  stock: 100,
  sort: 10,
  status: 0  // 默认下架
};

addProduct(productData).then(response => {
  console.log('添加成功:', response);
});
```

### 2. 更新价格
```javascript
updateProductPrice(productId, newPrice).then(response => {
  console.log('价格更新成功:', response);
});
```

### 3. 更新库存
```javascript
updateProductStock(productId, newStock).then(response => {
  console.log('库存更新成功:', response);
});
```

### 4. 上架/下架
```javascript
updateProductStatus(productId, status).then(response => {
  console.log('状态更新成功:', response);
});
```

## 权限控制

所有接口都使用了Spring Security的权限控制：
```java
@PreAuthorize("hasAuthority('admin:live:product:list')")
```

确保只有具有相应权限的用户才能操作直播间商品。

## 日志记录

### 成功日志
- 记录操作成功的关键信息
- 包含操作参数和结果

### 错误日志
- 详细的错误信息和堆栈跟踪
- 便于问题排查和调试

### 日志示例
```
INFO  - 添加商品到直播间成功: roomId=room_001, productId=123, productName=直播专享商品, price=99.99, stock=100, status=0
ERROR - 更新商品价格失败: id=123, price=99.99, error=商品不存在
```

## 注意事项

### 1. 数据一致性
- 商品状态使用Boolean类型存储
- 0/1状态值需要转换为true/false

### 2. 业务规则
- 新添加商品默认下架状态
- 下架商品自动结束讲解
- 价格必须大于0.01

### 3. 扩展性
- 支持自定义商品属性
- 预留了标签和优惠信息字段
- 便于后续功能扩展

这个实现完全符合前端live.js的API接口定义，提供了完整的商品管理功能，包括添加、更新价格、更新库存、上下架等核心操作。
