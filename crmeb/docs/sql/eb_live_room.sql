-- 直播间表
CREATE TABLE `eb_live_room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
  `anchor_id` bigint(20) DEFAULT NULL COMMENT '主播ID',
  `avatar` varchar(500) DEFAULT NULL COMMENT '主播头像',
  `name` varchar(255) DEFAULT NULL COMMENT '主播名称',
  `room_name` varchar(255) NOT NULL COMMENT '直播间名称',
  `room_desc` varchar(1000) DEFAULT NULL COMMENT '直播间描述',
  `anchor_name` varchar(255) DEFAULT NULL COMMENT '主播名称',
  `anchor_avatar` varchar(500) DEFAULT NULL COMMENT '主播头像',
  `category` varchar(100) DEFAULT NULL COMMENT '分类',
  `cover` varchar(500) DEFAULT NULL COMMENT '直播封面',
  `visitor_count` bigint(20) DEFAULT '0' COMMENT '访问人数',
  `online_count` int(11) DEFAULT '0' COMMENT '在线人数',
  `start_time` bigint(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间',
  `status` int(11) DEFAULT '0' COMMENT '状态：0-未开播，1-直播中，2-已结束',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_id` (`room_id`),
  KEY `idx_anchor_id` (`anchor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间表';

-- 插入测试数据
INSERT INTO `eb_live_room` (`room_id`, `room_name`, `room_desc`, `anchor_name`, `anchor_avatar`, `category`, `cover`, `visitor_count`, `online_count`, `status`) VALUES
('room_001', '时尚穿搭直播间', '分享最新时尚穿搭技巧，带你了解潮流趋势', '时尚达人小美', 'https://picsum.photos/200/200?random=1', '时尚', 'https://picsum.photos/400/300?random=1', 1250, 0, 0),
('room_002', '美妆护肤直播间', '专业美妆师教你化妆技巧，推荐好用护肤品', '美妆师小雅', 'https://picsum.photos/200/200?random=2', '美妆', 'https://picsum.photos/400/300?random=2', 890, 0, 0),
('room_003', '数码科技直播间', '最新数码产品评测，科技资讯分享', '科技达人小明', 'https://picsum.photos/200/200?random=3', '数码', 'https://picsum.photos/400/300?random=3', 2100, 0, 0),
('room_004', '美食烹饪直播间', '教你做美味家常菜，分享烹饪小技巧', '美食博主小厨', 'https://picsum.photos/200/200?random=4', '美食', 'https://picsum.photos/400/300?random=4', 1680, 0, 0),
('room_005', '健身运动直播间', '专业健身教练带你科学运动，塑造完美身材', '健身教练小强', 'https://picsum.photos/200/200?random=5', '健身', 'https://picsum.photos/400/300?random=5', 950, 0, 0);
