-- 直播间商品表
CREATE TABLE `eb_live_room_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `product_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品原价',
  `stock` int(11) DEFAULT '0' COMMENT '商品库存',
  `sales` int(11) DEFAULT '0' COMMENT '商品销量',
  `product_info` varchar(1000) DEFAULT NULL COMMENT '商品简介',
  `product_url` varchar(500) DEFAULT NULL COMMENT '商品详情页链接',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热卖：0-否，1-是',
  `sort` int(11) DEFAULT '0' COMMENT '排序权重，数字越大越靠前',
  `status` tinyint(1) DEFAULT '1' COMMENT '商品状态：0-下架，1-上架',
  `is_explaining` tinyint(1) DEFAULT '0' COMMENT '是否正在讲解：0-否，1-是',
  `explain_start_time` datetime DEFAULT NULL COMMENT '讲解开始时间',
  `explain_end_time` datetime DEFAULT NULL COMMENT '讲解结束时间',
  `tags` varchar(500) DEFAULT NULL COMMENT '商品标签，多个用逗号分隔',
  `discount_info` varchar(255) DEFAULT NULL COMMENT '优惠信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status_sort` (`status`, `sort`),
  KEY `idx_is_explaining` (`is_explaining`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间商品表';

-- 插入测试数据
INSERT INTO `eb_live_room_product` (`room_id`, `product_id`, `product_name`, `product_image`, `product_price`, `original_price`, `stock`, `sales`, `product_info`, `is_recommend`, `is_hot`, `sort`, `status`, `tags`, `discount_info`) VALUES
('room_001', 1, '时尚女装连衣裙', 'https://picsum.photos/300/300?random=1', 299.00, 399.00, 100, 25, '优质面料，舒适透气，时尚百搭', 1, 1, 100, 1, '新品,热销,限时优惠', '限时7.5折'),
('room_001', 2, '男士休闲运动鞋', 'https://picsum.photos/300/300?random=2', 199.00, 299.00, 80, 18, '轻便舒适，防滑耐磨，运动首选', 1, 0, 90, 1, '运动,舒适,防滑', '买一送一'),
('room_001', 3, '智能手机保护壳', 'https://picsum.photos/300/300?random=3', 39.00, 59.00, 200, 45, '全包防摔，精准开孔，手感舒适', 0, 1, 80, 1, '防摔,精准,舒适', '第二件半价'),
('room_001', 4, '无线蓝牙耳机', 'https://picsum.photos/300/300?random=4', 159.00, 199.00, 50, 12, '高清音质，长续航，舒适佩戴', 1, 1, 95, 1, '音质,续航,舒适', '限量特价'),
('room_001', 5, '家用小电器榨汁机', 'https://picsum.photos/300/300?random=5', 89.00, 129.00, 30, 8, '便携设计，一键操作，易清洗', 0, 0, 70, 1, '便携,易用,清洗', '满减优惠');
