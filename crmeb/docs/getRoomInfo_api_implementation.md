# getRoomInfo API 接口实现文档

## 接口概述

实现了 `/api/front/live/getRoomInfo` 接口，用于获取直播间的基本信息、WebSocket连接地址和直播流地址。

## 接口详情

### 请求信息
- **URL**: `/api/front/live/getRoomInfo`
- **Method**: `GET`
- **参数**: 
  - `roomId` (String, required): 直播间ID

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roomInfo": {
      "roomId": "room_001",
      "title": "SY主播的直播间",
      "cover": "https://img0.baidu.com/it/u=1195238836,824290916&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=500",
      "viewCount": 15,
      "status": 1,
      "startTime": 1697872000000,
      "endTime": 0
    },
    "socketUrl": "ws://localhost:8081/api/front/live/websocket/room_001",
    "liveUrl": "https://live.aimaibumai.com/live/room_001.flv"
  }
}
```

## 实现细节

### 1. Controller层实现
**文件**: `crmeb-front/src/main/java/com/zbkj/front/controller/LiveRoomController.java`

```java
@ApiOperation(value = "获取直播间信息")
@RequestMapping(value = "/getRoomInfo", method = RequestMethod.GET)
public CommonResult<LiveRoomInfoResponse> getRoomInfo(
        @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId) {
    LiveRoomInfoResponse response = liveRoomService.getRoomInfo(roomId);
    if (response == null) {
        return CommonResult.failed("直播间不存在");
    }
    return CommonResult.success(response);
}
```

### 2. Service层实现
**文件**: `crmeb-service/src/main/java/com/zbkj/service/service/impl/LiveRoomServiceImpl.java`

主要功能：
- 根据roomId查询数据库中的直播间信息
- 获取实时在线人数（通过WebSocket连接统计）
- 动态构建WebSocket连接地址
- 生成直播流地址

### 3. 响应对象定义
**文件**: `crmeb-common/src/main/java/com/zbkj/common/response/LiveRoomInfoResponse.java`

包含两个主要部分：
- `RoomInfo`: 直播间基本信息
- `socketUrl`: WebSocket连接地址
- `liveUrl`: 直播流地址

## 字段说明

### RoomInfo 对象
| 字段 | 类型 | 说明 |
|------|------|------|
| roomId | String | 直播间ID |
| title | String | 直播间标题 |
| cover | String | 直播间封面图片地址 |
| viewCount | Integer | 观看人数（实时在线人数） |
| status | Integer | 状态：0-未开始，1-直播中，2-已结束 |
| startTime | Long | 开始时间戳（毫秒） |
| endTime | Long | 结束时间戳（毫秒） |

### 其他字段
| 字段 | 类型 | 说明 |
|------|------|------|
| socketUrl | String | WebSocket连接地址 |
| liveUrl | String | 直播流地址 |

## 特性说明

### 1. 实时在线人数
- 优先返回WebSocket实时连接人数
- 如果没有在线用户，则返回数据库中的访问人数
- 通过 `LiveRoomWebSocketHandler.getRoomOnlineCount(roomId)` 获取

### 2. 动态地址生成
- **WebSocket地址**: 根据当前服务配置动态生成
- **直播流地址**: 根据roomId生成对应的流地址

### 3. 时间戳处理
- 数据库存储的是秒级时间戳
- 接口返回毫秒级时间戳（符合前端JavaScript标准）

## 使用示例

### 请求示例
```bash
curl -X GET "http://localhost:8081/api/front/live/getRoomInfo?roomId=room_001"
```

### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roomInfo": {
      "roomId": "room_001",
      "title": "SY主播的直播间",
      "cover": "https://img0.baidu.com/it/u=1195238836,824290916&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=500",
      "viewCount": 15,
      "status": 1,
      "startTime": 1697872000000,
      "endTime": 0
    },
    "socketUrl": "ws://localhost:8081/api/front/live/websocket/room_001",
    "liveUrl": "https://live.aimaibumai.com/live/room_001.flv"
  }
}
```

## 错误处理

### 直播间不存在
```json
{
  "code": 500,
  "msg": "直播间不存在",
  "data": null
}
```

## 前端集成

### JavaScript调用示例
```javascript
// 获取直播间信息
async function getRoomInfo(roomId) {
  try {
    const response = await fetch(`/api/front/live/getRoomInfo?roomId=${roomId}`);
    const result = await response.json();
    
    if (result.code === 200) {
      const { roomInfo, socketUrl, liveUrl } = result.data;
      
      // 更新页面信息
      updateRoomInfo(roomInfo);
      
      // 连接WebSocket
      connectWebSocket(socketUrl);
      
      // 初始化直播播放器
      initLivePlayer(liveUrl);
    } else {
      console.error('获取直播间信息失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

## 配置说明

### 可配置项
1. **WebSocket地址**: 可以通过配置文件设置协议、主机和端口
2. **直播流地址**: 可以根据不同环境配置不同的流媒体服务器地址

### 建议配置
- 开发环境: `ws://localhost:8081`
- 生产环境: `wss://your-domain.com`

## 注意事项

1. **安全性**: 生产环境建议使用HTTPS和WSS协议
2. **性能**: 在线人数统计基于内存，重启服务会重置
3. **扩展性**: 支持多直播间并发访问
4. **兼容性**: 时间戳格式兼容JavaScript Date对象

## 测试数据

系统预置了测试直播间：
- `room_001`: SY主播的直播间
- `room_002`: 美妆达人小雅的直播间  
- `room_003`: 时尚博主Lisa的直播间

可以使用这些roomId进行接口测试。
