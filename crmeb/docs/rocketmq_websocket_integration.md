# RocketMQ + WebSocket集成文档

## 集成概述

本次集成将WebSocket消息发送机制从直接发送改为通过RocketMQ异步处理，提高系统的可靠性、扩展性和解耦性。

## 架构设计

### 原架构
```
WebSocket接收消息 → 直接发送到Session → 客户端接收
```

### 新架构
```
WebSocket接收消息 → 发送到RocketMQ → 消费者处理 → 发送到Session → 客户端接收
```

### 架构优势
1. **解耦**: 消息生产和消费分离
2. **可靠性**: 消息持久化，防止丢失
3. **扩展性**: 支持多实例部署
4. **异步处理**: 提高响应速度
5. **流量削峰**: 处理突发流量

## 核心组件

### 1. 消息常量定义 (MqConstants)
```java
public class MqConstants {
    // Topic定义
    public static final String TOPIC_LIVE_WEBSOCKET = "LIVE_WEBSOCKET_TOPIC";
    
    // Tag定义
    public static final String TAG_SINGLE_MESSAGE = "SINGLE_MESSAGE";      // 单用户消息
    public static final String TAG_ROOM_BROADCAST = "ROOM_BROADCAST";      // 房间广播消息
    public static final String TAG_SYSTEM_MESSAGE = "SYSTEM_MESSAGE";      // 系统消息
    public static final String TAG_PRODUCT_MESSAGE = "PRODUCT_MESSAGE";    // 商品推荐消息
    public static final String TAG_ONLINE_COUNT = "ONLINE_COUNT";          // 在线人数更新
}
```

### 2. 消息实体 (WebSocketMessage)
```java
@Data
public class WebSocketMessage {
    private String messageId;           // 消息ID
    private String messageType;         // 消息类型
    private String targetType;          // 目标类型：SINGLE/ROOM/MULTI/ALL
    private String roomId;              // 房间ID
    private Integer userId;             // 用户ID
    private List<Integer> userIds;      // 多用户ID列表
    private String content;             // 消息内容（JSON格式）
    private Integer priority;           // 消息优先级
    private Integer delayLevel;         // 延迟级别
    private Integer retryCount;         // 重试次数
    private Date createTime;            // 创建时间
}
```

### 3. 消息生产者 (WebSocketMessageProducer)
负责将WebSocket消息发送到RocketMQ：
- `sendToUser()` - 发送单用户消息
- `sendToRoom()` - 发送房间广播消息
- `sendSystemMessage()` - 发送系统消息
- `sendProductMessage()` - 发送商品推荐消息
- `sendOnlineCountUpdate()` - 发送在线人数更新

### 4. 消息消费者 (WebSocketMessageConsumer)
负责从RocketMQ消费消息并发送到WebSocket：
- 支持并发消费
- 消息类型分发处理
- 重试机制
- 异常处理

### 5. 消息发送器 (WebSocketMessageSender)
负责将消息实际发送到WebSocket连接：
- 管理用户和房间连接映射
- 处理连接状态检查
- 批量消息发送优化

## 消息流程

### 1. 用户消息流程
```
用户发送消息 → NettyWebSocketHandler接收 → 保存数据库 → 发送到MQ → 消费者处理 → 广播到房间
```

### 2. 系统消息流程
```
系统事件触发 → 创建系统消息 → 发送到MQ → 消费者处理 → 发送到目标用户/房间
```

### 3. 商品推荐流程
```
管理员推荐商品 → 创建商品消息 → 发送到MQ → 消费者处理 → 广播到房间
```

## 配置说明

### RocketMQ配置
```yaml
rocketmq:
  name-server: localhost:9876
  producer:
    group: live-websocket-producer
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304
    compress-message-body-threshold: 4096
  consumer:
    group: live-websocket-consumer
    consume-thread-min: 5
    consume-thread-max: 20
    consume-message-batch-max-size: 1
```

### 消息优先级配置
- **高优先级(8)**: 系统消息、强制关闭
- **普通优先级(4)**: 用户消息、商品推荐
- **低优先级(1)**: 统计消息、在线人数更新

## 消息类型和处理

### 1. 单用户消息 (SINGLE)
```json
{
  "messageId": "WS_1697872000000_123",
  "messageType": "TEXT",
  "targetType": "SINGLE",
  "userId": 1001,
  "content": "{\"msgType\":3,\"content\":\"Hello\",\"username\":\"张三\"}"
}
```

### 2. 房间广播消息 (ROOM)
```json
{
  "messageId": "WS_1697872000001_124",
  "messageType": "TEXT",
  "targetType": "ROOM",
  "roomId": "room_001",
  "content": "{\"msgType\":3,\"content\":\"大家好\",\"username\":\"李四\"}"
}
```

### 3. 系统消息 (SYSTEM)
```json
{
  "messageId": "WS_1697872000002_125",
  "messageType": "WELCOME",
  "targetType": "ROOM",
  "roomId": "room_001",
  "priority": 8,
  "content": "{\"msgType\":2,\"content\":\"欢迎进入直播间\",\"username\":\"系统\"}"
}
```

## 性能优化

### 1. 消息批处理
- 消费者支持批量处理消息
- 减少网络开销
- 提高吞吐量

### 2. 连接池管理
- 复用WebSocket连接
- 自动清理无效连接
- 连接状态实时监控

### 3. 内存优化
- 消息对象池化
- 及时释放资源
- 避免内存泄漏

## 可靠性保证

### 1. 消息持久化
- RocketMQ自动持久化消息
- 防止消息丢失
- 支持消息回溯

### 2. 重试机制
- 消费失败自动重试
- 可配置重试次数
- 死信队列处理

### 3. 异常处理
- 完善的异常捕获
- 错误日志记录
- 优雅降级处理

## 监控和运维

### 1. 关键指标
- 消息生产速率
- 消息消费速率
- 消息积压数量
- 连接数统计
- 错误率统计

### 2. 日志记录
```
INFO  - 发送房间广播消息到MQ: roomId=room_001, messageId=WS_xxx
DEBUG - 消息处理完成: messageId=WS_xxx, targetType=ROOM
ERROR - 消息处理失败，准备重试: messageId=WS_xxx, retryCount=1
```

### 3. 健康检查
- RocketMQ连接状态检查
- WebSocket连接数监控
- 消息处理延迟监控

## 测试接口

### 1. 发送测试消息
```bash
# 发送房间消息
POST /api/test/sendToRoom?roomId=room_001&content=测试消息

# 发送用户消息
POST /api/test/sendToUser?userId=1001&content=私信消息

# 发送商品推荐
POST /api/test/sendProduct?roomId=room_001&productName=商品名&productId=123
```

### 2. 获取统计信息
```bash
# 房间统计
GET /api/test/roomStats/room_001

# 全局统计
GET /api/test/globalStats
```

## 部署说明

### 1. RocketMQ部署
```bash
# 启动NameServer
nohup sh mqnamesrv &

# 启动Broker
nohup sh mqbroker -n localhost:9876 &
```

### 2. 应用部署
```bash
# 构建应用
mvn clean package -DskipTests

# 启动应用
java -jar crmeb-ws-1.0.jar
```

### 3. 验证部署
```bash
# 检查RocketMQ状态
curl http://localhost:8083/api/health/check

# 测试消息发送
curl -X POST "http://localhost:8083/api/test/sendToRoom?roomId=room_001&content=测试"
```

## 故障排查

### 1. 常见问题
- **RocketMQ连接失败**: 检查NameServer地址和端口
- **消息发送失败**: 检查Topic和权限配置
- **消息积压**: 检查消费者线程数和处理速度

### 2. 调试工具
- RocketMQ Console: 查看Topic、消息、消费情况
- 应用日志: 查看详细的处理日志
- 监控面板: 实时监控系统状态

## 扩展功能

### 1. 消息过滤
- 基于Tag的消息过滤
- 自定义过滤规则
- 减少无效消息处理

### 2. 消息路由
- 基于用户属性的消息路由
- 地域化消息分发
- 个性化推送

### 3. 流量控制
- 消息发送频率限制
- 用户级别的流控
- 系统保护机制

这个集成方案提供了高可靠、高性能的WebSocket消息处理能力，支持大规模用户并发和复杂的业务场景。
