# WebSocket消息缓存功能测试指南

## 测试环境准备

### 1. 启动服务
```bash
# 启动后端服务
cd crmeb/crmeb-front
mvn spring-boot:run

# 服务运行在 http://localhost:8081
```

### 2. 准备测试数据
确保数据库中有测试直播间和消息数据：
```sql
-- 检查直播间数据
SELECT * FROM eb_live_room WHERE room_id = 'room_001';

-- 检查消息数据
SELECT * FROM eb_live_room_msg WHERE room_id = 'room_001' ORDER BY create_time DESC LIMIT 10;
```

## 功能测试

### 1. 欢迎消息测试

#### 测试步骤
1. 打开浏览器开发者工具
2. 连接WebSocket：
```javascript
const ws = new WebSocket('ws://localhost:8081/api/front/live/websocket/room_001');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};
```

#### 预期结果
连接建立后立即收到欢迎消息：
```json
{
  "roomId": "room_001",
  "msgType": 2,
  "content": "欢迎进入直播间！",
  "publisher": "SYSTEM",
  "username": "系统消息",
  "isNew": true
}
```

### 2. 历史消息测试

#### 测试步骤
1. 确保数据库中有历史消息
2. 新建WebSocket连接
3. 观察接收到的消息顺序

#### 预期结果
1. 首先收到欢迎消息
2. 然后按时间顺序收到历史消息（最多20条）
3. 历史消息的`isNew`字段为`false`

#### 验证代码
```javascript
let messageCount = 0;
let welcomeReceived = false;
let historyMessages = [];

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    messageCount++;
    
    if (message.msgType === 2 && message.content === '欢迎进入直播间！') {
        welcomeReceived = true;
        console.log('✓ 欢迎消息已接收');
    } else if (!message.isNew) {
        historyMessages.push(message);
        console.log(`历史消息 ${historyMessages.length}:`, message.content);
    }
};

// 5秒后检查结果
setTimeout(() => {
    console.log('测试结果:');
    console.log('- 欢迎消息:', welcomeReceived ? '✓' : '✗');
    console.log('- 历史消息数量:', historyMessages.length);
    console.log('- 总消息数:', messageCount);
}, 5000);
```

### 3. 实时消息测试

#### 测试步骤
1. 建立WebSocket连接
2. 等待历史消息接收完成
3. 发送新消息
4. 验证消息广播

#### 发送消息代码
```javascript
// 等待连接建立和历史消息接收完成
setTimeout(() => {
    const testMessage = {
        roomId: 'room_001',
        msgType: 1,
        content: '这是一条测试消息',
        username: '测试用户',
        avatar: 'https://picsum.photos/200/200?random=999'
    };
    
    ws.send(JSON.stringify(testMessage));
    console.log('发送测试消息:', testMessage.content);
}, 3000);
```

#### 预期结果
1. 消息保存到数据库
2. 消息添加到缓存
3. 所有连接的用户都收到消息
4. 新消息的`isNew`字段为`true`

### 4. 多用户测试

#### 测试步骤
1. 打开多个浏览器标签页
2. 每个标签页建立WebSocket连接
3. 观察在线人数更新
4. 测试消息广播

#### 多连接测试代码
```javascript
// 创建多个连接
const connections = [];
const userCount = 3;

for (let i = 0; i < userCount; i++) {
    const ws = new WebSocket('ws://localhost:8081/api/front/live/websocket/room_001');
    
    ws.onopen = function() {
        console.log(`用户${i+1}连接成功`);
    };
    
    ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        if (message.content && message.content.startsWith('ONLINE_COUNT_UPDATE:')) {
            const count = message.content.split(':')[1];
            console.log(`用户${i+1}收到在线人数更新: ${count}`);
        }
    };
    
    connections.push(ws);
}

// 5秒后用第一个连接发送消息
setTimeout(() => {
    const testMessage = {
        roomId: 'room_001',
        msgType: 1,
        content: '多用户测试消息',
        username: '用户1'
    };
    connections[0].send(JSON.stringify(testMessage));
}, 5000);
```

### 5. 缓存容量测试

#### 测试步骤
1. 连接到直播间
2. 快速发送超过20条消息
3. 断开重连
4. 检查历史消息数量

#### 批量发送消息代码
```javascript
let messagesSent = 0;
const totalMessages = 25; // 超过缓存容量

function sendBatchMessages() {
    const interval = setInterval(() => {
        if (messagesSent >= totalMessages) {
            clearInterval(interval);
            console.log(`已发送${messagesSent}条消息，等待5秒后重连测试`);
            
            // 5秒后重连测试缓存
            setTimeout(() => {
                ws.close();
                testCacheLimit();
            }, 5000);
            return;
        }
        
        const message = {
            roomId: 'room_001',
            msgType: 1,
            content: `测试消息 ${messagesSent + 1}`,
            username: '测试用户'
        };
        
        ws.send(JSON.stringify(message));
        messagesSent++;
    }, 100); // 每100ms发送一条
}

function testCacheLimit() {
    const newWs = new WebSocket('ws://localhost:8081/api/front/live/websocket/room_001');
    let historyCount = 0;
    
    newWs.onmessage = function(event) {
        const message = JSON.parse(event.data);
        if (!message.isNew && message.msgType === 1) {
            historyCount++;
            console.log(`历史消息${historyCount}: ${message.content}`);
        }
    };
    
    setTimeout(() => {
        console.log(`缓存测试结果: 收到${historyCount}条历史消息`);
        console.log(historyCount <= 20 ? '✓ 缓存容量限制正常' : '✗ 缓存容量超出限制');
        newWs.close();
    }, 3000);
}

// 开始测试
sendBatchMessages();
```

## 性能测试

### 1. 并发连接测试
```javascript
// 测试100个并发连接
const concurrentConnections = 100;
const connections = [];

for (let i = 0; i < concurrentConnections; i++) {
    setTimeout(() => {
        const ws = new WebSocket('ws://localhost:8081/api/front/live/websocket/room_001');
        connections.push(ws);
        
        ws.onopen = function() {
            console.log(`连接${i+1}建立成功`);
        };
        
        ws.onerror = function(error) {
            console.error(`连接${i+1}失败:`, error);
        };
    }, i * 10); // 每10ms建立一个连接
}
```

### 2. 消息发送频率测试
```javascript
// 测试高频消息发送
let messageCount = 0;
const startTime = Date.now();

const highFrequencyTest = setInterval(() => {
    const message = {
        roomId: 'room_001',
        msgType: 1,
        content: `高频消息 ${messageCount + 1}`,
        username: '压力测试'
    };
    
    ws.send(JSON.stringify(message));
    messageCount++;
    
    if (messageCount >= 100) {
        clearInterval(highFrequencyTest);
        const duration = Date.now() - startTime;
        console.log(`发送100条消息耗时: ${duration}ms`);
        console.log(`平均发送频率: ${(messageCount / duration * 1000).toFixed(2)} 消息/秒`);
    }
}, 10); // 每10ms发送一条消息
```

## 错误场景测试

### 1. 网络断开重连测试
```javascript
// 模拟网络断开
ws.close();

// 3秒后重连
setTimeout(() => {
    const newWs = new WebSocket('ws://localhost:8081/api/front/live/websocket/room_001');
    console.log('重连测试开始');
    
    newWs.onmessage = function(event) {
        const message = JSON.parse(event.data);
        console.log('重连后收到消息:', message);
    };
}, 3000);
```

### 2. 无效消息测试
```javascript
// 发送无效JSON
ws.send('invalid json');

// 发送缺少字段的消息
ws.send(JSON.stringify({roomId: 'room_001'}));

// 发送错误的房间ID
ws.send(JSON.stringify({
    roomId: 'invalid_room',
    msgType: 1,
    content: '错误房间消息'
}));
```

## 测试检查清单

### 功能测试
- [ ] 新用户连接收到欢迎消息
- [ ] 新用户收到历史消息（最多20条）
- [ ] 历史消息按时间顺序发送
- [ ] 实时消息正常广播
- [ ] 在线人数统计准确
- [ ] 用户断开连接正常处理

### 性能测试
- [ ] 多用户并发连接稳定
- [ ] 消息缓存容量限制有效
- [ ] 高频消息发送不丢失
- [ ] 内存使用合理
- [ ] 无人房间缓存自动清理

### 错误处理测试
- [ ] 网络断开重连正常
- [ ] 无效消息不影响系统
- [ ] 数据库异常不中断服务
- [ ] 连接异常自动清理

## 预期测试结果

### 正常情况
1. 新用户连接后立即收到欢迎消息
2. 随后收到最近20条历史消息
3. 实时消息正常收发和广播
4. 在线人数准确统计
5. 缓存容量限制在20条以内

### 异常处理
1. 网络异常时自动重连
2. 无效消息被忽略
3. 系统保持稳定运行
4. 资源自动清理
