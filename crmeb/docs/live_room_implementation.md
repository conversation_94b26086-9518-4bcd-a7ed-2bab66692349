# 直播间功能实现文档

## 功能概述

本次开发实现了完整的直播间功能，包括：

1. **动态数据获取**：直播间名称、主播信息等从数据库动态获取
2. **实时消息系统**：基于WebSocket的实时聊天功能
3. **在线人数统计**：实时显示WebSocket连接人数
4. **热卖商品展示**：从商品表动态获取热卖商品

## 技术架构

### 后端实现

#### 1. WebSocket配置
- **文件位置**: `crmeb-front/src/main/java/com/zbkj/front/config/WebSocketConfig.java`
- **功能**: 配置WebSocket端点和处理器

#### 2. WebSocket处理器
- **文件位置**: `crmeb-front/src/main/java/com/zbkj/front/websocket/LiveRoomWebSocketHandler.java`
- **功能**: 
  - 管理WebSocket连接
  - 处理消息广播
  - 统计在线人数
  - 消息持久化

#### 3. 业务服务层
- **LiveRoomService**: 直播间信息管理
- **LiveRoomMsgService**: 消息管理服务
- **文件位置**: `crmeb-service/src/main/java/com/zbkj/service/service/`

#### 4. API接口
- **文件位置**: `crmeb-front/src/main/java/com/zbkj/front/controller/LiveRoomController.java`
- **接口列表**:
  - `GET /api/front/live/room/{roomId}` - 获取直播间详情
  - `GET /api/front/live/messages/{roomId}` - 获取消息列表
  - `GET /api/front/live/hot-products` - 获取热卖商品
  - `GET /api/front/live/recent-messages/{roomId}` - 获取最近消息

### 前端实现

#### 1. 页面修改
- **文件位置**: `app/pages/live/index.vue`
- **主要改动**:
  - 移除硬编码数据
  - 添加API调用方法
  - 集成WebSocket连接
  - 实现实时消息收发

#### 2. WebSocket连接
- **连接地址**: `ws://localhost:8081/api/front/live/websocket/{roomId}`
- **消息格式**:
```json
{
  "roomId": "room_001",
  "msgType": 1,
  "content": "消息内容",
  "username": "用户名",
  "avatar": "头像URL"
}
```

## 数据库设计

### 直播间表 (eb_live_room)
```sql
CREATE TABLE `eb_live_room` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
  `anchor_id` int(11) NOT NULL COMMENT '主播ID',
  `avatar` varchar(255) DEFAULT NULL COMMENT '主播头像',
  `name` varchar(100) NOT NULL COMMENT '主播名称',
  `visitor_count` bigint(20) DEFAULT 0 COMMENT '访问人数',
  `start_time` bigint(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态 1-直播中 2-已结束',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_id` (`room_id`)
);
```

### 直播间消息表 (eb_live_room_msg)
```sql
CREATE TABLE `eb_live_room_msg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
  `publisher` varchar(100) NOT NULL COMMENT '发送者',
  `msg_type` bigint(20) DEFAULT 1 COMMENT '消息类型 1-文本消息 2-系统消息',
  `content` text NOT NULL COMMENT '消息内容',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_create_time` (`create_time`)
);
```

## 部署说明

### 1. 数据库初始化
执行SQL脚本：
```bash
mysql -u username -p database_name < crmeb/sql/live_room_data.sql
```

### 2. 后端启动
1. 确保已添加WebSocket依赖到pom.xml
2. 启动crmeb-front服务（端口8081）

### 3. 前端配置
1. 确保前端项目中的API地址配置正确
2. WebSocket连接地址需要根据实际部署环境调整

## 使用说明

### 1. 进入直播间
- 访问 `app/pages/live/index.vue` 页面
- 页面会自动加载直播间信息和历史消息

### 2. 发送消息
- 在输入框中输入消息
- 点击发送按钮或按回车键
- 消息会通过WebSocket实时广播给所有在线用户

### 3. 查看商品
- 点击购物车按钮打开商品抽屉
- 商品列表从数据库动态获取
- 支持商品详情查看和购买跳转

### 4. 在线人数
- 页面顶部显示实时在线人数
- 基于WebSocket连接数统计

## 测试数据

系统已预置测试数据：
- 3个直播间 (room_001, room_002, room_003)
- 每个直播间包含5-10条历史消息
- 主播信息和头像

## 注意事项

1. **WebSocket连接**：确保防火墙允许WebSocket连接
2. **跨域配置**：WebSocket配置中已设置允许跨域
3. **消息持久化**：所有消息都会保存到数据库
4. **连接重试**：前端会自动重连断开的WebSocket
5. **性能优化**：消息列表只显示最新8条，避免内存溢出

## 扩展功能

可以进一步扩展的功能：
1. 消息类型扩展（图片、表情、礼物）
2. 用户权限管理（禁言、踢出）
3. 直播间管理后台
4. 消息审核和过滤
5. 数据统计和分析
