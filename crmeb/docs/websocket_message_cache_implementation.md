# WebSocket消息缓存功能实现文档

## 功能概述

在直播间WebSocket功能中实现了消息缓存机制，为新用户提供更好的体验：

1. **消息缓存**: 每个直播间缓存最近20条消息
2. **欢迎消息**: 新用户连接时发送欢迎消息
3. **历史消息**: 自动发送直播间最近的消息记录
4. **实时消息**: 继续接收和广播实时消息

## 技术实现

### 1. 数据结构设计

```java
// 存储每个直播间最近的20条消息
private static final ConcurrentHashMap<String, LinkedList<LiveRoomMessageResponse>> roomMessageCache = new ConcurrentHashMap<>();

// 最大缓存消息数量
private static final int MAX_CACHE_MESSAGES = 20;
```

### 2. 核心功能流程

#### 2.1 新用户连接流程
```
用户连接 → 加入房间 → 发送欢迎消息 → 发送历史消息 → 广播在线人数更新
```

#### 2.2 消息处理流程
```
接收消息 → 保存到数据库 → 添加到缓存 → 广播给所有用户
```

#### 2.3 用户断开流程
```
用户断开 → 移除连接 → 广播在线人数更新 → 清理空房间缓存
```

## 功能详解

### 1. 欢迎消息功能

#### 实现方法
```java
private void sendWelcomeMessage(WebSocketSession session, String roomId)
```

#### 消息格式
```json
{
  "roomId": "room_001",
  "msgType": 2,
  "content": "欢迎进入直播间！",
  "publisher": "SYSTEM",
  "username": "系统消息",
  "avatar": "",
  "createTime": "2023-10-20T10:30:00.000Z",
  "isNew": true
}
```

#### 特点
- 消息类型为2（系统消息）
- 只发送给新连接的用户
- 立即发送，无延迟

### 2. 历史消息功能

#### 实现方法
```java
private void sendHistoryMessages(WebSocketSession session, String roomId)
```

#### 加载策略
1. **优先从缓存获取**: 检查内存中是否有缓存的消息
2. **数据库回退**: 缓存为空时从数据库加载最近20条消息
3. **按序发送**: 按时间顺序发送历史消息
4. **标记处理**: 历史消息标记为`isNew: false`

#### 发送特点
- 消息间隔10ms，避免发送过快
- 检查连接状态，断开时停止发送
- 按时间顺序发送（最早的先发送）

### 3. 消息缓存机制

#### 缓存策略
- **FIFO队列**: 使用LinkedList实现先进先出
- **容量限制**: 最多缓存20条消息
- **自动清理**: 超出容量时自动移除最早的消息

#### 缓存更新
```java
private void addMessageToCache(String roomId, LiveRoomMessageResponse message)
```

- 新消息添加到队列末尾
- 超出容量时移除队列头部消息
- 线程安全的并发操作

### 4. 数据库加载

#### 实现方法
```java
private void loadMessagesFromDatabase(String roomId)
```

#### 加载逻辑
1. 调用`liveRoomMsgService.getRecentMessages(roomId, 20)`
2. 按时间倒序获取消息
3. 转换为正序存储到缓存
4. 记录加载日志

### 5. 缓存清理机制

#### 清理时机
- 用户断开连接时检查
- 房间无人时自动清理
- 避免内存泄漏

#### 实现方法
```java
private void cleanupRoomCache(String roomId)
```

## 消息流程示例

### 新用户连接示例

```
1. 用户A连接到room_001
   → 发送: "欢迎进入直播间！"
   
2. 发送历史消息（假设有3条）
   → 发送: "用户B: 大家好"
   → 发送: "用户C: 主播唱得真好"
   → 发送: "用户D: 666"
   
3. 广播在线人数更新
   → 广播: "ONLINE_COUNT_UPDATE:1"
   
4. 用户A发送消息
   → 保存到数据库
   → 添加到缓存
   → 广播给所有用户
```

### 消息缓存更新示例

```
缓存状态（最多20条）:
[消息1, 消息2, ..., 消息19, 消息20]

新消息到达:
[消息2, 消息3, ..., 消息20, 新消息]  // 消息1被移除
```

## 性能优化

### 1. 内存管理
- 限制每个房间最多缓存20条消息
- 无人房间自动清理缓存
- 使用ConcurrentHashMap保证线程安全

### 2. 网络优化
- 历史消息发送间隔10ms，避免网络拥塞
- 检查连接状态，避免无效发送
- 消息压缩（JSON格式）

### 3. 数据库优化
- 优先使用内存缓存
- 只在缓存为空时查询数据库
- 批量加载减少数据库访问

## 配置参数

### 可调整参数
```java
// 最大缓存消息数量
private static final int MAX_CACHE_MESSAGES = 20;

// 历史消息发送间隔（毫秒）
Thread.sleep(10);
```

### 建议配置
- **开发环境**: 缓存10条消息，便于测试
- **生产环境**: 缓存20条消息，平衡性能和体验
- **高并发环境**: 可适当减少缓存数量

## 监控和日志

### 关键日志
```
INFO  - 用户连接到直播间: room_001, 当前房间人数: 5
INFO  - 发送欢迎消息到直播间: room_001
INFO  - 发送3条历史消息到直播间: room_001
INFO  - 从数据库加载10条消息到缓存，直播间: room_001
DEBUG - 添加消息到缓存，直播间: room_001, 当前缓存消息数: 15
INFO  - 清理直播间缓存: room_001
```

### 监控指标
- 每个房间的缓存消息数量
- 数据库加载频率
- 消息发送成功率
- 内存使用情况

## 错误处理

### 异常情况处理
1. **连接断开**: 停止发送历史消息
2. **数据库异常**: 记录错误日志，继续服务
3. **JSON解析失败**: 跳过错误消息
4. **内存不足**: 自动清理无人房间缓存

### 容错机制
- 发送失败不影响其他用户
- 缓存异常不影响实时消息
- 数据库异常时使用空缓存

## 使用效果

### 用户体验提升
1. **无缝体验**: 新用户立即看到聊天历史
2. **上下文连续**: 了解当前聊天话题
3. **快速融入**: 通过历史消息快速了解直播内容

### 系统性能
1. **减少数据库压力**: 缓存机制减少重复查询
2. **提高响应速度**: 内存访问比数据库快
3. **节省带宽**: 避免重复发送相同历史消息

## 后续优化建议

1. **消息去重**: 避免重复缓存相同消息
2. **智能清理**: 根据房间活跃度调整清理策略
3. **分级缓存**: 热门房间使用更大缓存
4. **持久化缓存**: 考虑使用Redis等外部缓存
5. **消息压缩**: 对历史消息进行压缩存储
