package com.zbkj.front.websocket;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.request.LiveRoomMessageRequest;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.service.service.LiveRoomMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 直播间WebSocket处理器
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Component
public class LiveRoomWebSocketHandler implements WebSocketHandler {

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    // 存储每个直播间的连接
    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<WebSocketSession>> roomSessions = new ConcurrentHashMap<>();
    
    // 存储session与房间的映射关系
    private static final ConcurrentHashMap<String, String> sessionRoomMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String roomId = getRoomIdFromSession(session);
        if (roomId != null) {
            // 将session加入到对应房间
            roomSessions.computeIfAbsent(roomId, k -> new CopyOnWriteArraySet<>()).add(session);
            sessionRoomMap.put(session.getId(), roomId);
            
            log.info("用户连接到直播间: {}, 当前房间人数: {}", roomId, roomSessions.get(roomId).size());
            
            // 广播在线人数更新
            broadcastOnlineCount(roomId);
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String roomId = sessionRoomMap.get(session.getId());
        if (roomId == null) {
            return;
        }

        try {
            String payload = message.getPayload().toString();
            LiveRoomMessageRequest messageRequest = JSON.parseObject(payload, LiveRoomMessageRequest.class);
            
            // 保存消息到数据库
            LiveRoomMessageResponse messageResponse = liveRoomMsgService.saveMessage(messageRequest);
            
            // 广播消息到房间内所有用户
            broadcastMessage(roomId, messageResponse);
            
        } catch (Exception e) {
            log.error("处理直播间消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
        removeSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String roomId = removeSession(session);
        if (roomId != null) {
            log.info("用户断开直播间连接: {}, 当前房间人数: {}", roomId, 
                    roomSessions.getOrDefault(roomId, new CopyOnWriteArraySet<>()).size());
            
            // 广播在线人数更新
            broadcastOnlineCount(roomId);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 从session中获取房间ID
     */
    private String getRoomIdFromSession(WebSocketSession session) {
        URI uri = session.getUri();
        if (uri != null) {
            String path = uri.getPath();
            String[] segments = path.split("/");
            if (segments.length > 0) {
                return segments[segments.length - 1];
            }
        }
        return null;
    }

    /**
     * 移除session
     */
    private String removeSession(WebSocketSession session) {
        String roomId = sessionRoomMap.remove(session.getId());
        if (roomId != null) {
            CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
            if (sessions != null) {
                sessions.remove(session);
                if (sessions.isEmpty()) {
                    roomSessions.remove(roomId);
                }
            }
        }
        return roomId;
    }

    /**
     * 广播消息到房间内所有用户
     */
    private void broadcastMessage(String roomId, LiveRoomMessageResponse message) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        if (sessions != null) {
            String messageJson = JSON.toJSONString(message);
            TextMessage textMessage = new TextMessage(messageJson);
            
            sessions.removeIf(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(textMessage);
                        return false;
                    } else {
                        return true;
                    }
                } catch (IOException e) {
                    log.error("发送消息失败: {}", e.getMessage());
                    return true;
                }
            });
        }
    }

    /**
     * 广播在线人数更新
     */
    private void broadcastOnlineCount(String roomId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        int onlineCount = sessions != null ? sessions.size() : 0;
        
        // 创建在线人数更新消息
        LiveRoomMessageResponse countMessage = new LiveRoomMessageResponse();
        countMessage.setRoomId(roomId);
        countMessage.setMsgType(2); // 系统消息
        countMessage.setContent("ONLINE_COUNT_UPDATE:" + onlineCount);
        countMessage.setPublisher("SYSTEM");
        
        broadcastMessage(roomId, countMessage);
    }

    /**
     * 获取房间在线人数
     */
    public static int getRoomOnlineCount(String roomId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        return sessions != null ? sessions.size() : 0;
    }
}
