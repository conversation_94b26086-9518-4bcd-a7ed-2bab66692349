package com.zbkj.front.biz.impl;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.common.response.LiveRoomResponse;
import com.zbkj.front.biz.LiveRoomBizService;
import com.zbkj.service.service.LiveRoomMsgService;
import com.zbkj.service.service.LiveRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 直播间业务逻辑服务实现类
 */
@Slf4j
@Service
public class LiveRoomBizServiceImpl implements LiveRoomBizService {

    @Autowired
    private LiveRoomService liveRoomService;

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

//    @Autowired
//    private WebSocketManager webSocketManager;

    @Override
    public LiveRoomInfoResponse getRoomInfo(String roomId) {
        // 从数据库获取基础信息
        LiveRoomInfoResponse response = liveRoomService.getRoomInfo(roomId);
        if (response == null) {
            return null;
        }

        // 整合WebSocket在线人数信息
//        int onlineCount = webSocketManager.getRoomOnlineCount(roomId);
//        if (onlineCount > 0) {
//            response.getRoomInfo().setViewCount(onlineCount);
//        }

        return response;
    }

    @Override
    public LiveRoomResponse getLiveRoomDetail(String roomId) {
        // 从数据库获取基础信息
        LiveRoomResponse response = liveRoomService.getLiveRoomDetail(roomId);
        if (response == null) {
            return null;
        }

        // 整合WebSocket在线人数信息
//        int onlineCount = webSocketManager.getRoomOnlineCount(roomId);
//        if (onlineCount > 0) {
//            response.setOnlineCount(onlineCount);
//        }

        return response;
    }

    @Override
    public CommonPage<LiveRoomMessageResponse> getMessageList(String roomId, PageParamRequest pageParamRequest) {
        return liveRoomMsgService.getMessageList(roomId, pageParamRequest);
    }

    @Override
    public List<LiveRoomMessageResponse> getRecentMessages(String roomId, Integer limit) {
        return liveRoomMsgService.getRecentMessages(roomId, limit);
    }

    @Override
    public List<IndexProductResponse> getHotProducts(Integer limit) {
        return liveRoomService.getHotProducts(limit);
    }
}
