package com.zbkj.front.biz;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.common.response.LiveRoomResponse;

import java.util.List;

/**
 * 直播间业务逻辑服务接口
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public interface LiveRoomBizService {

    /**
     * 获取直播间信息（整合数据库信息和WebSocket在线信息）
     * @param roomId 直播间ID
     * @return 直播间信息
     */
    LiveRoomInfoResponse getRoomInfo(String roomId);

    /**
     * 获取直播间详情（整合数据库信息和WebSocket在线信息）
     * @param roomId 直播间ID
     * @return 直播间详情
     */
    LiveRoomResponse getLiveRoomDetail(String roomId);

    /**
     * 获取直播间消息列表
     * @param roomId 直播间ID
     * @param pageParamRequest 分页参数
     * @return 消息列表
     */
    CommonPage<LiveRoomMessageResponse> getMessageList(String roomId, PageParamRequest pageParamRequest);

    /**
     * 获取直播间最近消息
     * @param roomId 直播间ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<LiveRoomMessageResponse> getRecentMessages(String roomId, Integer limit);

    /**
     * 获取热卖商品列表
     * @param limit 限制数量
     * @return 商品列表
     */
    List<IndexProductResponse> getHotProducts(Integer limit);
}
