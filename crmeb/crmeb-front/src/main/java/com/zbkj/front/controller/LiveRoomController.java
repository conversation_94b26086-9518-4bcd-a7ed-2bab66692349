package com.zbkj.front.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.common.response.LiveRoomResponse;
import com.zbkj.front.biz.LiveRoomBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播间控制器
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@RestController
@RequestMapping("api/front/live")
@Api(tags = "直播间相关接口")
public class LiveRoomController {

    @Autowired
    private LiveRoomService liveRoomService;

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    /**
     * 获取直播间信息
     */
    @ApiOperation(value = "获取直播间信息")
    @RequestMapping(value = "/getRoomInfo", method = RequestMethod.GET)
    public CommonResult<LiveRoomInfoResponse> getRoomInfo(
            @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId) {
        LiveRoomInfoResponse response = liveRoomService.getRoomInfo(roomId);
        if (response == null) {
            return CommonResult.failed("直播间不存在");
        }
        return CommonResult.success(response);
    }

    /**
     * 获取直播间详情
     */
    @ApiOperation(value = "获取直播间详情")
    @RequestMapping(value = "/room/{roomId}", method = RequestMethod.GET)
    public CommonResult<LiveRoomResponse> getLiveRoomDetail(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        LiveRoomResponse response = liveRoomService.getLiveRoomDetail(roomId);
        if (response == null) {
            return CommonResult.failed("直播间不存在");
        }
        return CommonResult.success(response);
    }

    /**
     * 获取直播间消息列表
     */
    @ApiOperation(value = "获取直播间消息列表")
    @RequestMapping(value = "/messages/{roomId}", method = RequestMethod.GET)
    public CommonResult<CommonPage<LiveRoomMessageResponse>> getMessageList(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<LiveRoomMessageResponse> messageList = liveRoomMsgService.getMessageList(roomId, pageParamRequest);
        return CommonResult.success(messageList);
    }

    /**
     * 获取热卖商品列表
     */
    @ApiOperation(value = "获取热卖商品列表")
    @RequestMapping(value = "/hot-products", method = RequestMethod.GET)
    public CommonResult<List<IndexProductResponse>> getHotProducts(
            @ApiParam(value = "限制数量", defaultValue = "10") @RequestParam(defaultValue = "10") Integer limit) {
        List<IndexProductResponse> hotProducts = liveRoomService.getHotProducts(limit);
        return CommonResult.success(hotProducts);
    }

    /**
     * 获取直播间最近消息
     */
    @ApiOperation(value = "获取直播间最近消息")
    @RequestMapping(value = "/recent-messages/{roomId}", method = RequestMethod.GET)
    public CommonResult<List<LiveRoomMessageResponse>> getRecentMessages(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId,
            @ApiParam(value = "限制数量", defaultValue = "20") @RequestParam(defaultValue = "20") Integer limit) {
        List<LiveRoomMessageResponse> recentMessages = liveRoomMsgService.getRecentMessages(roomId, limit);
        return CommonResult.success(recentMessages);
    }
}
