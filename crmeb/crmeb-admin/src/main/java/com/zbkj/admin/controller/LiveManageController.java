package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.model.live.LiveRoomProduct;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.service.service.LiveRoomService;
import com.zbkj.service.service.LiveRoomProductService;
import com.zbkj.service.service.LiveRoomMsgService;
import com.zbkj.service.service.StoreProductService;
import com.zbkj.common.model.product.StoreProduct;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播管理控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/live")
@Api(tags = "直播管理")
@Validated
public class LiveManageController {

    @Autowired
    private LiveRoomService liveRoomService;

    @Autowired
    private LiveRoomProductService liveRoomProductService;

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    @Autowired
    private StoreProductService storeProductService;

    // ==================== 直播间管理 ====================

    /**
     * 获取直播间列表
     */
    @PreAuthorize("hasAuthority('admin:live:room:list')")
    @ApiOperation(value = "获取直播间列表")
    @RequestMapping(value = "/room/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<LiveRoom>> getRoomList(@Validated PageParamRequest pageParamRequest) {
        CommonPage<LiveRoom> roomPage = liveRoomService.getList(pageParamRequest);
        return CommonResult.success(roomPage);
    }

    /**
     * 获取直播间详情
     */
    @PreAuthorize("hasAuthority('admin:live:room:info')")
    @ApiOperation(value = "获取直播间详情")
    @RequestMapping(value = "/room/info/{roomId}", method = RequestMethod.GET)
    public CommonResult<LiveRoom> getRoomInfo(@ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        LiveRoom room = liveRoomService.getByRoomId(roomId);
        if (room == null) {
            return CommonResult.failed("直播间不存在");
        }
        return CommonResult.success(room);
    }

    /**
     * 开始直播
     */
    @PreAuthorize("hasAuthority('admin:live:room:start')")
    @ApiOperation(value = "开始直播")
    @RequestMapping(value = "/room/start/{roomId}", method = RequestMethod.POST)
    public CommonResult<String> startLive(@ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        boolean success = liveRoomService.startLive(roomId);
        if (success) {
            return CommonResult.success("直播开始成功");
        } else {
            return CommonResult.failed("直播开始失败");
        }
    }

    /**
     * 结束直播
     */
    @PreAuthorize("hasAuthority('admin:live:room:stop')")
    @ApiOperation(value = "结束直播")
    @RequestMapping(value = "/room/stop/{roomId}", method = RequestMethod.POST)
    public CommonResult<String> stopLive(@ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        boolean success = liveRoomService.stopLive(roomId);
        if (success) {
            return CommonResult.success("直播结束成功");
        } else {
            return CommonResult.failed("直播结束失败");
        }
    }

    /**
     * 创建直播间
     */
    @PreAuthorize("hasAuthority('admin:live:room:create')")
    @ApiOperation(value = "创建直播间")
    @RequestMapping(value = "/room/create", method = RequestMethod.POST)
    public CommonResult<String> createRoom(@RequestBody @Validated LiveRoom liveRoom) {
        boolean success = liveRoomService.create(liveRoom);
        if (success) {
            return CommonResult.success("直播间创建成功");
        } else {
            return CommonResult.failed("直播间创建失败");
        }
    }

    /**
     * 更新直播间信息
     */
    @PreAuthorize("hasAuthority('admin:live:room:update')")
    @ApiOperation(value = "更新直播间信息")
    @RequestMapping(value = "/room/update", method = RequestMethod.POST)
    public CommonResult<String> updateRoom(@RequestBody @Validated LiveRoom liveRoom) {
        boolean success = liveRoomService.updateById(liveRoom);
        if (success) {
            return CommonResult.success("直播间更新成功");
        } else {
            return CommonResult.failed("直播间更新失败");
        }
    }

    /**
     * 删除直播间
     */
    @PreAuthorize("hasAuthority('admin:live:room:delete')")
    @ApiOperation(value = "删除直播间")
    @RequestMapping(value = "/room/delete/{roomId}", method = RequestMethod.DELETE)
    public CommonResult<String> deleteRoom(@ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        boolean success = liveRoomService.deleteByRoomId(roomId);
        if (success) {
            return CommonResult.success("直播间删除成功");
        } else {
            return CommonResult.failed("直播间删除失败");
        }
    }

    // ==================== 直播间消息管理 ====================

    /**
     * 获取直播间消息列表
     */
    @PreAuthorize("hasAuthority('admin:live:message:list')")
    @ApiOperation(value = "获取直播间消息列表")
    @RequestMapping(value = "/message/list/{roomId}", method = RequestMethod.GET)
    public CommonResult<CommonPage<LiveRoomMessageResponse>> getMessageList(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<LiveRoomMessageResponse> messagePage = liveRoomMsgService.getPageByRoomId(roomId, pageParamRequest);
        return CommonResult.success(messagePage);
    }

    /**
     * 删除消息
     */
    @PreAuthorize("hasAuthority('admin:live:message:delete')")
    @ApiOperation(value = "删除消息")
    @RequestMapping(value = "/message/delete/{messageId}", method = RequestMethod.DELETE)
    public CommonResult<String> deleteMessage(@ApiParam(value = "消息ID", required = true) @PathVariable Integer messageId) {
        boolean success = liveRoomMsgService.removeById(messageId);
        if (success) {
            return CommonResult.success("消息删除成功");
        } else {
            return CommonResult.failed("消息删除失败");
        }
    }

    /**
     * 批量删除消息
     */
    @PreAuthorize("hasAuthority('admin:live:message:delete')")
    @ApiOperation(value = "批量删除消息")
    @RequestMapping(value = "/message/batchDelete", method = RequestMethod.POST)
    public CommonResult<String> batchDeleteMessage(@RequestBody List<Integer> messageIds) {
        boolean success = liveRoomMsgService.removeByIds(messageIds);
        if (success) {
            return CommonResult.success("消息批量删除成功");
        } else {
            return CommonResult.failed("消息批量删除失败");
        }
    }

    /**
     * 清空直播间消息
     */
    @PreAuthorize("hasAuthority('admin:live:message:clear')")
    @ApiOperation(value = "清空直播间消息")
    @RequestMapping(value = "/message/clear/{roomId}", method = RequestMethod.POST)
    public CommonResult<String> clearRoomMessage(@ApiParam(value = "直播间ID", required = true) @PathVariable String roomId) {
        boolean success = liveRoomMsgService.clearByRoomId(roomId);
        if (success) {
            return CommonResult.success("直播间消息清空成功");
        } else {
            return CommonResult.failed("直播间消息清空失败");
        }
    }

    // ==================== 直播间商品管理 ====================

    /**
     * 获取直播间商品列表
     */
    @PreAuthorize("hasAuthority('admin:live:product:list')")
    @ApiOperation(value = "获取直播间商品列表")
    @RequestMapping(value = "/product/list/{roomId}", method = RequestMethod.GET)
    public CommonResult<CommonPage<LiveRoomProduct>> getProductList(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<LiveRoomProduct> productPage = liveRoomProductService.getByRoomIdPage(roomId, pageParamRequest);
        return CommonResult.success(productPage);
    }

    /**
     * 获取可添加的商品列表
     */
    @PreAuthorize("hasAuthority('admin:live:product:available')")
    @ApiOperation(value = "获取可添加的商品列表")
    @RequestMapping(value = "/product/available/{roomId}", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreProduct>> getAvailableProducts(
            @ApiParam(value = "直播间ID", required = true) @PathVariable String roomId,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<StoreProduct> productPage = storeProductService.getAvailableForLiveRoom(roomId, pageParamRequest);
        return CommonResult.success(productPage);
    }

    /**
     * 添加商品到直播间
     */
    @PreAuthorize("hasAuthority('admin:live:product:add')")
    @ApiOperation(value = "添加商品到直播间")
    @RequestMapping(value = "/product/add", method = RequestMethod.POST)
    public CommonResult<String> addProduct(
            @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId) {
        boolean success = liveRoomProductService.addProductToRoom(roomId, productId);
        if (success) {
            return CommonResult.success("商品添加成功");
        } else {
            return CommonResult.failed("商品添加失败");
        }
    }

    /**
     * 从直播间移除商品
     */
    @PreAuthorize("hasAuthority('admin:live:product:remove')")
    @ApiOperation(value = "从直播间移除商品")
    @RequestMapping(value = "/product/remove", method = RequestMethod.POST)
    public CommonResult<String> removeProduct(
            @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId) {
        boolean success = liveRoomProductService.removeProductFromRoom(roomId, productId);
        if (success) {
            return CommonResult.success("商品移除成功");
        } else {
            return CommonResult.failed("商品移除失败");
        }
    }

    /**
     * 更新商品排序
     */
    @PreAuthorize("hasAuthority('admin:live:product:sort')")
    @ApiOperation(value = "更新商品排序")
    @RequestMapping(value = "/product/sort", method = RequestMethod.POST)
    public CommonResult<String> updateProductSort(
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer id,
            @ApiParam(value = "排序权重", required = true) @RequestParam Integer sort) {
        boolean success = liveRoomProductService.updateSort(id, sort);
        if (success) {
            return CommonResult.success("商品排序更新成功");
        } else {
            return CommonResult.failed("商品排序更新失败");
        }
    }

    /**
     * 更新商品推荐状态
     */
    @PreAuthorize("hasAuthority('admin:live:product:recommend')")
    @ApiOperation(value = "更新商品推荐状态")
    @RequestMapping(value = "/product/recommend", method = RequestMethod.POST)
    public CommonResult<String> updateRecommendStatus(
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer id,
            @ApiParam(value = "是否推荐", required = true) @RequestParam Boolean isRecommend) {
        boolean success = liveRoomProductService.updateRecommendStatus(id, isRecommend);
        if (success) {
            return CommonResult.success("商品推荐状态更新成功");
        } else {
            return CommonResult.failed("商品推荐状态更新失败");
        }
    }

    /**
     * 更新商品热卖状态
     */
    @PreAuthorize("hasAuthority('admin:live:product:hot')")
    @ApiOperation(value = "更新商品热卖状态")
    @RequestMapping(value = "/product/hot", method = RequestMethod.POST)
    public CommonResult<String> updateHotStatus(
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer id,
            @ApiParam(value = "是否热卖", required = true) @RequestParam Boolean isHot) {
        boolean success = liveRoomProductService.updateHotStatus(id, isHot);
        if (success) {
            return CommonResult.success("商品热卖状态更新成功");
        } else {
            return CommonResult.failed("商品热卖状态更新失败");
        }
    }

    /**
     * 开始讲解商品
     */
    @PreAuthorize("hasAuthority('admin:live:product:explain')")
    @ApiOperation(value = "开始讲解商品")
    @RequestMapping(value = "/product/startExplain", method = RequestMethod.POST)
    public CommonResult<String> startExplaining(
            @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId) {
        boolean success = liveRoomProductService.startExplaining(roomId, productId);
        if (success) {
            return CommonResult.success("开始讲解商品成功");
        } else {
            return CommonResult.failed("开始讲解商品失败");
        }
    }

    /**
     * 结束讲解商品
     */
    @PreAuthorize("hasAuthority('admin:live:product:explain')")
    @ApiOperation(value = "结束讲解商品")
    @RequestMapping(value = "/product/stopExplain", method = RequestMethod.POST)
    public CommonResult<String> stopExplaining(
            @ApiParam(value = "直播间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId) {
        boolean success = liveRoomProductService.stopExplaining(roomId, productId);
        if (success) {
            return CommonResult.success("结束讲解商品成功");
        } else {
            return CommonResult.failed("结束讲解商品失败");
        }
    }
}
