#!/bin/bash

echo "开始编译检查..."

# 编译common模块
echo "编译 crmeb-common..."
cd crmeb-common
mvn clean compile -DskipTests -q
if [ $? -ne 0 ]; then
    echo "crmeb-common 编译失败"
    exit 1
fi
echo "crmeb-common 编译成功"
cd ..

# 编译service模块
echo "编译 crmeb-service..."
cd crmeb-service
mvn clean compile -DskipTests -q
if [ $? -ne 0 ]; then
    echo "crmeb-service 编译失败"
    exit 1
fi
echo "crmeb-service 编译成功"
cd ..

# 编译admin模块
echo "编译 crmeb-admin..."
cd crmeb-admin
mvn clean compile -DskipTests -q
if [ $? -ne 0 ]; then
    echo "crmeb-admin 编译失败"
    exit 1
fi
echo "crmeb-admin 编译成功"
cd ..

echo "所有模块编译成功！"
