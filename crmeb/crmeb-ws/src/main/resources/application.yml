# CRMEB 相关配置
crmeb:
  version: CRMEB-JAVA-KY-v1.3.4 # 当前代码版本

# 配置端口
server:
  port: 8083  # Spring Boot端口，用于健康检查等

spring:
  profiles:
    #  配置的环境
    #    active: #spring.profiles.active#
    active: dev

  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${crmeb.filePath}
  application:
    name: cemrb-ws
  jackson:
    locale: zh_CN
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************************
    username: crmeb
    password: 111111
  redis:
    host: 127.0.0.1 #地址
    port: 6379 #端口
    password: 111111
    timeout: 30000 # 连接超时时间（毫秒）
    database: 3  #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1

debug: true
logging:
  level:
    io.swagger.*: error
    com.zbjk.crmeb: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml
  file:
    path: ./crmeb_log

# mybatis 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*/*Mapper.xml #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  # 配置slq打印日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
#      logic-delete-field: isDel  #全局逻辑删除字段值 3.3.0开始支持，详情看下面。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)


#swagger 配置
swagger:
  basic:
    enable: true #是否开启
    check: false #是否打开验证
    username: #访问swagger的账号
    password: #访问swagger的密码


# RocketMQ配置
rocketmq:
  producer:
    access-key: Za2l2S2nF0fvX8nG
    secret-key: Yc27W6xSuoSrQj7R
    endpoints: rmq-cn-v3m4c7qn009.cn-beijing.rmq.aliyuncs.com:8080
    topic: live-messages
    namespace: rmq-cn-v3m4c7qn009
  simple-consumer:
    access-key: Za2l2S2nF0fvX8nG
    secret-key: Yc27W6xSuoSrQj7R
    endpoints: rmq-cn-v3m4c7qn009.cn-beijing.rmq.aliyuncs.com:8080
    topic: live-messages
    namespace: rmq-cn-v3m4c7qn009
    consumer-group: live-message-pusher

