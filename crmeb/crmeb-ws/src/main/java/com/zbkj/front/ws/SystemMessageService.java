package com.zbkj.front.ws;

import com.zbkj.common.enums.LiveRoomMessageTypeEnum;
import com.zbkj.common.model.user.User;
import com.zbkj.common.response.LiveRoomMessageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 系统消息服务
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class SystemMessageService {

    /**
     * 创建欢迎消息
     * @param roomId 直播间ID
     * @param user 用户信息
     * @return 欢迎消息
     */
    public LiveRoomMessageResponse createWelcomeMessage(String roomId, User user) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setRoomId(roomId);
        message.setMsgType(LiveRoomMessageTypeEnum.RES_WELCOME_MSG.getCode());
        message.setContent(String.format("欢迎 %s 进入直播间！", user.getNickname()));
        message.setPublisher("SYSTEM");
        message.setUsername("系统消息");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.info("创建欢迎消息: roomId={}, userId={}, nickname={}", roomId, user.getUid(), user.getNickname());
        return message;
    }

    /**
     * 创建用户离开消息
     * @param roomId 直播间ID
     * @param user 用户信息
     * @return 离开消息
     */
    public LiveRoomMessageResponse createLeaveMessage(String roomId, User user) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setRoomId(roomId);
        message.setMsgType(LiveRoomMessageTypeEnum.RES_SYSTEM_MSG.getCode());
        message.setContent(String.format("%s 离开了直播间", user.getNickname()));
        message.setPublisher("SYSTEM");
        message.setUsername("系统消息");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.info("创建离开消息: roomId={}, userId={}, nickname={}", roomId, user.getUid(), user.getNickname());
        return message;
    }

    /**
     * 创建在线人数更新消息
     * @param roomId 直播间ID
     * @param onlineCount 在线人数
     * @return 在线人数消息
     */
    public LiveRoomMessageResponse createOnlineCountMessage(String roomId, int onlineCount) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setRoomId(roomId);
        message.setMsgType(LiveRoomMessageTypeEnum.RES_SYSTEM_MSG.getCode());
        message.setContent("ONLINE_COUNT_UPDATE:" + onlineCount);
        message.setPublisher("SYSTEM");
        message.setUsername("系统消息");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.debug("创建在线人数更新消息: roomId={}, onlineCount={}", roomId, onlineCount);
        return message;
    }

    /**
     * 创建商品讲解消息
     * @param roomId 直播间ID
     * @param productName 商品名称
     * @param productId 商品ID
     * @param productImage 商品图片
     * @param productPrice 商品价格
     * @return 商品讲解消息
     */
    public LiveRoomMessageResponse createProductMessage(String roomId, String productName, 
                                                       Integer productId, String productImage, String productPrice) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setRoomId(roomId);
        message.setMsgType(LiveRoomMessageTypeEnum.RES_PROD_ON_SALE.getCode());
        
        // 构建商品信息JSON
        String productInfo = String.format("{\"productId\":%d,\"productName\":\"%s\",\"productImage\":\"%s\",\"productPrice\":\"%s\"}", 
                                         productId, productName, productImage, productPrice);
        message.setContent(productInfo);
        message.setPublisher("SYSTEM");
        message.setUsername("商品推荐");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.info("创建商品讲解消息: roomId={}, productId={}, productName={}", roomId, productId, productName);
        return message;
    }

    /**
     * 创建强制关闭连接消息
     * @param reason 关闭原因
     * @return 强制关闭消息
     */
    public LiveRoomMessageResponse createForceCloseMessage(String reason) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setMsgType(LiveRoomMessageTypeEnum.RES_FORCE_CLOSE.getCode());
        message.setContent(reason);
        message.setPublisher("SYSTEM");
        message.setUsername("系统消息");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.warn("创建强制关闭连接消息: reason={}", reason);
        return message;
    }

    /**
     * 创建认证成功消息
     * @param roomId 直播间ID
     * @param user 用户信息
     * @return 认证成功消息
     */
    public LiveRoomMessageResponse createAuthSuccessMessage(String roomId, User user) {
        LiveRoomMessageResponse message = new LiveRoomMessageResponse();
        message.setRoomId(roomId);
        message.setMsgType(LiveRoomMessageTypeEnum.RES_SYSTEM_MSG.getCode());
        message.setContent("AUTH_SUCCESS");
        message.setPublisher("SYSTEM");
        message.setUsername("系统消息");
        message.setAvatar("");
        message.setCreateTime(new Date());
        message.setIsNew(true);
        
        log.info("创建认证成功消息: roomId={}, userId={}", roomId, user.getUid());
        return message;
    }
}
