package com.zbkj.front.mq;

import com.alibaba.fastjson.JSON;
import com.zbkj.front.config.RocketmqProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.SessionCredentialsProvider;
import org.apache.rocketmq.client.apis.StaticSessionCredentialsProvider;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.ProducerBuilder;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 消息生产者
 */
@Slf4j
@Service
public class MessageProducer implements SmartLifecycle {

    private ClientServiceProvider clientServiceProvider;

    private final RocketmqProperties rmqProperties;

    private Producer producer;
    private boolean running;

    public MessageProducer(RocketmqProperties rmqProperties) {
        this.rmqProperties = rmqProperties;
    }

    @Override
    public void start() {
        clientServiceProvider = ClientServiceProvider.loadService();

        try {
            SessionCredentialsProvider sessionCredentialsProvider =
                    new StaticSessionCredentialsProvider(rmqProperties.getAccessKey(), rmqProperties.getSecretKey());
            ClientConfiguration clientConfiguration = ClientConfiguration.newBuilder()
                    .setEndpoints(rmqProperties.getEndpoints())
                    .setNamespace(rmqProperties.getNamespace())
                    .setCredentialProvider(sessionCredentialsProvider)
                    .build();
            final ProducerBuilder builder = clientServiceProvider.newProducerBuilder()
                    .setClientConfiguration(clientConfiguration)
                    .setTopics(rmqProperties.getProducer().getTopic());
            producer = builder.build();
            this.running = true;
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void stop() {
        try {
            producer.close();
            this.running = false;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean isRunning() {
        return this.running;
    }

    /**
     * 发送消息到RocketMQ
     */
    public void sendMessage(Object msg, String... keys) {
        try {
            Message message = clientServiceProvider.newMessageBuilder()
                    .setTopic(rmqProperties.getProducer().getTopic())
                    .setBody(JSON.toJSONBytes(msg))
                    .setKeys(keys)
                    .build();

            SendReceipt sendReceipt = this.producer.send(message);
            log.info("发送消息到MQ成功: messageId={}", sendReceipt.getMessageId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
