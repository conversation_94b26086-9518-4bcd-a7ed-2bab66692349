package com.zbkj.front.ws;

import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.user.User;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * WebSocket认证服务
 */
@Slf4j
@Service
public class WebSocketAuthService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserService userService;

    /**
     * 验证token并获取用户信息
     * @param token 用户token
     * @return 用户信息，验证失败返回null
     */
    public User authenticateUser(String token) {
        if (StringUtils.isEmpty(token)) {
            log.warn("WebSocket认证失败: token为空");
            return null;
        }

        try {
            // 构建Redis key
            String tokenKey = Constants.USER_TOKEN_REDIS_KEY_PREFIX + token;
            
            // 检查token是否存在
            if (!redisUtil.exists(tokenKey)) {
                log.warn("WebSocket认证失败: token不存在或已过期, token={}", token);
                return null;
            }

            // 获取用户ID
            Integer userId = redisUtil.get(tokenKey);
            if (userId == null) {
                log.warn("WebSocket认证失败: 无法获取用户ID, token={}", token);
                return null;
            }

            // 获取用户信息
            User user = userService.getInfoByUid(userId);
            if (user == null) {
                log.warn("WebSocket认证失败: 用户不存在, userId={}", userId);
                return null;
            }

            // 检查用户状态
            if (!user.getStatus()) {
                log.warn("WebSocket认证失败: 用户已被禁用, userId={}", userId);
                return null;
            }

            // 刷新token过期时间
            redisUtil.set(tokenKey, userId, Constants.TOKEN_EXPRESS_MINUTES, java.util.concurrent.TimeUnit.MINUTES);

            log.info("WebSocket认证成功: userId={}, nickname={}", user.getUid(), user.getNickname());
            return user;

        } catch (Exception e) {
            log.error("WebSocket认证异常: token={}, error={}", token, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证用户是否有权限访问指定直播间
     * @param user 用户信息
     * @param roomId 直播间ID
     * @return 是否有权限
     */
    public boolean hasRoomPermission(User user, String roomId) {
        // 这里可以添加更复杂的权限验证逻辑
        // 例如：检查用户是否被禁止进入某个直播间、是否是VIP用户等
        
        if (user == null || StringUtils.isEmpty(roomId)) {
            return false;
        }

        // 基础权限检查：用户状态正常
        if (!user.getStatus()) {
            log.warn("用户状态异常，无权限进入直播间: userId={}, roomId={}", user.getUid(), roomId);
            return false;
        }

        // 可以在这里添加更多权限检查逻辑
        // 例如：检查黑名单、VIP权限、房间访问限制等

        return true;
    }
}
