//package com.zbkj.front.netty;
//
//import io.netty.bootstrap.ServerBootstrap;
//import io.netty.channel.ChannelFuture;
//import io.netty.channel.ChannelInitializer;
//import io.netty.channel.ChannelOption;
//import io.netty.channel.EventLoopGroup;
//import io.netty.channel.nio.NioEventLoopGroup;
//import io.netty.channel.socket.SocketChannel;
//import io.netty.channel.socket.nio.NioServerSocketChannel;
//import io.netty.handler.codec.http.HttpObjectAggregator;
//import io.netty.handler.codec.http.HttpServerCodec;
//import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
//import io.netty.handler.logging.LogLevel;
//import io.netty.handler.logging.LoggingHandler;
//import io.netty.handler.stream.ChunkedWriteHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//
///**
// * Netty WebSocket服务器
// */
//@Slf4j
//@Component
//public class NettyWebSocketServer {
//
//    @Value("${netty.websocket.port:8082}")
//    private int port;
//
//    @Value("${netty.websocket.path:/chat}")
//    private String path;
//
//    @Autowired
//    private NettyWebSocketHandler webSocketHandler;
//
//    private EventLoopGroup bossGroup;
//    private EventLoopGroup workerGroup;
//    private ChannelFuture channelFuture;
//
//    @PostConstruct
//    public void start() {
//        new Thread(() -> {
//            try {
//                bossGroup = new NioEventLoopGroup(1);
//                workerGroup = new NioEventLoopGroup();
//
//                ServerBootstrap bootstrap = new ServerBootstrap();
//                bootstrap.group(bossGroup, workerGroup)
//                        .channel(NioServerSocketChannel.class)
//                        .option(ChannelOption.SO_BACKLOG, 1024)
//                        .option(ChannelOption.SO_REUSEADDR, true)
//                        .childOption(ChannelOption.SO_KEEPALIVE, true)
//                        .childOption(ChannelOption.TCP_NODELAY, true)
//                        .handler(new LoggingHandler(LogLevel.INFO))
//                        .childHandler(new ChannelInitializer<SocketChannel>() {
//                            @Override
//                            protected void initChannel(SocketChannel ch) throws Exception {
//                                ch.pipeline()
//                                        // HTTP编解码器
//                                        .addLast(new HttpServerCodec())
//                                        // HTTP对象聚合器
//                                        .addLast(new HttpObjectAggregator(65536))
//                                        // 支持大文件传输
//                                        .addLast(new ChunkedWriteHandler())
//                                        // WebSocket路径处理器
//                                        .addLast(new WebSocketPathHandler())
//                                        // WebSocket协议处理器
//                                        .addLast(new WebSocketServerProtocolHandler(path + "/*", null, true, 65536))
//                                        // 自定义WebSocket处理器
//                                        .addLast(webSocketHandler);
//                            }
//                        });
//
//                channelFuture = bootstrap.bind(port).sync();
//                log.info("Netty WebSocket服务器启动成功，端口: {}, 路径: {}", port, path);
//
//                channelFuture.channel().closeFuture().sync();
//            } catch (Exception e) {
//                log.error("Netty WebSocket服务器启动失败", e);
//            } finally {
//                shutdown();
//            }
//        }, "NettyWebSocketServer").start();
//    }
//
//    @PreDestroy
//    public void shutdown() {
//        try {
//            if (channelFuture != null) {
//                channelFuture.channel().close().sync();
//            }
//        } catch (InterruptedException e) {
//            log.error("关闭Netty服务器失败", e);
//        } finally {
//            if (bossGroup != null) {
//                bossGroup.shutdownGracefully();
//            }
//            if (workerGroup != null) {
//                workerGroup.shutdownGracefully();
//            }
//            log.info("Netty WebSocket服务器已关闭");
//        }
//    }
//}
