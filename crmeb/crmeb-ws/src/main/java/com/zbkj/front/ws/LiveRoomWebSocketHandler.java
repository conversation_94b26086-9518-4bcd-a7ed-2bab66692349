package com.zbkj.front.ws;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.enums.LiveRoomMessageTypeEnum;
import com.zbkj.common.model.user.User;
import com.zbkj.common.request.LiveRoomMessageRequest;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.front.mq.MessageProducer;
import com.zbkj.service.service.LiveRoomMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 直播间WebSocket处理器
 */
@Slf4j
public class LiveRoomWebSocketHandler implements WebSocketHandler {

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    @Autowired
    private WebSocketAuthService webSocketAuthService;

    @Autowired
    private SystemMessageService systemMessageService;

    @Resource
    private MessageProducer messageProducer;

    /**
     * 存储每个用户的连接
     * key: userId, value: session对象
     */
    private static final ConcurrentHashMap<Integer, WebSocketSession> userSessions = new ConcurrentHashMap<>();

    /**
     * 存储每个直播间的连接
     * key: roomId, value: session集合
     */
    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<WebSocketSession>> roomSessions = new ConcurrentHashMap<>();

    /**
     * 存储session与房间的映射关系
     * key: sessionId, value: roomId
     */
    private static final ConcurrentHashMap<String, String> sessionRoomMap = new ConcurrentHashMap<>();

    /**
     * 存储session与用户的映射关系
     * key: sessionId, value: User对象
     */
    private static final ConcurrentHashMap<String, User> sessionUserMap = new ConcurrentHashMap<>();


    // 最大缓存消息数量
    private static final int MAX_CACHE_MESSAGES = 20;

    // 未认证连接的超时时间（毫秒）
    private static final long AUTH_TIMEOUT = 30000; // 30秒

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String roomId = getRoomIdFromSession(session);
        if (StringUtils.isEmpty(roomId)) {
            log.warn("WebSocket连接建立失败: 无法获取房间ID, sessionId={}", session.getId());
            session.close(CloseStatus.BAD_DATA);
            return;
        }

        log.info("WebSocket连接建立: sessionId={}, roomId={}, 等待用户认证", session.getId(), roomId);

    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();

        log.debug("收到WebSocket消息: sessionId={}, payload={}", sessionId, payload);

        // 解析消息
        LiveRoomMessageRequest messageRequest;
        try {
            messageRequest = JSON.parseObject(payload, LiveRoomMessageRequest.class);
        } catch (Exception e) {
            log.error("解析WebSocket消息失败: sessionId={}, payload={}, error={}", sessionId, payload, e.getMessage());
            sendForceCloseMessage(session, "消息格式错误");
            return;
        }

        String roomId = getRoomIdFromSession(session);
        if (StringUtils.isEmpty(roomId)) {
            log.error("无法获取房间ID: sessionId={}", sessionId);
            sendForceCloseMessage(session, "房间ID无效");
            return;
        }

        // 检查是否为认证消息
        if (messageRequest.getMsgType() == LiveRoomMessageTypeEnum.REQ_AUTH.getCode()) {
            handleAuthMessage(session, roomId, messageRequest);
            return;
        }

        // 检查用户是否已认证
        User user = sessionUserMap.get(sessionId);
        if (user == null) {
            log.warn("未认证用户尝试发送消息: sessionId={}, roomId={}", sessionId, roomId);
            sendForceCloseMessage(session, "请先进行身份认证");
            return;
        }

        // 处理已认证用户的消息
        handleUserMessage(session, roomId, user, messageRequest);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
        removeSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        String roomId = removeSession(session);
        User user = sessionUserMap.remove(sessionId);

        if (roomId != null) {
            int currentCount = roomSessions.getOrDefault(roomId, new CopyOnWriteArraySet<>()).size();

            if (user != null) {
                log.info("用户离开了直播间: userId={}, nickname={}, roomId={}, 当前房间人数={}",
                        user.getUid(), user.getNickname(), roomId, currentCount);
            } else {
                log.info("未认证用户断开连接: sessionId={}, roomId={}, 当前房间人数={}",
                        sessionId, roomId, currentCount);
            }

            // 广播在线人数更新
            broadcastOnlineCount(roomId);

        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 从session中获取房间ID
     */
    private String getRoomIdFromSession(WebSocketSession session) {
        URI uri = session.getUri();
        if (uri != null) {
            String path = uri.getPath();
            String[] segments = path.split("/");
            if (segments.length > 0) {
                return segments[segments.length - 1];
            }
        }
        return null;
    }

    /**
     * 处理认证消息
     */
    private void handleAuthMessage(WebSocketSession session, String roomId, LiveRoomMessageRequest messageRequest) {
        String sessionId = session.getId();
        String token = messageRequest.getContent();

        log.info("处理用户认证: sessionId={}, roomId={}", sessionId, roomId);

        // 验证token
        User user = webSocketAuthService.authenticateUser(token);
        if (user == null) {
            log.warn("用户认证失败: sessionId={}, roomId={}, token={}", sessionId, roomId, token);
            sendForceCloseMessage(session, "认证失败，请重新登录");
            return;
        }

        // 检查房间权限
        if (!webSocketAuthService.hasRoomPermission(user, roomId)) {
            log.warn("用户无权限进入直播间: userId={}, roomId={}", user.getUid(), roomId);
            sendForceCloseMessage(session, "无权限进入该直播间");
            return;
        }

        // 认证成功，加入直播间
        joinRoom(session, roomId, user);
    }

    /**
     * 用户加入直播间
     */
    private void joinRoom(WebSocketSession session, String roomId, User user) {
        String sessionId = session.getId();
        Integer userId = user.getUid();

        // 如果用户已有其他连接，先关闭旧连接
        WebSocketSession oldSession = userSessions.get(userId);
        if (oldSession != null && oldSession.isOpen()) {
            try {
                log.info("关闭用户旧连接: userId={}, oldSessionId={}", userId, oldSession.getId());
                // 发送踢出消息
                LiveRoomMessageResponse leaveMessage = systemMessageService.createTickoutMessage(roomId, user);
                sendMessageToSession(oldSession, leaveMessage);
                // 关闭连接
                oldSession.close(CloseStatus.NORMAL);
            } catch (Exception e) {
                log.error("关闭旧连接失败: {}", e.getMessage());
            }
        }

        // 建立新的映射关系
        roomSessions.computeIfAbsent(roomId, k -> new CopyOnWriteArraySet<>()).add(session);
        sessionRoomMap.put(sessionId, roomId);
        sessionUserMap.put(sessionId, user);
        userSessions.put(userId, session);

        log.info("用户成功加入直播间: userId={}, nickname={}, roomId={}, 当前房间人数={}",
                userId, user.getNickname(), roomId, roomSessions.get(roomId).size());

        // 发送认证成功消息
        LiveRoomMessageResponse authSuccessMessage = systemMessageService.createAuthSuccessMessage(roomId, user);
        sendMessageToSession(session, authSuccessMessage);

        // 广播用户进入消息
        LiveRoomMessageResponse welcomeMessage = systemMessageService.createWelcomeMessage(roomId, user);
         broadcastMessage(roomId, welcomeMessage);
//        messageProducer.sendMessage(welcomeMessage);

        // 广播在线人数更新
        broadcastOnlineCount(roomId);
    }

    /**
     * 处理用户消息
     */
    private void handleUserMessage(WebSocketSession session, String roomId, User user, LiveRoomMessageRequest messageRequest) {
        try {
            if (messageRequest.getMsgType() == LiveRoomMessageTypeEnum.REQ_TEXT_MSG.getCode()) {
                // 处理文本消息
                handleTextMessage(roomId, user, messageRequest);
            } else if (messageRequest.getMsgType() == LiveRoomMessageTypeEnum.REQ_HEART_BEAT.getCode()) {
                log.info("收到心跳消息: userId={}, roomId={}", user.getUid(), roomId);
            } else {
                log.warn("不支持的消息类型: userId={}, roomId={}, msgType={}",
                        user.getUid(), roomId, messageRequest.getMsgType());
            }
        } catch (Exception e) {
            log.error("处理用户消息失败: userId={}, roomId={}, error={}",
                    user.getUid(), roomId, e.getMessage(), e);
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(String roomId, User user, LiveRoomMessageRequest messageRequest) {
        // 设置发送者信息
        messageRequest.setRoomId(roomId);
        messageRequest.setUsername(user.getNickname());
        messageRequest.setAvatar(user.getAvatar());

        log.info("处理文本消息: userId={}, nickname={}, roomId={}, content={}",
                user.getUid(), user.getNickname(), roomId, messageRequest.getContent());

        // 保存消息到数据库
        LiveRoomMessageResponse messageResponse = new LiveRoomMessageResponse(); // liveRoomMsgService.saveMessage(messageRequest);

        // 设置消息类型为文本消息响应
        messageResponse.setMsgType(LiveRoomMessageTypeEnum.RES_TEXT_MSG.getCode());
        messageResponse.setUsername(user.getNickname());
        messageResponse.setAvatar(user.getAvatar());
        messageResponse.setContent(messageRequest.getContent());

        // 广播消息到房间内所有用户
        broadcastMessage(roomId, messageResponse);
    }

    /**
     * 移除session
     */
    private String removeSession(WebSocketSession session) {
        String sessionId = session.getId();
        String roomId = sessionRoomMap.remove(sessionId);
        User user = sessionUserMap.get(sessionId);

        if (roomId != null) {
            CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
            if (sessions != null) {
                sessions.remove(session);
                if (sessions.isEmpty()) {
                    roomSessions.remove(roomId);
                }
            }
        }

        if (user != null) {
            userSessions.remove(user.getUid());
        }

        return roomId;
    }

    /**
     * 发送强制关闭消息并关闭连接
     */
    private void sendForceCloseMessage(WebSocketSession session, String reason) {
        try {
            LiveRoomMessageResponse forceCloseMessage = systemMessageService.createForceCloseMessage(reason);
            sendMessageToSession(session, forceCloseMessage);

            // 延迟关闭连接，确保消息发送完成
            new Thread(() -> {
                try {
                    Thread.sleep(100);
                    if (session.isOpen()) {
                        session.close(CloseStatus.POLICY_VIOLATION);
                    }
                } catch (Exception e) {
                    log.error("关闭连接失败: {}", e.getMessage());
                }
            }).start();

        } catch (Exception e) {
            log.error("发送强制关闭消息失败: {}", e.getMessage());
            try {
                if (session.isOpen()) {
                    session.close(CloseStatus.SERVER_ERROR);
                }
            } catch (IOException ioException) {
                log.error("强制关闭连接失败: {}", ioException.getMessage());
            }
        }
    }

    /**
     * 发送消息到指定session
     */
    private void sendMessageToSession(WebSocketSession session, LiveRoomMessageResponse message) {
        try {
            if (session.isOpen()) {
                String messageJson = JSON.toJSONString(message);
                TextMessage textMessage = new TextMessage(messageJson);
                session.sendMessage(textMessage);

                log.debug("发送消息到session: sessionId={}, msgType={}, content={}",
                        session.getId(), message.getMsgType(), message.getContent());
            }
        } catch (IOException e) {
            log.error("发送消息到session失败: sessionId={}, error={}", session.getId(), e.getMessage());
        }
    }

    /**
     * 广播消息到房间内所有用户
     */
    public void broadcastMessage(String roomId, LiveRoomMessageResponse message) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        if (sessions == null || sessions.isEmpty()) {
            log.debug("房间无在线用户，跳过消息广播: roomId={}", roomId);
            return;
        }

        String messageJson = JSON.toJSONString(message);
        TextMessage textMessage = new TextMessage(messageJson);

        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(textMessage);
                    return false; // 保留session
                } else {
                    return true; // 移除无效session
                }
            } catch (IOException e) {
                log.warn("广播消息失败，移除无效session: sessionId={}, error={}", session.getId(), e.getMessage());
                return true; // 移除失败的session
            }
        });

        log.debug("消息广播完成: roomId={}, 在线用户数={}, msgType={}, content={}",
                roomId, sessions.size(), message.getMsgType(),
                message.getContent().length() > 50 ? message.getContent().substring(0, 50) + "..." : message.getContent());
    }

    /**
     * 广播在线人数更新
     */
    private void broadcastOnlineCount(String roomId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        int onlineCount = sessions != null ? sessions.size() : 0;

        // 创建在线人数更新消息
        LiveRoomMessageResponse countMessage = systemMessageService.createOnlineCountMessage(roomId, onlineCount);
        broadcastMessage(roomId, countMessage);

        log.debug("广播在线人数更新: roomId={}, onlineCount={}", roomId, onlineCount);
    }

    /**
     * 推送商品讲解消息（供外部调用）
     */
    public static void pushProductMessage(String roomId, String productName, Integer productId,
                                          String productImage, String productPrice) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        if (sessions == null || sessions.isEmpty()) {
            log.warn("推送商品消息失败，房间无在线用户: roomId={}", roomId);
            return;
        }

        // 创建商品讲解消息
        LiveRoomMessageResponse productMessage = new LiveRoomMessageResponse();
        productMessage.setRoomId(roomId);
        productMessage.setMsgType(LiveRoomMessageTypeEnum.RES_PROD_ON_SALE.getCode());

        // 构建商品信息JSON
        String productInfo = String.format("{\"productId\":%d,\"productName\":\"%s\",\"productImage\":\"%s\",\"productPrice\":\"%s\"}",
                productId, productName, productImage, productPrice);
        productMessage.setContent(productInfo);
        productMessage.setPublisher("SYSTEM");
        productMessage.setUsername("商品推荐");
        productMessage.setAvatar("");
        productMessage.setCreateTime(new Date());

        // 广播商品消息
        String messageJson = JSON.toJSONString(productMessage);
        TextMessage textMessage = new TextMessage(messageJson);

        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(textMessage);
                    return false;
                } else {
                    return true;
                }
            } catch (IOException e) {
                log.error("推送商品消息失败: sessionId={}, error={}", session.getId(), e.getMessage());
                return true;
            }
        });

        log.info("推送商品讲解消息: roomId={}, productId={}, productName={}, 接收用户数={}",
                roomId, productId, productName, sessions.size());
    }

    /**
     * 获取房间在线人数
     */
    public static int getRoomOnlineCount(String roomId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        return sessions != null ? sessions.size() : 0;
    }

    /**
     * 获取在线直播间数量
     */
    public static int getOnlineRoomCount() {
        return roomSessions.size();
    }

    /**
     * 获取总在线用户数
     */
    public static int getTotalOnlineCount() {
        return roomSessions.values().stream()
                .mapToInt(sessions -> sessions.size())
                .sum();
    }

    /**
     * 获取房间内的用户列表
     */
    public static List<User> getRoomUsers(String roomId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = roomSessions.get(roomId);
        if (sessions == null || sessions.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        return sessions.stream()
                .map(session -> sessionUserMap.get(session.getId()))
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

}
