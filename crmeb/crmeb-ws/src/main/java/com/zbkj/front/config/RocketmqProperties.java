package com.zbkj.front.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(
        prefix = "live.rocketmq"
)
@Data
public class RocketmqProperties {

    private String accessKey;
    private String secretKey;
    private String namespace;
    private String endpoints;

    private Producer producer;

    private Map<String, Consumer> consumers;


    @Data
    public static class Producer {
        private String topic;
    }

    @Data
    public static class Consumer {
        private String consumerGroup;
        private String topic;
    }
}
