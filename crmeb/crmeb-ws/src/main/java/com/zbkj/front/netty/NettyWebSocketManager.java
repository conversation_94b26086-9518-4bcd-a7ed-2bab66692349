//package com.zbkj.front.netty;
//
//import com.zbkj.common.model.user.User;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * Netty WebSocket管理器
// * 提供与原Spring WebSocket相同的API接口，保持兼容性
// * +----------------------------------------------------------------------
// * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// * +----------------------------------------------------------------------
// * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// * +----------------------------------------------------------------------
// * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// * +----------------------------------------------------------------------
// * | Author: CRMEB Team <<EMAIL>>
// * +----------------------------------------------------------------------
// */
//@Component
//public class NettyWebSocketManager {
//
//    /**
//     * 获取直播间在线人数
//     * @param roomId 直播间ID
//     * @return 在线人数
//     */
//    public int getRoomOnlineCount(String roomId) {
//        return NettyWebSocketHandler.getRoomOnlineCount(roomId);
//    }
//
//    /**
//     * 检查直播间是否有在线用户
//     * @param roomId 直播间ID
//     * @return 是否有在线用户
//     */
//    public boolean hasOnlineUsers(String roomId) {
//        return getRoomOnlineCount(roomId) > 0;
//    }
//
//    /**
//     * 获取所有在线直播间数量
//     * @return 在线直播间数量
//     */
//    public int getOnlineRoomCount() {
//        return NettyWebSocketHandler.getOnlineRoomCount();
//    }
//
//    /**
//     * 获取总在线用户数
//     * @return 总在线用户数
//     */
//    public int getTotalOnlineCount() {
//        return NettyWebSocketHandler.getTotalOnlineCount();
//    }
//
//    /**
//     * 获取房间内的用户列表
//     * @param roomId 直播间ID
//     * @return 用户列表
//     */
//    public List<User> getRoomUsers(String roomId) {
//        return NettyWebSocketHandler.getRoomUsers(roomId);
//    }
//
//    /**
//     * 推送商品讲解消息
//     * @param roomId 直播间ID
//     * @param productName 商品名称
//     * @param productId 商品ID
//     * @param productImage 商品图片
//     * @param productPrice 商品价格
//     */
//    public void pushProductMessage(String roomId, String productName, Integer productId,
//                                 String productImage, String productPrice) {
//        NettyWebSocketHandler.pushProductMessage(roomId, productName, productId, productImage, productPrice);
//    }
//}
