//package com.zbkj.front.netty;
//
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.channel.ChannelInboundHandlerAdapter;
//import io.netty.handler.codec.http.FullHttpRequest;
//import io.netty.handler.codec.http.HttpHeaders;
//import io.netty.util.AttributeKey;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.util.StringUtils;
//
//import java.net.URI;
//
///**
// * WebSocket路径处理器
// * 用于解析WebSocket连接的路径信息，提取房间ID
// * +----------------------------------------------------------------------
// * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// * +----------------------------------------------------------------------
// * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// * +----------------------------------------------------------------------
// * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// * +----------------------------------------------------------------------
// * | Author: CRMEB Team <<EMAIL>>
// * +----------------------------------------------------------------------
// */
//@Slf4j
//public class WebSocketPathHandler extends ChannelInboundHandlerAdapter {
//
//    private static final AttributeKey<String> ROOM_ID_ATTR = AttributeKey.valueOf("roomId");
//    private static final AttributeKey<String> URI_ATTR = AttributeKey.valueOf("uri");
//
//    @Override
//    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
//        if (msg instanceof FullHttpRequest) {
//            FullHttpRequest request = (FullHttpRequest) msg;
//
//            // 检查是否为WebSocket升级请求
//            HttpHeaders headers = request.headers();
//            String upgrade = headers.get("Upgrade");
//            String connection = headers.get("Connection");
//
//            if ("websocket".equalsIgnoreCase(upgrade) &&
//                connection != null && connection.toLowerCase().contains("upgrade")) {
//
//                // 解析URI并提取房间ID
//                String uri = request.uri();
//                String roomId = extractRoomIdFromUri(uri);
//
//                if (!StringUtils.isEmpty(roomId)) {
//                    // 将房间ID和URI存储到Channel属性中
//                    ctx.channel().attr(ROOM_ID_ATTR).set(roomId);
//                    ctx.channel().attr(URI_ATTR).set(uri);
//
//                    log.debug("WebSocket路径解析: uri={}, roomId={}", uri, roomId);
//                } else {
//                    log.warn("无法从URI中提取房间ID: uri={}", uri);
//                }
//            }
//        }
//
//        // 继续传递消息
//        super.channelRead(ctx, msg);
//    }
//
//    /**
//     * 从URI中提取房间ID
//     * 支持的URI格式：
//     * - /chat/{roomId}
//     * - /chat/{roomId}/
//     * - /chat/{roomId}?param=value
//     */
//    private String extractRoomIdFromUri(String uri) {
//        try {
//            if (StringUtils.isEmpty(uri)) {
//                return null;
//            }
//
//            // 移除查询参数
//            int queryIndex = uri.indexOf('?');
//            if (queryIndex > 0) {
//                uri = uri.substring(0, queryIndex);
//            }
//
//            // 移除末尾的斜杠
//            if (uri.endsWith("/")) {
//                uri = uri.substring(0, uri.length() - 1);
//            }
//
//            // 分割路径
//            String[] segments = uri.split("/");
//
//            // 期望格式: ["", "chat", "{roomId}"]
//            if (segments.length >= 3 && "chat".equals(segments[1])) {
//                String roomId = segments[2];
//
//                // 验证房间ID格式（可以根据需要调整验证规则）
//                if (!StringUtils.isEmpty(roomId) && roomId.matches("^[a-zA-Z0-9_-]+$")) {
//                    return roomId;
//                }
//            }
//
//            return null;
//        } catch (Exception e) {
//            log.error("解析URI失败: uri={}, error={}", uri, e.getMessage());
//            return null;
//        }
//    }
//
//    /**
//     * 获取Channel中存储的房间ID
//     */
//    public static String getRoomId(ChannelHandlerContext ctx) {
//        return ctx.channel().attr(ROOM_ID_ATTR).get();
//    }
//
//    /**
//     * 获取Channel中存储的URI
//     */
//    public static String getUri(ChannelHandlerContext ctx) {
//        return ctx.channel().attr(URI_ATTR).get();
//    }
//}
