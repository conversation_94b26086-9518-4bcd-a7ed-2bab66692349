//package com.zbkj.front.netty;
//
//import com.alibaba.fastjson.JSON;
//import com.zbkj.common.enums.LiveRoomMessageTypeEnum;
//import com.zbkj.common.model.user.User;
//import com.zbkj.common.request.LiveRoomMessageRequest;
//import com.zbkj.common.response.LiveRoomMessageResponse;
//import com.zbkj.front.ws.SystemMessageService;
//import com.zbkj.front.ws.WebSocketAuthService;
//import com.zbkj.service.service.LiveRoomMsgService;
//import io.netty.channel.Channel;
//import io.netty.channel.ChannelHandler;
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.channel.SimpleChannelInboundHandler;
//import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
//import io.netty.handler.codec.http.websocketx.WebSocketFrame;
//import io.netty.util.AttributeKey;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.net.URI;
//import java.util.Date;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.CopyOnWriteArraySet;
//
///**
// * Netty WebSocket处理器
// * +----------------------------------------------------------------------
// * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// * +----------------------------------------------------------------------
// * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// * +----------------------------------------------------------------------
// * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// * +----------------------------------------------------------------------
// * | Author: CRMEB Team <<EMAIL>>
// * +----------------------------------------------------------------------
// */
//@Slf4j
//@Component
//@ChannelHandler.Sharable
//public class NettyWebSocketHandler extends SimpleChannelInboundHandler<WebSocketFrame> {
//
//    @Autowired
//    private LiveRoomMsgService liveRoomMsgService;
//
//    @Autowired
//    private WebSocketAuthService webSocketAuthService;
//
//    @Autowired
//    private SystemMessageService systemMessageService;
//
//    // Channel属性键
//    private static final AttributeKey<String> ROOM_ID_KEY = AttributeKey.valueOf("roomId");
//    private static final AttributeKey<User> USER_KEY = AttributeKey.valueOf("user");
//    private static final AttributeKey<Boolean> AUTHENTICATED_KEY = AttributeKey.valueOf("authenticated");
//    private static final AttributeKey<Long> CONNECT_TIME_KEY = AttributeKey.valueOf("connectTime");
//
//    /**
//     * 存储每个用户的连接
//     * key: userId, value: Channel对象
//     */
//    private static final ConcurrentHashMap<Integer, Channel> userChannels = new ConcurrentHashMap<>();
//
//    /**
//     * 存储每个直播间的连接
//     * key: roomId, value: Channel集合
//     */
//    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<Channel>> roomChannels = new ConcurrentHashMap<>();
//
//    /**
//     * 存储Channel与房间的映射关系
//     * key: channelId, value: roomId
//     */
//    private static final ConcurrentHashMap<String, String> channelRoomMap = new ConcurrentHashMap<>();
//
//    /**
//     * 存储每个直播间最近的20条消息
//     * key: roomId, value: 消息列表
//     */
//    private static final ConcurrentHashMap<String, LinkedList<LiveRoomMessageResponse>> roomMessageCache = new ConcurrentHashMap<>();
//
//    /**
//     * 存储未认证的Channel
//     * key: channelId, value: 连接时间戳
//     */
//    private static final ConcurrentHashMap<String, Long> unauthenticatedChannels = new ConcurrentHashMap<>();
//
//    // 最大缓存消息数量
//    private static final int MAX_CACHE_MESSAGES = 20;
//
//    // 未认证连接的超时时间（毫秒）
//    private static final long AUTH_TIMEOUT = 30000; // 30秒
//
//    @Override
//    public void channelActive(ChannelHandlerContext ctx) throws Exception {
//        Channel channel = ctx.channel();
//        String channelId = channel.id().asShortText();
//
//        // 从URI中提取房间ID
//        String roomId = extractRoomIdFromUri(channel);
//        if (StringUtils.isEmpty(roomId)) {
//            log.warn("WebSocket连接建立失败: 无法获取房间ID, channelId={}", channelId);
//            channel.close();
//            return;
//        }
//
//        // 设置Channel属性
//        channel.attr(ROOM_ID_KEY).set(roomId);
//        channel.attr(AUTHENTICATED_KEY).set(false);
//        channel.attr(CONNECT_TIME_KEY).set(System.currentTimeMillis());
//
//        // 记录未认证的连接
//        unauthenticatedChannels.put(channelId, System.currentTimeMillis());
//
//        log.info("WebSocket连接建立: channelId={}, roomId={}, 等待用户认证", channelId, roomId);
//
//        // 发送认证要求消息
//        LiveRoomMessageResponse authRequiredMessage = new LiveRoomMessageResponse();
//        authRequiredMessage.setMsgType(LiveRoomMessageTypeEnum.RES_SYSTEM_MSG.getCode());
//        authRequiredMessage.setContent("AUTH_REQUIRED");
//        authRequiredMessage.setPublisher("SYSTEM");
//        sendMessageToChannel(channel, authRequiredMessage);
//
//        super.channelActive(ctx);
//    }
//
//    @Override
//    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
//        if (frame instanceof TextWebSocketFrame) {
//            handleTextFrame(ctx, (TextWebSocketFrame) frame);
//        }
//    }
//
//    @Override
//    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
//        Channel channel = ctx.channel();
//        String channelId = channel.id().asShortText();
//        String roomId = removeChannel(channel);
//        User user = channel.attr(USER_KEY).get();
//
//        if (roomId != null) {
//            int currentCount = roomChannels.getOrDefault(roomId, new CopyOnWriteArraySet<>()).size();
//
//            if (user != null) {
//                log.info("用户断开直播间连接: userId={}, nickname={}, roomId={}, 当前房间人数={}",
//                        user.getUid(), user.getNickname(), roomId, currentCount);
//
//                // 广播用户离开消息
//                LiveRoomMessageResponse leaveMessage = systemMessageService.createLeaveMessage(roomId, user);
//                broadcastMessage(roomId, leaveMessage);
//            } else {
//                log.info("未认证用户断开连接: channelId={}, roomId={}, 当前房间人数={}",
//                        channelId, roomId, currentCount);
//            }
//
//            // 广播在线人数更新
//            broadcastOnlineCount(roomId);
//
//            // 如果房间无人，清理缓存
//            cleanupRoomCache(roomId);
//        }
//
//        // 清理未认证连接记录
//        unauthenticatedChannels.remove(channelId);
//
//        super.channelInactive(ctx);
//    }
//
//    @Override
//    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
//        log.error("WebSocket连接异常: channelId={}, error={}", ctx.channel().id().asShortText(), cause.getMessage(), cause);
//        ctx.close();
//    }
//
//    /**
//     * 处理文本消息
//     */
//    private void handleTextFrame(ChannelHandlerContext ctx, TextWebSocketFrame frame) {
//        Channel channel = ctx.channel();
//        String channelId = channel.id().asShortText();
//        String text = frame.text();
//
//        log.debug("收到WebSocket消息: channelId={}, text={}", channelId, text);
//
//        // 解析消息
//        LiveRoomMessageRequest messageRequest;
//        try {
//            messageRequest = JSON.parseObject(text, LiveRoomMessageRequest.class);
//        } catch (Exception e) {
//            log.error("解析WebSocket消息失败: channelId={}, text={}, error={}", channelId, text, e.getMessage());
//            sendForceCloseMessage(channel, "消息格式错误");
//            return;
//        }
//
//        String roomId = channel.attr(ROOM_ID_KEY).get();
//        if (StringUtils.isEmpty(roomId)) {
//            log.error("无法获取房间ID: channelId={}", channelId);
//            sendForceCloseMessage(channel, "房间ID无效");
//            return;
//        }
//
//        // 检查是否为认证消息
//        if (messageRequest.getMsgType() == LiveRoomMessageTypeEnum.REQ_AUTH.getCode()) {
//            handleAuthMessage(channel, roomId, messageRequest);
//            return;
//        }
//
//        // 检查用户是否已认证
//        Boolean authenticated = channel.attr(AUTHENTICATED_KEY).get();
//        User user = channel.attr(USER_KEY).get();
//        if (!Boolean.TRUE.equals(authenticated) || user == null) {
//            log.warn("未认证用户尝试发送消息: channelId={}, roomId={}", channelId, roomId);
//            sendForceCloseMessage(channel, "请先进行身份认证");
//            return;
//        }
//
//        // 处理已认证用户的消息
//        handleUserMessage(channel, roomId, user, messageRequest);
//    }
//
//    /**
//     * 从URI中提取房间ID
//     */
//    private String extractRoomIdFromUri(Channel channel) {
//        try {
//            // 从Channel属性中获取房间ID（由WebSocketPathHandler设置）
//            String roomId = channel.attr(AttributeKey.<String>valueOf("roomId")).get();
//
//            if (!StringUtils.isEmpty(roomId)) {
//                return roomId;
//            }
//
//            // 如果没有找到，尝试从URI属性中解析
//            String uri = channel.attr(AttributeKey.<String>valueOf("uri")).get();
//            if (!StringUtils.isEmpty(uri)) {
//                return parseRoomIdFromUri(uri);
//            }
//
//            log.warn("无法获取房间ID，使用默认房间");
//            return "room_001"; // 默认房间
//        } catch (Exception e) {
//            log.error("提取房间ID失败", e);
//            return "room_001";
//        }
//    }
//
//    /**
//     * 从URI字符串中解析房间ID
//     */
//    private String parseRoomIdFromUri(String uri) {
//        try {
//            // 移除查询参数
//            int queryIndex = uri.indexOf('?');
//            if (queryIndex > 0) {
//                uri = uri.substring(0, queryIndex);
//            }
//
//            // 移除末尾的斜杠
//            if (uri.endsWith("/")) {
//                uri = uri.substring(0, uri.length() - 1);
//            }
//
//            // 分割路径
//            String[] segments = uri.split("/");
//
//            // 期望格式: ["", "chat", "{roomId}"]
//            if (segments.length >= 3 && "chat".equals(segments[1])) {
//                return segments[2];
//            }
//
//            return null;
//        } catch (Exception e) {
//            log.error("解析URI失败: uri={}", uri, e);
//            return null;
//        }
//    }
//
//    /**
//     * 处理认证消息
//     */
//    private void handleAuthMessage(Channel channel, String roomId, LiveRoomMessageRequest messageRequest) {
//        String channelId = channel.id().asShortText();
//        String token = messageRequest.getContent();
//
//        log.info("处理用户认证: channelId={}, roomId={}", channelId, roomId);
//
//        // 验证token
//        User user = webSocketAuthService.authenticateUser(token);
//        if (user == null) {
//            log.warn("用户认证失败: channelId={}, roomId={}, token={}", channelId, roomId, token);
//            sendForceCloseMessage(channel, "认证失败，请重新登录");
//            return;
//        }
//
//        // 检查房间权限
//        if (!webSocketAuthService.hasRoomPermission(user, roomId)) {
//            log.warn("用户无权限进入直播间: userId={}, roomId={}", user.getUid(), roomId);
//            sendForceCloseMessage(channel, "无权限进入该直播间");
//            return;
//        }
//
//        // 认证成功，加入直播间
//        joinRoom(channel, roomId, user);
//    }
//
//    /**
//     * 用户加入直播间
//     */
//    private void joinRoom(Channel channel, String roomId, User user) {
//        String channelId = channel.id().asShortText();
//        Integer userId = user.getUid();
//
//        // 移除未认证记录
//        unauthenticatedChannels.remove(channelId);
//
//        // 如果用户已有其他连接，先关闭旧连接
//        Channel oldChannel = userChannels.get(userId);
//        if (oldChannel != null && oldChannel.isActive()) {
//            log.info("关闭用户旧连接: userId={}, oldChannelId={}", userId, oldChannel.id().asShortText());
//            oldChannel.close();
//        }
//
//        // 建立新的映射关系
//        roomChannels.computeIfAbsent(roomId, k -> new CopyOnWriteArraySet<>()).add(channel);
//        channelRoomMap.put(channelId, roomId);
//        channel.attr(USER_KEY).set(user);
//        channel.attr(AUTHENTICATED_KEY).set(true);
//        userChannels.put(userId, channel);
//
//        log.info("用户成功加入直播间: userId={}, nickname={}, roomId={}, 当前房间人数={}",
//                userId, user.getNickname(), roomId, roomChannels.get(roomId).size());
//
//        // 发送认证成功消息
//        LiveRoomMessageResponse authSuccessMessage = systemMessageService.createAuthSuccessMessage(roomId, user);
//        sendMessageToChannel(channel, authSuccessMessage);
//
//        // 发送个人欢迎消息
//        sendWelcomeMessage(channel, roomId, user);
//
//        // 发送历史消息
//        sendHistoryMessages(channel, roomId);
//
//        // 广播用户进入消息
//        LiveRoomMessageResponse welcomeMessage = systemMessageService.createWelcomeMessage(roomId, user);
//        broadcastMessage(roomId, welcomeMessage);
//
//        // 广播在线人数更新
//        broadcastOnlineCount(roomId);
//    }
//
//    /**
//     * 处理用户消息
//     */
//    private void handleUserMessage(Channel channel, String roomId, User user, LiveRoomMessageRequest messageRequest) {
//        try {
//            if (messageRequest.getMsgType() == LiveRoomMessageTypeEnum.REQ_TEXT_MSG.getCode()) {
//                // 处理文本消息
//                handleTextMessage(roomId, user, messageRequest);
//            } else {
//                log.warn("不支持的消息类型: userId={}, roomId={}, msgType={}",
//                        user.getUid(), roomId, messageRequest.getMsgType());
//            }
//        } catch (Exception e) {
//            log.error("处理用户消息失败: userId={}, roomId={}, error={}",
//                    user.getUid(), roomId, e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 处理文本消息
//     */
//    private void handleTextMessage(String roomId, User user, LiveRoomMessageRequest messageRequest) {
//        // 设置发送者信息
//        messageRequest.setRoomId(roomId);
//        messageRequest.setUsername(user.getNickname());
//        messageRequest.setAvatar(user.getAvatar());
//
//        log.info("处理文本消息: userId={}, nickname={}, roomId={}, content={}",
//                user.getUid(), user.getNickname(), roomId, messageRequest.getContent());
//
//        // 保存消息到数据库
//        LiveRoomMessageResponse messageResponse = liveRoomMsgService.saveMessage(messageRequest);
//
//        // 设置消息类型为文本消息响应
//        messageResponse.setMsgType(LiveRoomMessageTypeEnum.RES_TEXT_MSG.getCode());
//        messageResponse.setUsername(user.getNickname());
//        messageResponse.setAvatar(user.getAvatar());
//        messageResponse.setIsNew(true);
//
//        // 添加消息到缓存
//        addMessageToCache(roomId, messageResponse);
//
//        // 广播消息到房间内所有用户
//        broadcastMessage(roomId, messageResponse);
//    }
//
//    /**
//     * 发送强制关闭消息并关闭连接
//     */
//    private void sendForceCloseMessage(Channel channel, String reason) {
//        try {
//            LiveRoomMessageResponse forceCloseMessage = systemMessageService.createForceCloseMessage(reason);
//            sendMessageToChannel(channel, forceCloseMessage);
//
//            // 延迟关闭连接，确保消息发送完成
//            channel.eventLoop().schedule(() -> {
//                if (channel.isActive()) {
//                    channel.close();
//                }
//            }, 100, java.util.concurrent.TimeUnit.MILLISECONDS);
//
//        } catch (Exception e) {
//            log.error("发送强制关闭消息失败: {}", e.getMessage());
//            if (channel.isActive()) {
//                channel.close();
//            }
//        }
//    }
//
//    /**
//     * 发送消息到指定Channel
//     */
//    private void sendMessageToChannel(Channel channel, LiveRoomMessageResponse message) {
//        try {
//            if (channel.isActive()) {
//                String messageJson = JSON.toJSONString(message);
//                TextWebSocketFrame frame = new TextWebSocketFrame(messageJson);
//                channel.writeAndFlush(frame);
//
//                log.debug("发送消息到Channel: channelId={}, msgType={}, content={}",
//                        channel.id().asShortText(), message.getMsgType(), message.getContent());
//            }
//        } catch (Exception e) {
//            log.error("发送消息到Channel失败: channelId={}, error={}", channel.id().asShortText(), e.getMessage());
//        }
//    }
//
//    /**
//     * 广播消息到房间内所有用户
//     */
//    private void broadcastMessage(String roomId, LiveRoomMessageResponse message) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        if (channels == null || channels.isEmpty()) {
//            log.debug("房间无在线用户，跳过消息广播: roomId={}", roomId);
//            return;
//        }
//
//        String messageJson = JSON.toJSONString(message);
//        TextWebSocketFrame frame = new TextWebSocketFrame(messageJson);
//
//        channels.removeIf(channel -> {
//            try {
//                if (channel.isActive()) {
//                    channel.writeAndFlush(frame.retain());
//                    return false; // 保留channel
//                } else {
//                    return true; // 移除无效channel
//                }
//            } catch (Exception e) {
//                log.warn("广播消息失败，移除无效channel: channelId={}, error={}", channel.id().asShortText(), e.getMessage());
//                return true; // 移除失败的channel
//            }
//        });
//
//        frame.release(); // 释放原始frame
//
//        log.debug("消息广播完成: roomId={}, 在线用户数={}, msgType={}, content={}",
//                roomId, channels.size(), message.getMsgType(),
//                message.getContent().length() > 50 ? message.getContent().substring(0, 50) + "..." : message.getContent());
//    }
//
//    /**
//     * 广播在线人数更新
//     */
//    private void broadcastOnlineCount(String roomId) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        int onlineCount = channels != null ? channels.size() : 0;
//
//        // 创建在线人数更新消息
//        LiveRoomMessageResponse countMessage = systemMessageService.createOnlineCountMessage(roomId, onlineCount);
//        broadcastMessage(roomId, countMessage);
//
//        log.debug("广播在线人数更新: roomId={}, onlineCount={}", roomId, onlineCount);
//    }
//
//    /**
//     * 发送个人欢迎消息给新用户
//     */
//    private void sendWelcomeMessage(Channel channel, String roomId, User user) {
//        try {
//            LiveRoomMessageResponse welcomeMessage = new LiveRoomMessageResponse();
//            welcomeMessage.setRoomId(roomId);
//            welcomeMessage.setMsgType(LiveRoomMessageTypeEnum.RES_SYSTEM_MSG.getCode());
//            welcomeMessage.setContent(String.format("欢迎 %s 进入直播间！开始愉快的观看体验吧~", user.getNickname()));
//            welcomeMessage.setPublisher("SYSTEM");
//            welcomeMessage.setUsername("系统消息");
//            welcomeMessage.setAvatar("");
//            welcomeMessage.setCreateTime(new Date());
//            welcomeMessage.setIsNew(true);
//
//            sendMessageToChannel(channel, welcomeMessage);
//            log.info("发送个人欢迎消息: userId={}, nickname={}, roomId={}", user.getUid(), user.getNickname(), roomId);
//        } catch (Exception e) {
//            log.error("发送个人欢迎消息失败: userId={}, error={}", user.getUid(), e.getMessage());
//        }
//    }
//
//    /**
//     * 发送历史消息给新用户
//     */
//    private void sendHistoryMessages(Channel channel, String roomId) {
//        try {
//            // 先尝试从缓存获取
//            LinkedList<LiveRoomMessageResponse> cachedMessages = roomMessageCache.get(roomId);
//
//            if (cachedMessages == null || cachedMessages.isEmpty()) {
//                // 缓存为空，从数据库加载最近20条消息
//                loadMessagesFromDatabase(roomId);
//                cachedMessages = roomMessageCache.get(roomId);
//            }
//
//            if (cachedMessages != null && !cachedMessages.isEmpty()) {
//                int sentCount = 0;
//                // 发送历史消息
//                for (LiveRoomMessageResponse message : cachedMessages) {
//                    if (!channel.isActive()) {
//                        break;
//                    }
//
//                    // 创建历史消息副本，避免修改缓存中的原始消息
//                    LiveRoomMessageResponse historyMessage = new LiveRoomMessageResponse();
//                    historyMessage.setId(message.getId());
//                    historyMessage.setRoomId(message.getRoomId());
//                    historyMessage.setPublisher(message.getPublisher());
//                    historyMessage.setMsgType(message.getMsgType());
//                    historyMessage.setContent(message.getContent());
//                    historyMessage.setUsername(message.getUsername());
//                    historyMessage.setAvatar(message.getAvatar());
//                    historyMessage.setCreateTime(message.getCreateTime());
//                    historyMessage.setIsNew(false); // 历史消息标记为非新消息
//
//                    sendMessageToChannel(channel, historyMessage);
//                    sentCount++;
//
//                    // 添加小延迟，避免消息发送过快
//                    Thread.sleep(10);
//                }
//                log.info("发送历史消息完成: roomId={}, 发送数量={}", roomId, sentCount);
//            } else {
//                log.info("无历史消息可发送: roomId={}", roomId);
//            }
//        } catch (Exception e) {
//            log.error("发送历史消息失败: roomId={}, error={}", roomId, e.getMessage());
//        }
//    }
//
//    /**
//     * 从数据库加载最近的消息到缓存
//     */
//    private void loadMessagesFromDatabase(String roomId) {
//        try {
//            List<LiveRoomMessageResponse> recentMessages = liveRoomMsgService.getRecentMessages(roomId, MAX_CACHE_MESSAGES);
//
//            LinkedList<LiveRoomMessageResponse> messageList = new LinkedList<>();
//            // 按时间顺序添加消息（最早的在前面）
//            for (LiveRoomMessageResponse message : recentMessages) {
//                messageList.addFirst(message);
//            }
//
//            roomMessageCache.put(roomId, messageList);
//            log.info("从数据库加载{}条消息到缓存，直播间: {}", messageList.size(), roomId);
//        } catch (Exception e) {
//            log.error("从数据库加载消息失败: {}", e.getMessage());
//        }
//    }
//
//    /**
//     * 添加消息到缓存
//     */
//    private void addMessageToCache(String roomId, LiveRoomMessageResponse message) {
//        LinkedList<LiveRoomMessageResponse> messageList = roomMessageCache.computeIfAbsent(roomId, k -> new LinkedList<>());
//
//        // 添加新消息到末尾
//        messageList.addLast(message);
//
//        // 如果超过最大缓存数量，移除最早的消息
//        while (messageList.size() > MAX_CACHE_MESSAGES) {
//            messageList.removeFirst();
//        }
//
//        log.debug("添加消息到缓存，直播间: {}, 当前缓存消息数: {}", roomId, messageList.size());
//    }
//
//    /**
//     * 移除Channel
//     */
//    private String removeChannel(Channel channel) {
//        String channelId = channel.id().asShortText();
//        String roomId = channelRoomMap.remove(channelId);
//        User user = channel.attr(USER_KEY).get();
//
//        if (roomId != null) {
//            CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//            if (channels != null) {
//                channels.remove(channel);
//                if (channels.isEmpty()) {
//                    roomChannels.remove(roomId);
//                }
//            }
//        }
//
//        if (user != null) {
//            userChannels.remove(user.getUid());
//        }
//
//        return roomId;
//    }
//
//    /**
//     * 清理房间缓存（当房间无人时调用）
//     */
//    private void cleanupRoomCache(String roomId) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        if (channels == null || channels.isEmpty()) {
//            roomMessageCache.remove(roomId);
//            log.info("清理直播间缓存: {}", roomId);
//        }
//    }
//
//    /**
//     * 推送商品讲解消息（供外部调用）
//     */
//    public static void pushProductMessage(String roomId, String productName, Integer productId,
//                                        String productImage, String productPrice) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        if (channels == null || channels.isEmpty()) {
//            log.warn("推送商品消息失败，房间无在线用户: roomId={}", roomId);
//            return;
//        }
//
//        // 创建商品讲解消息
//        LiveRoomMessageResponse productMessage = new LiveRoomMessageResponse();
//        productMessage.setRoomId(roomId);
//        productMessage.setMsgType(LiveRoomMessageTypeEnum.RES_PROD_ON_SALE.getCode());
//
//        // 构建商品信息JSON
//        String productInfo = String.format("{\"productId\":%d,\"productName\":\"%s\",\"productImage\":\"%s\",\"productPrice\":\"%s\"}",
//                                         productId, productName, productImage, productPrice);
//        productMessage.setContent(productInfo);
//        productMessage.setPublisher("SYSTEM");
//        productMessage.setUsername("商品推荐");
//        productMessage.setAvatar("");
//        productMessage.setCreateTime(new Date());
//        productMessage.setIsNew(true);
//
//        // 广播商品消息
//        String messageJson = JSON.toJSONString(productMessage);
//        TextWebSocketFrame frame = new TextWebSocketFrame(messageJson);
//
//        channels.removeIf(channel -> {
//            try {
//                if (channel.isActive()) {
//                    channel.writeAndFlush(frame.retain());
//                    return false;
//                } else {
//                    return true;
//                }
//            } catch (Exception e) {
//                log.error("推送商品消息失败: channelId={}, error={}", channel.id().asShortText(), e.getMessage());
//                return true;
//            }
//        });
//
//        frame.release();
//
//        log.info("推送商品讲解消息: roomId={}, productId={}, productName={}, 接收用户数={}",
//                roomId, productId, productName, channels.size());
//    }
//
//    /**
//     * 获取房间在线人数
//     */
//    public static int getRoomOnlineCount(String roomId) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        return channels != null ? channels.size() : 0;
//    }
//
//    /**
//     * 获取在线直播间数量
//     */
//    public static int getOnlineRoomCount() {
//        return roomChannels.size();
//    }
//
//    /**
//     * 获取总在线用户数
//     */
//    public static int getTotalOnlineCount() {
//        return roomChannels.values().stream()
//                .mapToInt(channels -> channels.size())
//                .sum();
//    }
//
//    /**
//     * 获取房间内的用户列表
//     */
//    public static List<User> getRoomUsers(String roomId) {
//        CopyOnWriteArraySet<Channel> channels = roomChannels.get(roomId);
//        if (channels == null || channels.isEmpty()) {
//            return new java.util.ArrayList<>();
//        }
//
//        return channels.stream()
//                .map(channel -> channel.attr(USER_KEY).get())
//                .filter(user -> user != null)
//                .collect(java.util.stream.Collectors.toList());
//    }
