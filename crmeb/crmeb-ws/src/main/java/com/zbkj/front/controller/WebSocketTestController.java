package com.zbkj.front.controller;

import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.front.mq.MessageProducer;
import com.zbkj.front.netty.NettyWebSocketManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket测试控制器
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Api(tags = "WebSocket测试接口")
@RestController
@RequestMapping("/api/test")
public class WebSocketTestController {

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private NettyWebSocketManager webSocketManager;

    @ApiOperation("发送测试消息到房间")
    @PostMapping("/sendToRoom")
    public Map<String, Object> sendToRoom(
            @ApiParam(value = "房间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "消息内容", required = true) @RequestParam String content) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LiveRoomMessageResponse message = new LiveRoomMessageResponse();
            message.setRoomId(roomId);
            message.setMsgType(3); // 文本消息
            message.setContent(content);
            message.setPublisher("TEST");
            message.setUsername("测试用户");
            message.setAvatar("");
            message.setCreateTime(new Date());
            message.setIsNew(true);

            messageProducer.sendToRoom(roomId, message);
            
            result.put("success", true);
            result.put("message", "消息发送成功");
            result.put("roomId", roomId);
            result.put("content", content);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
        }
        
        return result;
    }

    @ApiOperation("发送测试消息到指定用户")
    @PostMapping("/sendToUser")
    public Map<String, Object> sendToUser(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "消息内容", required = true) @RequestParam String content) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LiveRoomMessageResponse message = new LiveRoomMessageResponse();
            message.setMsgType(3); // 文本消息
            message.setContent(content);
            message.setPublisher("TEST");
            message.setUsername("测试用户");
            message.setAvatar("");
            message.setCreateTime(new Date());
            message.setIsNew(true);

            messageProducer.sendToUser(userId, message);
            
            result.put("success", true);
            result.put("message", "消息发送成功");
            result.put("userId", userId);
            result.put("content", content);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
        }
        
        return result;
    }

    @ApiOperation("发送商品推荐消息")
    @PostMapping("/sendProduct")
    public Map<String, Object> sendProduct(
            @ApiParam(value = "房间ID", required = true) @RequestParam String roomId,
            @ApiParam(value = "商品名称", required = true) @RequestParam String productName,
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId,
            @ApiParam(value = "商品图片") @RequestParam(defaultValue = "") String productImage,
            @ApiParam(value = "商品价格") @RequestParam(defaultValue = "0.00") String productPrice) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LiveRoomMessageResponse message = new LiveRoomMessageResponse();
            message.setRoomId(roomId);
            message.setMsgType(4); // 商品推荐
            
            // 构建商品信息JSON
            String productInfo = String.format("{\"productId\":%d,\"productName\":\"%s\",\"productImage\":\"%s\",\"productPrice\":\"%s\"}", 
                                             productId, productName, productImage, productPrice);
            message.setContent(productInfo);
            message.setPublisher("SYSTEM");
            message.setUsername("商品推荐");
            message.setAvatar("");
            message.setCreateTime(new Date());
            message.setIsNew(true);

            messageProducer.sendProductMessage(roomId, message);
            
            result.put("success", true);
            result.put("message", "商品推荐发送成功");
            result.put("roomId", roomId);
            result.put("productName", productName);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "商品推荐发送失败: " + e.getMessage());
        }
        
        return result;
    }

    @ApiOperation("获取房间统计信息")
    @GetMapping("/roomStats/{roomId}")
    public Map<String, Object> getRoomStats(
            @ApiParam(value = "房间ID", required = true) @PathVariable String roomId) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("roomId", roomId);
        result.put("onlineCount", webSocketManager.getRoomOnlineCount(roomId));
        result.put("hasOnlineUsers", webSocketManager.hasOnlineUsers(roomId));
        result.put("users", webSocketManager.getRoomUsers(roomId));
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    @ApiOperation("获取全局统计信息")
    @GetMapping("/globalStats")
    public Map<String, Object> getGlobalStats() {
        Map<String, Object> result = new HashMap<>();
        result.put("totalOnlineCount", webSocketManager.getTotalOnlineCount());
        result.put("onlineRoomCount", webSocketManager.getOnlineRoomCount());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    @ApiOperation("发送在线人数更新")
    @PostMapping("/updateOnlineCount")
    public Map<String, Object> updateOnlineCount(
            @ApiParam(value = "房间ID", required = true) @RequestParam String roomId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            int onlineCount = webSocketManager.getRoomOnlineCount(roomId);
            messageProducer.sendOnlineCountUpdate(roomId, onlineCount);
            
            result.put("success", true);
            result.put("message", "在线人数更新发送成功");
            result.put("roomId", roomId);
            result.put("onlineCount", onlineCount);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "在线人数更新发送失败: " + e.getMessage());
        }
        
        return result;
    }
}
