package com.zbkj.front.ws;

import org.springframework.stereotype.Component;

/**
 * WebSocket管理器
 * 提供WebSocket相关功能的统一接口
 */
@Component
public class WebSocketManager {

    /**
     * 获取直播间在线人数
     * @param roomId 直播间ID
     * @return 在线人数
     */
    public int getRoomOnlineCount(String roomId) {
        return LiveRoomWebSocketHandler.getRoomOnlineCount(roomId);
    }

    /**
     * 检查直播间是否有在线用户
     * @param roomId 直播间ID
     * @return 是否有在线用户
     */
    public boolean hasOnlineUsers(String roomId) {
        return getRoomOnlineCount(roomId) > 0;
    }

    /**
     * 获取所有在线直播间数量
     * @return 在线直播间数量
     */
    public int getOnlineRoomCount() {
        return LiveRoomWebSocketHandler.getOnlineRoomCount();
    }

    /**
     * 获取总在线用户数
     * @return 总在线用户数
     */
    public int getTotalOnlineCount() {
        return LiveRoomWebSocketHandler.getTotalOnlineCount();
    }
}
