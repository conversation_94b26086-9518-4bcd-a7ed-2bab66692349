package com.zbkj.front.mq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.SessionCredentialsProvider;
import org.apache.rocketmq.client.apis.StaticSessionCredentialsProvider;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.autoconfigure.RocketMQProperties;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 消息生产者
 */
@Slf4j
@Service
public class MessageConsumer  implements MessageListener {

    @Resource
    private RocketMQClientTemplate clientTemplate;
    @Resource
    private ClientServiceProvider clientServiceProvider;



    private void init(RocketMQProperties rocketMQProperties) {
        SessionCredentialsProvider sessionCredentialsProvider =
                new StaticSessionCredentialsProvider(accessKey, secretKey);

        PushConsumer pushConsumer

    }

    /**
     * 发送消息到RocketMQ
     */
    private void sendMessage(Object msg, String topic, String tag, String... keys) {
        try {
            Message message = clientServiceProvider.newMessageBuilder()
                    .setTopic(topic)
                    .setBody(JSON.toJSONBytes(msg))
                    .setTag(tag)
                    .setKeys(keys)
                    .build();

            SendReceipt sendReceipt = clientTemplate.getProducer().send(message);
            log.info("发送消息到MQ成功: messageId={}", sendReceipt.getMessageId());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {

        return null;
    }
}
