package com.zbkj.front.config;

import com.zbkj.front.ws.LiveRoomWebSocketHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册直播间WebSocket处理器
        registry.addHandler(liveRoomWebSocketHandler(), "/chat/{roomId}")
                .setAllowedOrigins("*"); // 允许跨域
    }

    @Bean
    public LiveRoomWebSocketHandler liveRoomWebSocketHandler() {
        return new LiveRoomWebSocketHandler();
    }
}
