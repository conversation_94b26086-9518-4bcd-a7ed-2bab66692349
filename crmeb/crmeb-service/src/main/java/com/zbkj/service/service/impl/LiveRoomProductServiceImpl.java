package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.model.live.LiveRoomProduct;
import com.zbkj.common.model.product.StoreProduct;
import com.zbkj.service.dao.LiveRoomProductDao;
import com.zbkj.service.service.LiveRoomProductService;
import com.zbkj.service.service.StoreProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 直播间商品服务实现类
 */
@Slf4j
@Service
public class LiveRoomProductServiceImpl extends ServiceImpl<LiveRoomProductDao, LiveRoomProduct> implements LiveRoomProductService {

    @Autowired
    private StoreProductService storeProductService;

    @Override
    public List<LiveRoomProduct> getByRoomId(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return null;
        }
        return baseMapper.selectByRoomIdOrderBySort(roomId);
    }

    @Override
    public List<LiveRoomProduct> getRecommendByRoomId(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return null;
        }
        return baseMapper.selectRecommendByRoomId(roomId);
    }

    @Override
    public List<LiveRoomProduct> getHotByRoomId(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return null;
        }
        return baseMapper.selectHotByRoomId(roomId);
    }

    @Override
    public List<LiveRoomProduct> getExplainingByRoomId(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return null;
        }
        return baseMapper.selectExplainingByRoomId(roomId);
    }

    @Override
    public LiveRoomProduct getByRoomIdAndProductId(String roomId, Integer productId) {
        if (StringUtils.isEmpty(roomId) || productId == null) {
            return null;
        }
        return baseMapper.selectByRoomIdAndProductId(roomId, productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startExplaining(String roomId, Integer productId) {
        try {
            // 先结束其他正在讲解的商品
            LambdaUpdateWrapper<LiveRoomProduct> stopWrapper = new LambdaUpdateWrapper<>();
            stopWrapper.eq(LiveRoomProduct::getRoomId, roomId)
                    .eq(LiveRoomProduct::getIsExplaining, true)
                    .set(LiveRoomProduct::getIsExplaining, false)
                    .set(LiveRoomProduct::getExplainEndTime, new Date())
                    .set(LiveRoomProduct::getUpdateTime, new Date());
            update(stopWrapper);

            // 开始讲解指定商品
            LambdaUpdateWrapper<LiveRoomProduct> startWrapper = new LambdaUpdateWrapper<>();
            startWrapper.eq(LiveRoomProduct::getRoomId, roomId)
                    .eq(LiveRoomProduct::getProductId, productId)
                    .set(LiveRoomProduct::getIsExplaining, true)
                    .set(LiveRoomProduct::getExplainStartTime, new Date())
                    .set(LiveRoomProduct::getExplainEndTime, null)
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            boolean result = update(startWrapper);
            if (result) {
                log.info("开始讲解商品: roomId={}, productId={}", roomId, productId);
            }
            return result;
        } catch (Exception e) {
            log.error("开始讲解商品失败: roomId={}, productId={}, error={}", roomId, productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopExplaining(String roomId, Integer productId) {
        try {
            LambdaUpdateWrapper<LiveRoomProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoomProduct::getRoomId, roomId)
                    .eq(LiveRoomProduct::getProductId, productId)
                    .eq(LiveRoomProduct::getIsExplaining, true)
                    .set(LiveRoomProduct::getIsExplaining, false)
                    .set(LiveRoomProduct::getExplainEndTime, new Date())
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            boolean result = update(wrapper);
            if (result) {
                log.info("结束讲解商品: roomId={}, productId={}", roomId, productId);
            }
            return result;
        } catch (Exception e) {
            log.error("结束讲解商品失败: roomId={}, productId={}, error={}", roomId, productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addProductToRoom(String roomId, Integer productId) {
        try {
            // 检查商品是否已存在
            LiveRoomProduct existing = getByRoomIdAndProductId(roomId, productId);
            if (existing != null) {
                log.warn("商品已存在于直播间: roomId={}, productId={}", roomId, productId);
                return false;
            }

            // 获取商品信息
            StoreProduct storeProduct = storeProductService.getById(productId);
            if (storeProduct == null) {
                log.error("商品不存在: productId={}", productId);
                return false;
            }

            // 创建直播间商品
            LiveRoomProduct liveRoomProduct = new LiveRoomProduct();
            liveRoomProduct.setRoomId(roomId);
            liveRoomProduct.setProductId(productId);
            liveRoomProduct.setProductName(storeProduct.getStoreName());
            liveRoomProduct.setProductImage(storeProduct.getImage());
            liveRoomProduct.setProductPrice(storeProduct.getPrice());
            liveRoomProduct.setOriginalPrice(storeProduct.getOtPrice());
            liveRoomProduct.setStock(storeProduct.getStock());
            liveRoomProduct.setSales(storeProduct.getSales());
            liveRoomProduct.setProductInfo(storeProduct.getStoreInfo());
            liveRoomProduct.setIsRecommend(false);
            liveRoomProduct.setIsHot(false);
            liveRoomProduct.setSort(0);
            liveRoomProduct.setStatus(true);
            liveRoomProduct.setIsExplaining(false);
            liveRoomProduct.setCreateTime(new Date());
            liveRoomProduct.setUpdateTime(new Date());

            boolean result = save(liveRoomProduct);
            if (result) {
                log.info("添加商品到直播间成功: roomId={}, productId={}, productName={}",
                        roomId, productId, storeProduct.getStoreName());
            }
            return result;
        } catch (Exception e) {
            log.error("添加商品到直播间失败: roomId={}, productId={}, error={}", roomId, productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeProductFromRoom(String roomId, Integer productId) {
        try {
            LambdaUpdateWrapper<LiveRoomProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoomProduct::getRoomId, roomId)
                    .eq(LiveRoomProduct::getProductId, productId)
                    .set(LiveRoomProduct::getStatus, false)
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            boolean result = update(wrapper);
            if (result) {
                log.info("从直播间移除商品: roomId={}, productId={}", roomId, productId);
            }
            return result;
        } catch (Exception e) {
            log.error("从直播间移除商品失败: roomId={}, productId={}, error={}", roomId, productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateSort(Integer id, Integer sort) {
        try {
            LambdaUpdateWrapper<LiveRoomProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoomProduct::getId, id)
                    .set(LiveRoomProduct::getSort, sort)
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            return update(wrapper);
        } catch (Exception e) {
            log.error("更新商品排序失败: id={}, sort={}, error={}", id, sort, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateRecommendStatus(Integer id, Boolean isRecommend) {
        try {
            LambdaUpdateWrapper<LiveRoomProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoomProduct::getId, id)
                    .set(LiveRoomProduct::getIsRecommend, isRecommend)
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            return update(wrapper);
        } catch (Exception e) {
            log.error("更新商品推荐状态失败: id={}, isRecommend={}, error={}", id, isRecommend, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateHotStatus(Integer id, Boolean isHot) {
        try {
            LambdaUpdateWrapper<LiveRoomProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoomProduct::getId, id)
                    .set(LiveRoomProduct::getIsHot, isHot)
                    .set(LiveRoomProduct::getUpdateTime, new Date());

            return update(wrapper);
        } catch (Exception e) {
            log.error("更新商品热卖状态失败: id={}, isHot={}, error={}", id, isHot, e.getMessage(), e);
            return false;
        }
    }
}
