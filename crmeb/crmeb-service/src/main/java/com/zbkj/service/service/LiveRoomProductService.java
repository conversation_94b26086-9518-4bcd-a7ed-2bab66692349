package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.live.LiveRoomProduct;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 直播间商品服务接口
 */
public interface LiveRoomProductService extends IService<LiveRoomProduct> {

    /**
     * 获取直播间商品列表
     * @param roomId 直播间ID
     * @return 商品列表
     */
    List<LiveRoomProduct> getByRoomId(String roomId);

    /**
     * 获取直播间推荐商品列表
     * @param roomId 直播间ID
     * @return 推荐商品列表
     */
    List<LiveRoomProduct> getRecommendByRoomId(String roomId);

    /**
     * 获取直播间热卖商品列表
     * @param roomId 直播间ID
     * @return 热卖商品列表
     */
    List<LiveRoomProduct> getHotByRoomId(String roomId);

    /**
     * 获取正在讲解的商品
     * @param roomId 直播间ID
     * @return 正在讲解的商品列表
     */
    List<LiveRoomProduct> getExplainingByRoomId(String roomId);

    /**
     * 根据商品ID获取直播间商品信息
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @return 商品信息
     */
    LiveRoomProduct getByRoomIdAndProductId(String roomId, Integer productId);

    /**
     * 开始讲解商品
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean startExplaining(String roomId, Integer productId);

    /**
     * 结束讲解商品
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean stopExplaining(String roomId, Integer productId);

    /**
     * 添加商品到直播间
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean addProductToRoom(String roomId, Integer productId);

    /**
     * 添加商品到直播间（带详细信息）
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @param productName 商品名称
     * @param productPrice 商品价格
     * @param stock 库存
     * @param sort 排序
     * @param status 状态
     * @return 是否成功
     */
    boolean addProductToRoom(String roomId, Integer productId, String productName,
                           BigDecimal productPrice, Integer stock, Integer sort, Integer status);

    /**
     * 从直播间移除商品
     * @param roomId 直播间ID
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean removeProductFromRoom(String roomId, Integer productId);

    /**
     * 更新商品排序
     * @param id 商品ID
     * @param sort 排序权重
     * @return 是否成功
     */
    boolean updateSort(Integer id, Integer sort);

    /**
     * 更新商品推荐状态
     * @param id 商品ID
     * @param isRecommend 是否推荐
     * @return 是否成功
     */
    boolean updateRecommendStatus(Integer id, Boolean isRecommend);

    /**
     * 更新商品热卖状态
     * @param id 商品ID
     * @param isHot 是否热卖
     * @return 是否成功
     */
    boolean updateHotStatus(Integer id, Boolean isHot);

    /**
     * 更新商品价格
     * @param id 商品ID
     * @param price 新价格
     * @return 是否成功
     */
    boolean updateProductPrice(Integer id, BigDecimal price);

    /**
     * 更新商品库存
     * @param id 商品ID
     * @param stock 新库存
     * @return 是否成功
     */
    boolean updateProductStock(Integer id, Integer stock);

    /**
     * 更新商品状态（上架/下架）
     * @param id 商品ID
     * @param status 状态（0-下架，1-上架）
     * @return 是否成功
     */
    boolean updateProductStatus(Integer id, Integer status);
}
