package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.model.live.LiveRoomMsg;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.LiveRoomMessageRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.service.dao.LiveRoomMsgDao;
import com.zbkj.service.service.LiveRoomMsgService;
import com.zbkj.service.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LiveRoomMsgServiceImpl extends ServiceImpl<LiveRoomMsgDao, LiveRoomMsg> implements LiveRoomMsgService {

    @Resource
    private LiveRoomMsgDao dao;

    @Autowired
    private UserService userService;

    @Override
    public LiveRoomMessageResponse saveMessage(LiveRoomMessageRequest request) {
        // 获取当前用户信息
        User currentUser = userService.getInfo();

        // 创建消息实体
        LiveRoomMsg liveRoomMsg = new LiveRoomMsg();
        liveRoomMsg.setRoomId(request.getRoomId());
        liveRoomMsg.setMsgType(Long.valueOf(request.getMsgType()));
        liveRoomMsg.setContent(request.getContent());
        liveRoomMsg.setCreateTime(new Date());
        liveRoomMsg.setUpdateTime(new Date());

        if (currentUser != null) {
            liveRoomMsg.setPublisher(currentUser.getUid().toString());
        } else {
            liveRoomMsg.setPublisher("游客" + System.currentTimeMillis() % 10000);
        }

        // 保存到数据库
        save(liveRoomMsg);

        // 构建响应对象
        LiveRoomMessageResponse response = new LiveRoomMessageResponse();
        BeanUtils.copyProperties(liveRoomMsg, response);
        response.setMsgType(request.getMsgType());

        if (currentUser != null) {
            response.setUsername(currentUser.getNickname());
            response.setAvatar(currentUser.getAvatar());
        } else {
            response.setUsername(request.getUsername() != null ? request.getUsername() : "游客");
            response.setAvatar(request.getAvatar() != null ? request.getAvatar() : "https://picsum.photos/200/200?random=" + System.currentTimeMillis() % 100);
        }
        response.setIsNew(true);

        return response;
    }

    @Override
    public CommonPage<LiveRoomMessageResponse> getMessageList(String roomId, PageParamRequest pageParamRequest) {
        Page<LiveRoomMsg> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<LiveRoomMsg> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoomMsg::getRoomId, roomId);
        wrapper.orderByDesc(LiveRoomMsg::getCreateTime);

        IPage<LiveRoomMsg> messageIPage = page(page, wrapper);

        List<LiveRoomMessageResponse> responseList = messageIPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return CommonPage.restPage(responseList);
    }

    @Override
    public List<LiveRoomMessageResponse> getRecentMessages(String roomId, Integer limit) {
        LambdaQueryWrapper<LiveRoomMsg> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoomMsg::getRoomId, roomId);
        wrapper.orderByDesc(LiveRoomMsg::getCreateTime);
        wrapper.last("LIMIT " + limit);

        List<LiveRoomMsg> messages = list(wrapper);

        return messages.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应对象
     */
    private LiveRoomMessageResponse convertToResponse(LiveRoomMsg liveRoomMsg) {
        LiveRoomMessageResponse response = new LiveRoomMessageResponse();
        BeanUtils.copyProperties(liveRoomMsg, response);
        response.setMsgType(liveRoomMsg.getMsgType().intValue());

        // 这里可以根据publisher获取用户信息，暂时使用默认值
        response.setUsername("用户" + liveRoomMsg.getPublisher());
        response.setAvatar("https://picsum.photos/200/200?random=" + Math.abs(liveRoomMsg.getPublisher().hashCode()) % 100);

        return response;
    }
}

