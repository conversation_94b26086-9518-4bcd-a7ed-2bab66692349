package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.live.LiveRoomMsg;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.LiveRoomMessageRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.LiveRoomMessageResponse;

import java.util.List;

public interface LiveRoomMsgService extends IService<LiveRoomMsg> {

    /**
     * 保存直播间消息
     * @param request 消息请求对象
     * @return 消息响应对象
     */
    LiveRoomMessageResponse saveMessage(LiveRoomMessageRequest request);

    /**
     * 获取直播间消息列表
     * @param roomId 直播间ID
     * @param pageParamRequest 分页参数
     * @return 消息列表
     */
    CommonPage<LiveRoomMessageResponse> getMessageList(String roomId, PageParamRequest pageParamRequest);

    /**
     * 获取直播间最近消息
     * @param roomId 直播间ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<LiveRoomMessageResponse> getRecentMessages(String roomId, Integer limit);
}
