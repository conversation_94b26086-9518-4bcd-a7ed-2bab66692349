package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomResponse;

import java.util.List;

public interface LiveRoomService extends IService<LiveRoom> {

    LiveRoomInfoResponse getLiveRoomInfo(String code);

    /**
     * 获取直播间详情
     * @param roomId 直播间ID
     * @return 直播间详情
     */
    LiveRoomResponse getLiveRoomDetail(String roomId);

    /**
     * 获取热卖商品列表
     * @param limit 限制数量
     * @return 商品列表
     */
    List<IndexProductResponse> getHotProducts(Integer limit);
}
