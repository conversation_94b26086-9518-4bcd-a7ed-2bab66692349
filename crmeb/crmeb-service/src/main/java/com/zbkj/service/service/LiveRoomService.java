package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomResponse;

import java.util.List;

public interface LiveRoomService extends IService<LiveRoom> {

    LiveRoomInfoResponse getLiveRoomInfo(String code);

    /**
     * 获取直播间信息
     * @param roomId 直播间ID
     * @return 直播间信息
     */
    LiveRoomInfoResponse getRoomInfo(String roomId);

    /**
     * 获取直播间详情
     * @param roomId 直播间ID
     * @return 直播间详情
     */
    LiveRoomResponse getLiveRoomDetail(String roomId);

    /**
     * 获取热卖商品列表
     * @param limit 限制数量
     * @return 商品列表
     */
    List<IndexProductResponse> getHotProducts(Integer limit);

    /**
     * 分页获取直播间列表
     * @param pageParamRequest 分页参数
     * @return 分页直播间列表
     */
    CommonPage<LiveRoom> getList(PageParamRequest pageParamRequest);

    /**
     * 根据房间ID获取直播间
     * @param roomId 房间ID
     * @return 直播间信息
     */
    LiveRoom getByRoomId(String roomId);

    /**
     * 开始直播
     * @param roomId 房间ID
     * @return 是否成功
     */
    boolean startLive(String roomId);

    /**
     * 结束直播
     * @param roomId 房间ID
     * @return 是否成功
     */
    boolean stopLive(String roomId);

    /**
     * 创建直播间
     * @param liveRoom 直播间信息
     * @return 是否成功
     */
    boolean create(LiveRoom liveRoom);

    /**
     * 根据房间ID删除直播间
     * @param roomId 房间ID
     * @return 是否成功
     */
    boolean deleteByRoomId(String roomId);
}
