package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.model.product.StoreProduct;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.common.response.LiveRoomResponse;
import com.zbkj.service.dao.LiveRoomDao;
import com.zbkj.service.service.LiveRoomMsgService;
import com.zbkj.service.service.LiveRoomService;
import com.zbkj.service.service.StoreProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LiveRoomServiceImpl extends ServiceImpl<LiveRoomDao, LiveRoom> implements LiveRoomService {

    @Resource
    private LiveRoomDao dao;

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    @Autowired
    private StoreProductService storeProductService;

    @Override
    public LiveRoomInfoResponse getLiveRoomInfo(String code) {
        // 原有方法保持不变
        return null;
    }

    @Override
    public LiveRoomInfoResponse getRoomInfo(String roomId) {
        // 查询直播间信息
        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoom::getRoomId, roomId);
        LiveRoom liveRoom = getOne(wrapper);

        if (liveRoom == null) {
            return null;
        }

        // 构建响应对象
        LiveRoomInfoResponse response = new LiveRoomInfoResponse();

        // 构建房间信息
        LiveRoomInfoResponse.RoomInfo roomInfo = new LiveRoomInfoResponse.RoomInfo();
        roomInfo.setRoomId(liveRoom.getRoomId());
        roomInfo.setTitle(liveRoom.getName() != null ? liveRoom.getName() + "的直播间" : "直播间");
        roomInfo.setAvatar(liveRoom.getAvatar());
        roomInfo.setCover(liveRoom.getCover());

        // 设置访问人数（从数据库获取，实时在线人数由上层业务逻辑处理）
        roomInfo.setViewCount(liveRoom.getVisitorCount() != null ? liveRoom.getVisitorCount().intValue() : 0);

        roomInfo.setStatus(liveRoom.getStatus() != null ? liveRoom.getStatus() : 1);

        // 时间戳转换为毫秒
        roomInfo.setStartTime(liveRoom.getStartTime() != null ? liveRoom.getStartTime() * 1000 : System.currentTimeMillis());
        roomInfo.setEndTime(liveRoom.getEndTime() != null ? liveRoom.getEndTime() * 1000 : 0L);

        response.setRoomInfo(roomInfo);

        response.setChatUrl("wss://sy-ws.aimaibumai.com/chat/" + roomId);
        response.setLiveUrl("webrtc://live.aimaibumai.com/live/" + roomId);

        return response;
    }

    @Override
    public LiveRoomResponse getLiveRoomDetail(String roomId) {
        // 查询直播间信息
        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoom::getRoomId, roomId);
        LiveRoom liveRoom = getOne(wrapper);

        if (liveRoom == null) {
            return null;
        }

        // 构建响应对象
        LiveRoomResponse response = new LiveRoomResponse();
        BeanUtils.copyProperties(liveRoom, response);

        // 设置直播间标题和分类（这里可以根据实际需求调整）
        response.setTitle(liveRoom.getName() + "的直播间");
        response.setCategory("商品直播");

        // 获取在线人数
        //response.setOnlineCount(LiveRoomWebSocketHandler.getRoomOnlineCount(roomId));

        // 获取最近消息
        List<LiveRoomMessageResponse> recentMessages = liveRoomMsgService.getRecentMessages(roomId, 10);
        response.setRecentMessages(recentMessages);

        // 获取热卖商品
        List<IndexProductResponse> hotProducts = getHotProducts(4);
        response.setHotProducts(hotProducts);

        return response;
    }

    @Override
    public List<IndexProductResponse> getHotProducts(Integer limit) {
        // 查询热卖商品（按销量排序）
        LambdaQueryWrapper<StoreProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreProduct::getIsShow, true);
        wrapper.eq(StoreProduct::getIsDel, false);
        wrapper.orderByDesc(StoreProduct::getSales);
        wrapper.last("LIMIT " + limit);

        List<StoreProduct> products = storeProductService.list(wrapper);

        return products.stream().map(product -> {
            IndexProductResponse response = new IndexProductResponse();
            response.setId(product.getId());
            response.setStoreName(product.getStoreName());
            response.setImage(product.getImage());
            response.setPrice(product.getPrice());
            response.setOtPrice(product.getOtPrice());
            response.setSales(product.getSales());
            response.setStock(product.getStock());
            response.setActivity(null); // 暂不处理活动信息
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 分页获取直播间列表
     */
    @Override
    public CommonPage<LiveRoom> getList(PageParamRequest pageParamRequest) {
        Page<LiveRoom> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(LiveRoom::getCreateTime);

        Page<LiveRoom> result = page(page, wrapper);
        PageInfo<LiveRoom> pageInfo = new PageInfo<>();

        pageInfo.setTotal(result.getTotal());
        pageInfo.setPages(Long.valueOf(result.getPages()).intValue());
        pageInfo.setTotal(result.getCurrent());
        pageInfo.setPageSize(Long.valueOf(result.getSize()).intValue());
        pageInfo.setList(result.getRecords());

        return CommonPage.restPage(pageInfo);
    }

    /**
     * 根据房间ID获取直播间
     */
    @Override
    public LiveRoom getByRoomId(String roomId) {
        if (roomId == null || roomId.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoom::getRoomId, roomId);

        return getOne(wrapper);
    }

    /**
     * 开始直播
     */
    @Override
    public boolean startLive(String roomId) {
        try {
            LambdaUpdateWrapper<LiveRoom> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoom::getRoomId, roomId)
                    .set(LiveRoom::getStatus, 1) // 1-直播中
                    .set(LiveRoom::getStartTime, System.currentTimeMillis()) // 转换为秒级时间戳
                    .set(LiveRoom::getEndTime, 0L);

            boolean result = update(wrapper);
            if (result) {
                log.info("开始直播成功: roomId={}", roomId);
            }
            return result;
        } catch (Exception e) {
            log.error("开始直播失败: roomId={}, error={}", roomId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 结束直播
     */
    @Override
    public boolean stopLive(String roomId) {
        try {
            LambdaUpdateWrapper<LiveRoom> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LiveRoom::getRoomId, roomId)
                    .set(LiveRoom::getStatus, 2) // 2-已结束
                    .set(LiveRoom::getEndTime, System.currentTimeMillis());

            boolean result = update(wrapper);
            if (result) {
                log.info("结束直播成功: roomId={}", roomId);
            }
            return result;
        } catch (Exception e) {
            log.error("结束直播失败: roomId={}, error={}", roomId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建直播间
     */
    @Override
    public boolean create(LiveRoom liveRoom) {
        try {
            // 生成唯一的房间ID
            if (liveRoom.getRoomId() == null || liveRoom.getRoomId().trim().isEmpty()) {
                liveRoom.setRoomId("room_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8));
            }

            // 设置默认值
            if (liveRoom.getStatus() == null) {
                liveRoom.setStatus(0); // 0-未开播
            }

            if (liveRoom.getVisitorCount() == null) {
                liveRoom.setVisitorCount(0L);
            }

            boolean result = save(liveRoom);
            if (result) {
                log.info("创建直播间成功: roomId={}, roomName={}", liveRoom.getRoomId(), liveRoom.getName());
            }
            return result;
        } catch (Exception e) {
            log.error("创建直播间失败: roomName={}, error={}", liveRoom.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据房间ID删除直播间
     */
    @Override
    public boolean deleteByRoomId(String roomId) {
        try {
            // 检查直播间是否存在
            LiveRoom liveRoom = getByRoomId(roomId);
            if (liveRoom == null) {
                log.warn("要删除的直播间不存在: roomId={}", roomId);
                return false;
            }

            // 检查直播间状态，如果正在直播中则不允许删除
            if (liveRoom.getStatus() != null && liveRoom.getStatus() == 1) {
                log.warn("直播间正在直播中，不允许删除: roomId={}", roomId);
                return false;
            }

            LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(LiveRoom::getRoomId, roomId);

            boolean result = remove(wrapper);
            if (result) {
                log.info("删除直播间成功: roomId={}", roomId);

                // TODO: 这里可以添加清理相关数据的逻辑
                // 比如清理直播间消息、商品等
            }
            return result;
        } catch (Exception e) {
            log.error("删除直播间失败: roomId={}, error={}", roomId, e.getMessage(), e);
            return false;
        }
    }
}

