package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.model.product.StoreProduct;
import com.zbkj.common.response.IndexProductResponse;
import com.zbkj.common.response.LiveRoomInfoResponse;
import com.zbkj.common.response.LiveRoomMessageResponse;
import com.zbkj.common.response.LiveRoomResponse;
import com.zbkj.front.websocket.LiveRoomWebSocketHandler;
import com.zbkj.service.dao.LiveRoomDao;
import com.zbkj.service.service.LiveRoomMsgService;
import com.zbkj.service.service.LiveRoomService;
import com.zbkj.service.service.StoreProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LiveRoomServiceImpl extends ServiceImpl<LiveRoomDao, LiveRoom> implements LiveRoomService {

    @Resource
    private LiveRoomDao dao;

    @Autowired
    private LiveRoomMsgService liveRoomMsgService;

    @Autowired
    private StoreProductService storeProductService;

    @Override
    public LiveRoomInfoResponse getLiveRoomInfo(String code) {
        // 原有方法保持不变
        return null;
    }

    @Override
    public LiveRoomInfoResponse getRoomInfo(String roomId) {
        // 查询直播间信息
        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoom::getRoomId, roomId);
        LiveRoom liveRoom = getOne(wrapper);

        if (liveRoom == null) {
            return null;
        }

        // 构建响应对象
        LiveRoomInfoResponse response = new LiveRoomInfoResponse();

        // 构建房间信息
        LiveRoomInfoResponse.RoomInfo roomInfo = new LiveRoomInfoResponse.RoomInfo();
        roomInfo.setRoomId(liveRoom.getRoomId());
        roomInfo.setTitle(liveRoom.getName() != null ? liveRoom.getName() + "的直播间" : "直播间");
        roomInfo.setCover(liveRoom.getAvatar());

        // 设置访问人数（从数据库获取，实时在线人数由上层业务逻辑处理）
        roomInfo.setViewCount(liveRoom.getVisitorCount() != null ? liveRoom.getVisitorCount().intValue() : 0);

        roomInfo.setStatus(liveRoom.getStatus() != null ? liveRoom.getStatus() : 1);

        // 时间戳转换为毫秒
        roomInfo.setStartTime(liveRoom.getStartTime() != null ? liveRoom.getStartTime() * 1000 : System.currentTimeMillis());
        roomInfo.setEndTime(liveRoom.getEndTime() != null ? liveRoom.getEndTime() * 1000 : 0L);

        response.setRoomInfo(roomInfo);

        // 动态构建WebSocket连接地址
        String protocol = "ws"; // 可以根据环境配置
        String host = "localhost"; // 可以从配置文件获取
        String port = "8081"; // 可以从配置文件获取
        response.setSocketUrl(String.format("%s://%s:%s/api/front/live/websocket/%s", protocol, host, port, roomId));

        // 设置直播地址（根据roomId动态生成）
        response.setLiveUrl("https://live.aimaibumai.com/live/" + roomId + ".flv");

        return response;
    }

    @Override
    public LiveRoomResponse getLiveRoomDetail(String roomId) {
        // 查询直播间信息
        LambdaQueryWrapper<LiveRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LiveRoom::getRoomId, roomId);
        LiveRoom liveRoom = getOne(wrapper);

        if (liveRoom == null) {
            return null;
        }

        // 构建响应对象
        LiveRoomResponse response = new LiveRoomResponse();
        BeanUtils.copyProperties(liveRoom, response);

        // 设置直播间标题和分类（这里可以根据实际需求调整）
        response.setTitle(liveRoom.getName() + "的直播间");
        response.setCategory("商品直播");

        // 获取在线人数
        response.setOnlineCount(LiveRoomWebSocketHandler.getRoomOnlineCount(roomId));

        // 获取最近消息
        List<LiveRoomMessageResponse> recentMessages = liveRoomMsgService.getRecentMessages(roomId, 10);
        response.setRecentMessages(recentMessages);

        // 获取热卖商品
        List<IndexProductResponse> hotProducts = getHotProducts(4);
        response.setHotProducts(hotProducts);

        return response;
    }

    @Override
    public List<IndexProductResponse> getHotProducts(Integer limit) {
        // 查询热卖商品（按销量排序）
        LambdaQueryWrapper<StoreProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreProduct::getIsShow, true);
        wrapper.eq(StoreProduct::getIsDel, false);
        wrapper.orderByDesc(StoreProduct::getSales);
        wrapper.last("LIMIT " + limit);

        List<StoreProduct> products = storeProductService.list(wrapper);

        return products.stream().map(product -> {
            IndexProductResponse response = new IndexProductResponse();
            response.setId(product.getId());
            response.setStoreName(product.getStoreName());
            response.setImage(product.getImage());
            response.setPrice(product.getPrice());
            response.setOtPrice(product.getOtPrice());
            response.setSales(product.getSales());
            response.setStock(product.getStock());
            response.setActivity(null); // 暂不处理活动信息
            return response;
        }).collect(Collectors.toList());
    }
}

