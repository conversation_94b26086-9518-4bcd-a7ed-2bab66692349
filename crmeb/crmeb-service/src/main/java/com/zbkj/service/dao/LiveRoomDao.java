package com.zbkj.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zbkj.common.model.live.LiveRoom;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 直播间Dao
 */
public interface LiveRoomDao extends BaseMapper<LiveRoom> {

    /**
     * 根据房间ID查询直播间
     */
    @Select("SELECT * FROM eb_live_room WHERE room_id = #{roomId}")
    LiveRoom selectByRoomId(@Param("roomId") String roomId);

    /**
     * 更新直播间状态
     */
    @Update("UPDATE eb_live_room SET status = #{status} WHERE room_id = #{roomId}")
    int updateStatusByRoomId(@Param("roomId") String roomId, @Param("status") Integer status);

    /**
     * 更新直播间开始时间
     */
    @Update("UPDATE eb_live_room SET status = #{status}, start_time = #{startTime}, update_time = NOW() WHERE room_id = #{roomId}")
    int updateStartTimeByRoomId(@Param("roomId") String roomId, @Param("status") Integer status, @Param("startTime") Long startTime);

    /**
     * 更新直播间结束时间
     */
    @Update("UPDATE eb_live_room SET status = #{status}, end_time = #{endTime}, update_time = NOW() WHERE room_id = #{roomId}")
    int updateEndTimeByRoomId(@Param("roomId") String roomId, @Param("status") Integer status, @Param("endTime") Long endTime);

    /**
     * 增加访问人数
     */
    @Update("UPDATE eb_live_room SET visitor_count = visitor_count + 1, update_time = NOW() WHERE room_id = #{roomId}")
    int incrementVisitorCount(@Param("roomId") String roomId);

    /**
     * 获取正在直播的房间列表
     */
    @Select("SELECT * FROM eb_live_room WHERE status = 1 ORDER BY start_time DESC")
    List<LiveRoom> selectLivingRooms();

    /**
     * 获取热门直播间（按访问人数排序）
     */
    @Select("SELECT * FROM eb_live_room WHERE status IN (0, 1) ORDER BY visitor_count DESC LIMIT #{limit}")
    List<LiveRoom> selectHotRooms(@Param("limit") Integer limit);

    /**
     * 根据分类获取直播间
     */
    @Select("SELECT * FROM eb_live_room WHERE category = #{category} AND status IN (0, 1) ORDER BY create_time DESC")
    List<LiveRoom> selectByCategory(@Param("category") String category);
}
