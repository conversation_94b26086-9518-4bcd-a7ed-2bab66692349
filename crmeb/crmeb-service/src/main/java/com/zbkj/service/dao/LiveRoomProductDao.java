package com.zbkj.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zbkj.common.model.live.LiveRoomProduct;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 直播间商品Dao
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public interface LiveRoomProductDao extends BaseMapper<LiveRoomProduct> {

    /**
     * 获取直播间商品列表（按排序权重和创建时间排序）
     */
    @Select("SELECT * FROM eb_live_room_product " +
            "WHERE room_id = #{roomId} AND status = 1 " +
            "ORDER BY sort DESC, create_time ASC")
    List<LiveRoomProduct> selectByRoomIdOrderBySort(@Param("roomId") String roomId);

    /**
     * 获取直播间推荐商品列表
     */
    @Select("SELECT * FROM eb_live_room_product " +
            "WHERE room_id = #{roomId} AND status = 1 AND is_recommend = 1 " +
            "ORDER BY sort DESC, create_time ASC")
    List<LiveRoomProduct> selectRecommendByRoomId(@Param("roomId") String roomId);

    /**
     * 获取直播间热卖商品列表
     */
    @Select("SELECT * FROM eb_live_room_product " +
            "WHERE room_id = #{roomId} AND status = 1 AND is_hot = 1 " +
            "ORDER BY sort DESC, sales DESC, create_time ASC")
    List<LiveRoomProduct> selectHotByRoomId(@Param("roomId") String roomId);

    /**
     * 获取正在讲解的商品
     */
    @Select("SELECT * FROM eb_live_room_product " +
            "WHERE room_id = #{roomId} AND status = 1 AND is_explaining = 1 " +
            "ORDER BY explain_start_time DESC")
    List<LiveRoomProduct> selectExplainingByRoomId(@Param("roomId") String roomId);

    /**
     * 根据商品ID获取直播间商品信息
     */
    @Select("SELECT * FROM eb_live_room_product " +
            "WHERE room_id = #{roomId} AND product_id = #{productId} AND status = 1")
    LiveRoomProduct selectByRoomIdAndProductId(@Param("roomId") String roomId, @Param("productId") Integer productId);

    /**
     * 更新商品讲解状态
     */
    @Select("UPDATE eb_live_room_product SET is_explaining = #{isExplaining}, " +
            "explain_start_time = #{explainStartTime}, explain_end_time = #{explainEndTime}, " +
            "update_time = NOW() " +
            "WHERE id = #{id}")
    int updateExplainingStatus(@Param("id") Integer id, 
                              @Param("isExplaining") Boolean isExplaining,
                              @Param("explainStartTime") java.util.Date explainStartTime,
                              @Param("explainEndTime") java.util.Date explainEndTime);
}
