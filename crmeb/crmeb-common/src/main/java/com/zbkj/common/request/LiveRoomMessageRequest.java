package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 直播间消息请求对象
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LiveRoomMessageRequest", description = "直播间消息请求对象")
public class LiveRoomMessageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "直播间ID", required = true)
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;

    @ApiModelProperty(value = "消息类型 1-文本消息 2-系统消息 3-商品推荐", required = true)
    @NotNull(message = "消息类型不能为空")
    private Integer msgType;

    @ApiModelProperty(value = "消息内容", required = true)
    @NotBlank(message = "消息内容不能为空")
    private String content;

    @ApiModelProperty(value = "发送者昵称")
    private String username;

    @ApiModelProperty(value = "发送者头像")
    private String avatar;
}
