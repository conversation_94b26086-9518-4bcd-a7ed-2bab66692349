package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 更新商品状态请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UpdateProductStatusRequest对象", description = "更新商品状态请求")
public class UpdateProductStatusRequest {

    @ApiModelProperty(value = "商品ID", required = true)
    @NotNull(message = "商品ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "商品状态", required = true, notes = "0-下架，1-上架")
    @NotNull(message = "商品状态不能为空")
    @Min(value = 0, message = "商品状态值不正确")
    @Max(value = 1, message = "商品状态值不正确")
    private Integer status;
}
