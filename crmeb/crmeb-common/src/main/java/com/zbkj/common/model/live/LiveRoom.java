package com.zbkj.common.model.live;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 直播间
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_live_room")
@ApiModel(value = "LiveRoom", description = "直播间")
public class LiveRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文章管理ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "直播间ID")
    private String roomId;

    @ApiModelProperty(value = "主播ID")
    private Long anchorId;

    @ApiModelProperty(value = "主播头像")
    private String avatar;

    @ApiModelProperty(value = "主播名称")
    private String name;

    @ApiModelProperty(value = "直播间名称")
    private String roomName;

    @ApiModelProperty(value = "直播间描述")
    private String roomDesc;

    @ApiModelProperty(value = "主播名称")
    private String anchorName;

    @ApiModelProperty(value = "主播头像")
    private String anchorAvatar;

    @ApiModelProperty(value = "分类")
    private String category;

    @ApiModelProperty(value = "直播封面")
    private String cover;

    @ApiModelProperty(value = "访问人数")
    private Long visitorCount;

    @ApiModelProperty(value = "在线人数")
    private Integer onlineCount;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建时间")
    private Date updateTime;


}
