package com.zbkj.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播间消息类型枚举
 **/
@Getter
@AllArgsConstructor
public enum LiveRoomMessageTypeEnum {

    /// /// 以下为客户端发送消息 /// ///

    REQ_AUTH(1001, "发送连接认证"),

    REQ_TEXT_MSG(1002, "发送文本消息"),

    /// /// 以下为服务端下发消息 /// ///

    RES_FORCE_CLOSE(2001, "强制关闭连接"),
    RES_SYSTEM_MSG(2002, "系统消息"),
    RES_WELCOME_MSG(2003, "欢迎进入直播间"),
    RES_TEXT_MSG(2004, "收到文本消息"),
    RES_PROD_ON_SALE(2005, "商品讲解上架"),

    ;

    private final int code;
    private final String description;


    public static LiveRoomMessageTypeEnum getByCode(int type) {
        for (LiveRoomMessageTypeEnum value : LiveRoomMessageTypeEnum.values()) {
            if (value.getCode() == type) {
                return value;
            }
        }
        return null;
    }
}
