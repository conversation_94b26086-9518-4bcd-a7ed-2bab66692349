package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 直播间信息响应对象
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LiveRoomInfoResponse", description = "直播间信息响应对象")
public class LiveRoomInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "直播间信息")
    private RoomInfo roomInfo;

    @ApiModelProperty(value = "WebSocket连接地址")
    private String socketUrl;

    @ApiModelProperty(value = "直播地址")
    private String liveUrl;

    @Data
    @Accessors(chain = true)
    public static class RoomInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "房间ID")
        private String roomId;

        @ApiModelProperty(value = "直播间标题")
        private String title;

        @ApiModelProperty(value = "直播间封面图片地址")
        private String cover;

        @ApiModelProperty(value = "观看人数")
        private Integer viewCount;

        @ApiModelProperty(value = "状态 0-未开始 1-直播中 2-已结束")
        private Integer status;

        @ApiModelProperty(value = "开始时间戳(毫秒)")
        private Long startTime;

        @ApiModelProperty(value = "结束时间戳(毫秒)")
        private Long endTime;
    }
}
