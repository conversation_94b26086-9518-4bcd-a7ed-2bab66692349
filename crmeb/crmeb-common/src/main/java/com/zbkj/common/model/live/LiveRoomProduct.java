package com.zbkj.common.model.live;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播间商品表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_live_room_product")
@ApiModel(value = "LiveRoomProduct对象", description = "直播间商品表")
public class LiveRoomProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "直播间ID")
    private String roomId;

    @ApiModelProperty(value = "商品ID")
    private Integer productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品图片")
    private String productImage;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "商品库存")
    private Integer stock;

    @ApiModelProperty(value = "商品销量")
    private Integer sales;

    @ApiModelProperty(value = "商品简介")
    private String productInfo;

    @ApiModelProperty(value = "商品详情页链接")
    private String productUrl;

    @ApiModelProperty(value = "是否推荐：0-否，1-是")
    private Boolean isRecommend;

    @ApiModelProperty(value = "是否热卖：0-否，1-是")
    private Boolean isHot;

    @ApiModelProperty(value = "排序权重，数字越大越靠前")
    private Integer sort;

    @ApiModelProperty(value = "商品状态：0-下架，1-上架")
    private Boolean status;

    @ApiModelProperty(value = "是否正在讲解：0-否，1-是")
    private Boolean isExplaining;

    @ApiModelProperty(value = "讲解开始时间")
    private Date explainStartTime;

    @ApiModelProperty(value = "讲解结束时间")
    private Date explainEndTime;

    @ApiModelProperty(value = "商品标签，多个用逗号分隔")
    private String tags;

    @ApiModelProperty(value = "优惠信息")
    private String discountInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
