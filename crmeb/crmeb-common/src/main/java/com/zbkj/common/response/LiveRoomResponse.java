package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 直播间响应对象
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LiveRoomResponse", description = "直播间响应对象")
public class LiveRoomResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "直播间ID")
    private String roomId;

    @ApiModelProperty(value = "主播ID")
    private Long anchorId;

    @ApiModelProperty(value = "主播头像")
    private String avatar;

    @ApiModelProperty(value = "主播名称")
    private String name;

    @ApiModelProperty(value = "直播间标题")
    private String title;

    @ApiModelProperty(value = "直播间分类")
    private String category;

    @ApiModelProperty(value = "在线人数")
    private Integer onlineCount;

    @ApiModelProperty(value = "访问人数")
    private Long visitorCount;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    @ApiModelProperty(value = "状态 1-直播中 2-已结束")
    private Integer status;

    @ApiModelProperty(value = "最近消息列表")
    private List<LiveRoomMessageResponse> recentMessages;

    @ApiModelProperty(value = "热卖商品列表")
    private List<IndexProductResponse> hotProducts;
}
