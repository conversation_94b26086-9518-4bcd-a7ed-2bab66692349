package com.zbkj.common.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 直播间消息响应对象
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LiveRoomMessageResponse", description = "直播间消息响应对象")
public class LiveRoomMessageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息ID")
    private Long id;

    @ApiModelProperty(value = "直播间ID")
    private String roomId;

    @ApiModelProperty(value = "发送者")
    private String publisher;

    /**
     * @see com.zbkj.common.enums.LiveRoomMessageTypeEnum
     */
    private Integer msgType;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "发送者昵称")
    private String username;

    @ApiModelProperty(value = "发送者头像")
    private String avatar;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
