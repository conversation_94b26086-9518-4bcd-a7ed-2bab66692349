<!DOCTYPE html>
<html lang=zh-CN>
    <head>
        <meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title><%= htmlWebpackPlugin.options.title %></title>
		<script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
		    document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
		
		
		<link href="https://web.sdk.qcloud.com/player/tcplayer/release/v4.9.1/tcplayer.min.css" rel="stylesheet">
		<script src="https://video.sdk.qcloudecdn.com/web/TXLivePlayer-1.3.2.min.js"></script>
		<script src="https://web.sdk.qcloud.com/player/tcplayer/release/v4.9.1/tcplayer.v4.9.1.min.js"></script>
		  
		<!-- 
	    <link rel="stylesheet" href="https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/1.13.1/index.min.css">
	    <script src="https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/1.13.1/index.min.js"></script>
		 <link rel="stylesheet" href="//lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.7.1/umd/veplayer.production.css">
		 <script src="//lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.7.1/umd/veplayer.production.js"></script>
		 -->
	</head>
	<body>
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
	</body>
</html>