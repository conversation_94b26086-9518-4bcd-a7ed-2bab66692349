<template>
	<!-- 秒杀 -->
	<view class="seckill" v-if="spikeList.length">
		<view class="title acea-row row-between-wrapper">
			<view class="acea-row row-middle">
				<view class="pictrue">
					<image src="/static/images/seckillTitle.png"></image>
				</view>
				<view class="lines"></view>
				<view class="point">{{point}} 场</view>
				<countDown :is-day="false" :tip-text="' '" :day-text="' '" :hour-text="' : '" :minute-text="' : '" :second-text="' '"
				 :datatime="datatime" :is-col="true" :bgColor="bgColor"></countDown>
			</view>
			<navigator url="/pages/activity/goods_seckill/index" hover-class="none" class="more acea-row row-center-wrapper">GO<text class="iconfont icon-xiangyou"></text></navigator>
		</view>
		<view class="conter">
			<scroll-view scroll-x="true" style="white-space: nowrap; vertical-align: middle;" show-scrollbar="false">
				<view class="itemCon" v-for="(item, index) in spikeList" :key="index" @click="goDetail(item)">
					<view class="item">
						<view class="pictrue">
							<image :src="item.image"></image>
						</view>
						<view class="name line1">{{item.title}}</view>
						<view class="x_money line1">¥<text class="num">{{item.price}}</text></view>
						<view class="y_money line1">¥{{item.otPrice}}</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	import countDown from "@/components/countDown";
	import {
		getSeckillIndexApi
	} from '@/api/activity.js';
	export default {
		name: 'a_seckill',
		components: {
			countDown
		},
		data() {
			return {
				bgColor: {
					'bgColor': '#fff',
					'Color': '#E93323',
					'width': '44rpx',
					'timeTxtwidth': '16rpx',
					'isDay': true
				},
				spikeList: [], // 秒杀
				point: '',
				datatime: 0,
				status: 0
			}
		},
		created() {
			this.getSeckillIndexTime();
		},
		methods: {
			getSeckillIndexTime() {
				getSeckillIndexApi().then(({data}) => {
					this.spikeList = data ? data.productList : [];
					this.point = data ? data.secKillResponse.time.split(',')[0] : '';
					this.datatime = data ? parseFloat(data.secKillResponse.timeSwap) : '';
					this.status =  data ? data.secKillResponse.status : 0;
				})
			},
			goDetail(item){
				uni.navigateTo({
					url: '/pages/activity/goods_seckill_details/index?id=' + item.id
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.default{
		width: 690rpx;
		height: 300rpx;
		border-radius: 14rpx;
		margin: 26rpx auto 0 auto;
		background-color: #ccc;
		text-align: center;
		line-height: 300rpx;
		.iconfont{
			font-size: 80rpx;
		}
	}
	.seckill {
		width: auto;
		height: 420rpx;
		background-image: url('data:image/png;base64,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');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		border-radius: 14rpx;
		margin: 0 auto 30rpx auto;
		padding: 24rpx;
        box-sizing: border-box;
		.title {
			.pictrue {
				width: 148rpx;
				height: 40rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.lines {
				width: 1rpx;
				height: 24rpx;
				background-color: #fff;
				opacity: 0.6;
				margin-left: 16rpx;
			}

			.point {
				font-size: 30rpx;
				color: #fff;
				margin-left: 21rpx;
				margin-right: 4rpx;
				font-weight: 800;
			}
				.styleAll {
					width: 35rpx;
					height: 35rpx;
					background-color: #2F2F2F;
					border-radius: 6rpx;
					color: #fff;
					text-align: center;
				}

			.more {
				width: 86rpx;
				height: 40rpx;
				background: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);
				opacity: 1;
				border-radius: 18px;
				font-size: 22rpx;
				color: #FE960F;
				padding-left: 8rpx;
                font-weight: 800;
				.iconfont {
					font-size: 21rpx;
				}
			}
		}

		.conter {
			width: 666rpx;
			height: 320rpx;
			border-radius: 12px;
			margin-top: 24rpx;

			.itemCon {
				display: inline-block;
				width: 186rpx;
				margin-right: 24rpx;

				.item {
					width: 100%;

					.pictrue {
						width: 100%;
						height: 186rpx;
						border-radius: 6rpx;

						image {
							width: 100%;
							height: 100%;
							border-radius: 6rpx;
						}
					}

					.name {
						font-size: 24rpx;
						color: #000;
						margin-top: 14rpx;
					}
					.y_money {
						font-size: 24rpx;
						color: #999999;
						text-decoration: line-through;
					}
					
					.x_money {
						color: #FD502F;
						font-size: 28rpx;
						height: 100%;
						font-weight: bold;
					    margin: 2rpx 0;
						.num {
							font-size: 28rpx;
						}
					}

					.money {
						// background: url("data:image/png;base64,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") no-repeat;
						margin-top: 14rpx;
					}
				}
			}
		}
	}
</style>
