<template>
  <view class="live-container">
    <!-- 直播视频背景 -->
    <view id="tcView" ref="tcView" class="video-background">
    </view>

    <!-- 顶部信息区 -->
    <view class="top-info">
      <view class="live-info">
        <view class="avatar">
          <image :src="liveData.avatar" mode="aspectFill"></image>
        </view>
        <view class="info-text">
          <text class="live-title">{{ liveData.title }}</text>
          <text class="live-status" v-if="liveData.status==0">暂未开播</text>
          <text class="live-status" v-else-if="liveData.status==1">正在直播</text>
          <text class="live-status" v-else>直播已结束</text>
        </view>
      </view>
      <view class="viewer-count">
        <text class="iconfont icon-yonghu"></text>
        <text>{{ viewersCount }}</text>
      </view>
    </view>

    <!-- 底部互动区域 -->
    <view class="bottom-interaction" v-if="isLiving">
      <!-- 留言滚动区 -->
      <scroll-view
        class="messages-container"
        scroll-y
        :scroll-top="scrollTop"
        scroll-with-animation
        :scroll-into-view="scrollIntoView"
      >
        <view class="messages-list">
          <view
            v-for="(message, index) in displayMessages"
            :key="index"
            :id="'message-' + index"
            class="message-item"
            :class="{ 'fade-in': message.isNew }"
          >
            <view class="message-content">
              <image class="user-avatar" :src="message.avatar" mode="aspectFill"></image>
              <text class="username">{{ message.username }}:</text>
              <text class="content">{{ message.content }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 输入框区域 -->
      <view class="input-area">
        <view class="input-wrapper">
          <input
            v-model="newMessage"
            class="message-input"
            placeholder="说点什么..."
            @confirm="sendMessage"
            confirm-type="send"
          />
          <button
            class="send-button"
            :class="{ 'disabled': !isConnected }"
            @click="sendMessage"
            :disabled="!isConnected"
          >
            <text class="iconfont icon-fasong"></text>
          </button>
        </view>

        <!-- 购物车按钮 -->
        <view class="cart-button" @click="toggleCart">
          <text class="iconfont icon-gouwuche"></text>
        </view>
      </view>
    </view>

    <!-- 未开播状态提示 -->
    <view v-if="!isLiving" class="not-live-container">
      <view class="not-live-content">
        <text class="not-live-icon">📺</text>
        <text class="not-live-title">
          {{ liveData.status === 2 ? '直播已结束' : '直播间暂未开播' }}
        </text>
        <text class="not-live-desc">
          {{ liveData.status === 2 ? '感谢观看，期待下次直播！' : '正在准备中，请稍后再来' }}
        </text>
      </view>
    </view>

    <!-- 商品抽屉 - 仅在直播中显示 -->
    <view
      v-if="isLiving"
      class="product-drawer"
      :class="{ 'show': cartVisible }"
    >
      <view class="drawer-header">
        <text class="drawer-title">直播间商品</text>
        <text class="iconfont icon-guanbi" @click="toggleCart"></text>
      </view>
      <scroll-view class="drawer-content" scroll-y="true">
        <view v-if="productsLoading" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>
        <view v-else-if="products.length === 0" class="empty-container">
          <text class="empty-text">暂无商品</text>
        </view>
        <view
          v-else
          v-for="(product, index) in products"
          :key="product.id || index"
          class="product-item"
          @click="buyProduct(product)"
        >
          <view class="product-image">
            <image :src="product.image" mode="aspectFill"></image>
            <view class="hot-tag">热卖</view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.storeName }}</text>
            <text class="product-desc">{{ product.storeInfo || '暂无描述' }}</text>
            <view class="product-bottom">
              <text class="product-price">¥{{ product.price }}</text>
              <button class="buy-button" @click.stop="buyProduct(product)">去购买</button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="cartVisible || productDialogVisible"
      class="overlay"
      @click="closeOverlay"
    ></view>

    <!-- 商品对话框 -->
    <view
      v-if="productDialogVisible"
      class="product-dialog"
    >
      <view class="dialog-content" @click.stop>
        <view class="dialog-image">
          <image :src="currentProduct.productImage || currentProduct.image" mode="aspectFill"></image>
          <view class="close-button" @click="closeProductDialog">
            <text class="iconfont icon-guanbi"></text>
          </view>
        </view>
        <view class="dialog-info">
          <text class="dialog-title">{{ currentProduct.productName || currentProduct.name }}</text>
          <text class="dialog-desc">{{ currentProduct.productInfo || currentProduct.description }}</text>
          <view class="dialog-price-info">
            <text class="dialog-price">¥{{ currentProduct.productPrice || currentProduct.price }}</text>
            <text v-if="currentProduct.originalPrice && currentProduct.originalPrice > (currentProduct.productPrice || currentProduct.price)"
                  class="dialog-original-price">¥{{ currentProduct.originalPrice }}</text>
          </view>
          <view class="dialog-stats">
            <text class="dialog-stock">库存: {{ currentProduct.stock || '充足' }}</text>
            <text class="dialog-sales">销量: {{ currentProduct.sales || 0 }}</text>
          </view>
          <button class="dialog-buy-button" @click="buyProduct(currentProduct)" :disabled="currentProduct.stock <= 0">
            <text class="iconfont icon-gouwuche"></text>
            {{ currentProduct.stock <= 0 ? '库存不足' : '立即购买' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

import store from '../../store';
import {
  toLogin,
  checkLogin
} from '@/libs/login';
import {
  getRoomInfo,
  getRoomProducts,
  getProductDetail,
  getRecentMessages
} from '@/api/live.js';

import {
	getProductslist
} from '@/api/store.js';

export default {
  name: 'LiveRoom',
  data() {
    return {
      roomId: '112233',
  	  player: null,

      // 直播状态管理
      isLiving: false, // 是否正在直播
      isInitialized: false, // 是否已初始化

      // 直播数据
      liveData: {
        title: '加载中...',
        avatar: '',
        description: '正在加载直播间信息...',
        status: 0, // 默认未开播状态
        startTime: null,
        endTime: null
      },

      // 观众数据
      viewersCount: 0,

      // 消息数据
      messages: [],
      newMessage: '',
      scrollTop: 0,
      scrollIntoView: '',

      // 购物车和商品数据
      cartVisible: false,
      products: [], // 从服务端获取
      productsLoading: false, // 商品加载状态

      // 商品对话框
      productDialogVisible: false,
      currentProduct: null,

      // WebSocket相关
      websocket: null,
      isConnected: false,
      isAuthenticated: false,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatTimer: null,
      authTimeout: null,

      // 定时器
      messageTimer: null,
      viewerTimer: null,
      productPushTimer: null,

    }
  },
  computed: {
    displayMessages() {
      // 只显示最新的8条消息
      return this.messages.slice(-8);
    }
  },
  methods: {
    // ==================== 数据加载方法 ====================

    // 加载直播间信息
    async loadRoomInfo() {
      try {
        const response = await getRoomInfo(this.roomId);
        if (response.code === 200 && response.data) {
          const roomInfo = response.data.roomInfo;
          this.liveData = {
            title: roomInfo.title || '直播间',
            description: roomInfo.description || '直播',
            avatar: roomInfo.cover || '',
            cover: roomInfo.cover || '',
            status: roomInfo.status || 0, // 默认未开播
            startTime: roomInfo.startTime,
            endTime: roomInfo.endTime
          };

          this.initializePlayer();

          // 更新直播状态
          this.isLiving = this.liveData.status === 1;

          console.log('直播间信息加载成功:', JSON.stringify(this.liveData));
          console.log('直播状态:', this.isLiving ? '直播中' : '未开播');

          return Promise.resolve();
        } else {
          console.error('加载直播间信息失败:', response);
          uni.showToast({
            title: '加载直播间信息失败',
            icon: 'none'
          });
          return Promise.reject(new Error('加载直播间信息失败'));
        }
      } catch (error) {
        console.error('加载直播间信息异常:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        return Promise.reject(error);
      }
    },

    // 加载商品列表
    async loadProducts() {
      this.productsLoading = true;
      try {
        const allProductsResponse = await getProductslist({
			roomId: this.roomId,
			page: 1,
			limit: 200,
		});
        // 处理所有商品
        if (allProductsResponse.code === 200) {
          this.products = allProductsResponse.data.list || [];
        }
		
        console.log('商品数据加载完成:', {
          total: this.products.length
        });

      } catch (error) {
        console.error('加载商品列表异常:', error);
        uni.showToast({
          title: '加载商品失败',
          icon: 'none'
        });
      } finally {
        this.productsLoading = false;
      }
    },

    // 刷新商品数据
    refreshProducts() {
      this.loadProducts();
    },

    // ==================== 直播功能初始化 ====================

    // 初始化直播功能模块
    initializeLiveFeatures() {
      if (this.isInitialized) {
        console.log('直播功能已初始化，跳过重复初始化');
        return;
      }

      console.log('开始初始化直播功能模块...');

      try {
        // 1. 连接WebSocket
        if (!this.isConnected) {
          this.connectWebSocket();
        }

        // 2. 初始化播放器
        this.startPlay();

        // 3. 加载商品列表
        this.loadProducts();

        // 4. 加载历史消息
        this.loadHistoryMessages();

        this.isInitialized = true;
        console.log('直播功能模块初始化完成');
      } catch (error) {
        console.error('初始化直播功能失败:', error);
        uni.showToast({
          title: '初始化失败，请重试',
          icon: 'none'
        });
      }
    },

    // 初始化播放器
    initializePlayer() {
      // 这里可以添加播放器初始化逻辑
      console.log('初始化播放器...');
      if (!this.player) {
        const container = document.getElementById('tcView');
        const video = document.createElement('video');
        video.id = 'player-container-id';
        video.width = container.clientWidth;
        video.height = container.clientHeight;
        video.setAttribute('preload', 'auto');
        video.setAttribute('playsinline', 'true');
        video.setAttribute('webkit-playsinline', 'true');
        container.appendChild(video);
        this.player = TCPlayer('player-container-id', {
          // sources: ['https://live.aimaibumai.com/live/112233.flv'],
          muted: false,
          autoplay: false,
          bigPlayButton: true,
          poster: this.liveData.cover,
          controlBar: {
            playToggle: false,
            progressControl: false,
            volumePanel: false,
            currentTimeDisplay: false,
            durationDisplay: false,
            timeDivider: false,
            playbackRateMenuButton: false,
            fullscreenToggle: false,
            QualitySwitcherMenuButton: false
          }
        });
      }
    },

    startPlay() {
      this.player.src('https://live.aimaibumai.com/live/112233.flv')
      this.player.play()
    },

    // 清理直播功能资源
    cleanupLiveFeatures() {
      console.log('开始清理直播功能资源...');

      try {
        // 1. 断开WebSocket连接
        this.disconnectWebSocket();

        // 2. 关闭播放器
        this.destroyPlayer();

        // 3. 清理状态
        this.isInitialized = false;
        this.isLiving = false;

        console.log('直播功能资源清理完成');
      } catch (error) {
        console.error('清理资源失败:', error);
      }
    },

    // 关闭播放器
    destroyPlayer() {
      if (this.player) {
        console.log('关闭播放器...');
        try {
          // TODO: 根据实际播放器SDK添加销毁代码
          // this.player.destroy();
          this.player = null;
        } catch (error) {
          console.error('关闭播放器失败:', error);
        }
      }
    },

    // 处理下播状态
    handleLiveEnd() {
      console.log('收到下播消息，切换到下播状态');

      // 更新状态
      this.isLiving = false;
      this.liveData.status = 2; // 2表示已结束

      // 清理资源
      this.cleanupLiveFeatures();

      // 显示下播提示
      uni.showToast({
        title: '直播已结束',
        icon: 'none',
        duration: 3000
      });

      // 添加系统消息
      this.addMessageToChat({
        username: '系统通知',
        content: '直播已结束，感谢观看！',
        avatar: '',
        isNew: true,
        msgType: 2
      });
    },

    // 获取商品详情
    async getProductDetail(productId) {
      try {
        const response = await getProductDetail(this.roomId, productId);
        if (response.status === 200) {
          return response.data;
        } else {
          console.error('获取商品详情失败:', response);
          return null;
        }
      } catch (error) {
        console.error('获取商品详情异常:', error);
        uni.showToast({
          title: '获取商品详情失败',
          icon: 'none'
        });
        return null;
      }
    },

    // 加载历史消息
    async loadHistoryMessages() {
      try {
        const response = await getRecentMessages(this.roomId, 20);
        if (response.status === 200) {
          const messages = response.data || [];
          // 将历史消息添加到消息列表
          messages.forEach(msg => {
            this.addMessageToChat({
              username: msg.username || '用户',
              content: msg.content,
              avatar: msg.avatar || '',
              isNew: false,
              msgType: msg.msgType
            });
          });
          console.log('历史消息加载完成:', messages.length);
        }
      } catch (error) {
        console.error('加载历史消息异常:', error);
      }
    },

    // 发送消息
    sendMessage() {
      if (!this.newMessage.trim()) return;

      if (!this.isConnected || !this.isAuthenticated) {
        uni.showToast({
          title: '连接已断开，请稍后重试',
          icon: 'none'
        });
        return;
      }

      // 构建消息对象
      const messageData = {
        roomId: this.roomId,
        msgType: 1002, // 文本消息
        content: this.newMessage.trim()
      };

      // 通过WebSocket发送消息
      this.sendWebSocketMessage(messageData);

      // 清空输入框
      this.newMessage = '';
    },

    // ==================== WebSocket相关方法 ====================

    // 连接WebSocket
    connectWebSocket() {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        return;
      }

      const wsUrl = `wss://sy-ws.aimaibumai.com/chat/${this.roomId}`;

      try {
        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = (event) => {
          console.log('WebSocket连接成功');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // 发送AUTH消息
          this.sendAuthMessage();

          // 设置认证超时
          this.authTimeout = setTimeout(() => {
            if (!this.isAuthenticated) {
			  this.isAuthenticated = true
              console.warn('认证超时，关闭连接');
              this.disconnectWebSocket();
            }
          }, 10000);
        };

        this.websocket.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.websocket.onclose = (event) => {
          console.log('WebSocket连接关闭', event.code, event.reason);
          this.isConnected = false;
          this.isAuthenticated = false;

          // 清理认证超时
          if (this.authTimeout) {
            clearTimeout(this.authTimeout);
            this.authTimeout = null;
          }

          // 尝试重连
          this.attemptReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          this.isConnected = false;
          this.isAuthenticated = false;
        };

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        this.attemptReconnect();
      }
    },

    // 断开WebSocket连接
    disconnectWebSocket() {
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
      this.isConnected = false;
      this.isAuthenticated = false;

      // 清理定时器
      if (this.authTimeout) {
        clearTimeout(this.authTimeout);
        this.authTimeout = null;
      }
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },

    // 尝试重连
    attemptReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log('达到最大重连次数，停止重连');
        uni.showToast({
          title: '连接失败，请刷新页面',
          icon: 'none'
        });
        return;
      }

      this.reconnectAttempts++;
      console.log(`尝试重连 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      setTimeout(() => {
        this.connectWebSocket();
      }, this.reconnectInterval);
    },

    // 发送WebSocket消息
    sendWebSocketMessage(data) {
      if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
        console.warn('WebSocket未连接，无法发送消息');
        return false;
      }

      try {
        this.websocket.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
        return false;
      }
    },

    // 处理WebSocket消息
    handleWebSocketMessage(event) {
      try {
        const message = JSON.parse(event.data);
        console.log('收到WebSocket消息:', message);

        // 根据消息类型处理
        switch (message.msgType) {
		  case 2001: // AUTH_SUCCESS
		    this.handleAuthSuccess(message);
			break;
          case 2002: // 系统消息
            this.handleSystemMessage(message);
            break;
          case 2004: // 文本消息响应
            this.handleTextMessage(message);
            break;
          case 2005: // 商品推荐
            this.handleProductMessage(message);
            break;
		  case 2006: // 人数更新
			const count = parseInt(message.content);
			this.viewersCount = count;
			break;
          case 2003: // 欢迎消息
            this.handleWelcomeMessage(message);
            break;
          case 2010: // 下播消息
            this.handleLiveEnd();
            break;
          case -1: // 强制关闭
            this.handleForceCloseMessage(message);
            break;
          default:
            console.log('未知消息类型:', message.msgType);
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const lastIndex = this.displayMessages.length - 1;
        if (lastIndex >= 0) {
          this.scrollIntoView = 'message-' + lastIndex;
        }
      });
    },

    // 切换购物车显示
    toggleCart() {
      this.cartVisible = !this.cartVisible;
    },

    // 显示商品对话框
    async showProductDialog(product) {
      // 如果传入的是完整的商品信息，直接使用
      if (product.productName || product.name) {
        this.currentProduct = product;
        this.productDialogVisible = true;
      } else if (product.productId || product.id) {
        // 如果只有商品ID，需要获取详细信息
        const productId = product.productId || product.id;
        const detailProduct = await this.getProductDetail(productId);
        if (detailProduct) {
          this.currentProduct = detailProduct;
          this.productDialogVisible = true;
        } else {
          uni.showToast({
            title: '获取商品信息失败',
            icon: 'none'
          });
          return;
        }
      } else {
        console.error('无效的商品信息:', product);
        return;
      }

      console.log('显示商品对话框:', this.currentProduct);

      this.addMessageToChat({
        username: '系统通知',
        content: `正在讲解【${this.currentProduct.productName || this.currentProduct.name}】，点击查看详情！`,
        avatar: '',
        msgType: 4
      });

      this.scrollToBottom();

      // 30秒后自动关闭
      setTimeout(() => {
        this.closeProductDialog();
      }, 30000);
    },

    // 关闭商品对话框
    closeProductDialog() {
      this.productDialogVisible = false;
      this.currentProduct = null;
    },

    // 购买商品
    buyProduct(product) {
      // 检查库存
      if (product.stock <= 0) {
        uni.showToast({
          title: '商品库存不足',
          icon: 'none'
        });
        return;
      }

      // 使用新的商品数据结构
      const productName = product.storeName;
      const productId = product.id;

      // 这里可以跳转到商品详情页
      if (productId) {
        // 跳转到商品详情页
        uni.navigateTo({
          url: `/pages/goods_details/index?id=${productId}&source=live`
        });
      }
	  
      this.closeProductDialog();
      this.cartVisible = false;
    },

    // 关闭遮罩
    closeOverlay() {
      this.cartVisible = false;
      this.productDialogVisible = false;
    },

    // ==================== 消息处理方法 ====================
	
	handleAuthSuccess(message) {
		// 认证成功
		this.isAuthenticated = true;
		console.log('用户认证成功');
		
		// 清理认证超时
		if (this.authTimeout) {
		  clearTimeout(this.authTimeout);
		  this.authTimeout = null;
	    }
		
		// 开始心跳
		this.startHeartbeat();
	},

    // 处理系统消息
    handleSystemMessage(message) {
      if (message.content === 'AUTH_REQUIRED') {
        // 需要认证，发送token
        this.sendAuthMessage();
      } else {
        // 其他系统消息显示在聊天区
        this.addMessageToChat(message);
      }
    },

    // 处理文本消息
    handleTextMessage(message) {
      this.addMessageToChat(message);
    },

    // 处理商品推荐消息
    handleProductMessage(message) {
      try {
        const productInfo = JSON.parse(message.content);
		this.showProductDialog(productInfo);
      } catch (error) {
        console.error('解析商品信息失败:', error);
      }
    },

    // 处理欢迎消息
    handleWelcomeMessage(message) {
      this.addMessageToChat(message);
    },

    // 处理强制关闭消息
    handleForceCloseMessage(message) {
      console.warn('收到强制关闭消息:', message.content);
      uni.showModal({
        title: '连接已断开',
        content: message.content,
        showCancel: false,
        success: () => {
          this.disconnectWebSocket();
        }
      });
    },

    // 发送认证消息
    sendAuthMessage() {
      const token = store.state.app.token
      if (!token) {
        console.log('no token')
        toLogin();
        return;
      }

      const authData = {
		roomId:  this.roomId,
        msgType: 1001,
        content: token
      };

      this.sendWebSocketMessage(authData);
    },

    // 添加消息到聊天区
    addMessageToChat(message) {
      this.messages.push({
        username: message.username || '系统消息',
        content: message.content,
        avatar: message.avatar || '',
        msgType: message.msgType
      });

      // 限制消息数量
      if (this.messages.length > 100) {
        this.messages = this.messages.slice(-50);
      }

      this.scrollToBottom();

      // 移除新消息标记
      setTimeout(() => {
        this.messages.forEach(msg => msg.isNew = false);
      }, 500);
    },

    // 开始心跳
    startHeartbeat() {
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected && this.isAuthenticated) {
          this.sendWebSocketMessage({
            msgType: 1000, // 心跳消息
            content: 'ping'
          });
        }
      }, 30000); // 30秒心跳
    }
  },

  onLoad(options) {
    // 获取房间ID参数
    if (options && options.roomId) {
      this.roomId = options.roomId;
    }

    // 初始化滚动位置
    this.scrollToBottom();

    // 首先加载直播间信息，根据状态决定是否初始化其他功能
    this.loadRoomInfo().then(() => {
      // 根据直播状态决定是否初始化功能模块
      if (this.liveData.status === 1) {
        this.initializeLiveFeatures();
      } else {
        console.log('直播间未开播，不初始化交互功能');
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.live-container {
  position: relative;
  width: 100vw;
  height: 100vh; // calc(100vh - 100rpx); // 减去tabbar高度
  overflow: hidden;
  background-color: #000;
}

// 视频背景
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .live-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 顶部信息区
.top-info {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 60rpx 30rpx 30rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .live-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .info-text {
      display: flex;
      flex-direction: column;

      .live-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
        margin-bottom: 8rpx;
      }

      .live-status {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
      }
    }
  }

  .viewer-count {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10rpx);

    .iconfont {
      font-size: 28rpx;
      color: #fff;
      margin-right: 8rpx;
    }

    text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}



// 底部互动区域
.bottom-interaction {
  position: fixed;
  bottom: 0; // 100rpx; // tabbar高度
  left: 0;
  right: 0;
  z-index: 10;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  transition: transform 0.3s ease; // 键盘弹出时的过渡动画

  &.keyboard-active {
    bottom: 0; // 键盘弹出时贴底
  }

  .messages-container {
    height: 400rpx;
    margin-bottom: 20rpx;
    max-height: 400rpx;

    .messages-list {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      min-height: 240rpx;

      .message-item {
        margin-bottom: 16rpx;
        animation: fadeInUp 0.5s ease;

        &.fade-in {
          animation: fadeInUp 0.5s ease;
        }

        .message-content {
          background: rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(10rpx);
          border-radius: 20rpx;
          padding: 16rpx 20rpx;
          max-width: 80%;
          display: flex;
          align-items: center;

          .user-avatar {
            width: 48rpx;
            height: 48rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .username {
            font-size: 26rpx;
            color: #FFDD2D;
            font-weight: 500;
            margin-right: 8rpx;
            flex-shrink: 0;
          }

          .content {
            font-size: 26rpx;
            color: #fff;
            line-height: 1.4;
            word-break: break-all;
          }
        }
      }
    }
  }

  .input-area {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .input-wrapper {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(10rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      border-radius: 40rpx;
      height: 80rpx;

      .message-input {
        flex: 1;
        background: transparent;
        border: none;
        padding: 20rpx 28rpx;
        padding-right: 80rpx; // 为发送按钮留空间
        font-size: 24rpx;
        color: #fff;
        height: 100%;
        line-height: 40rpx;
        box-sizing: border-box;

        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .send-button {
        position: absolute;
        right: 8rpx;
        background: #E93323;
        color: #fff;
        border: none;
        border-radius: 50%;
        padding: 0;
        transition: all 0.3s ease;
        height: 64rpx;
        width: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        .iconfont {
          font-size: 28rpx;
        }

        &.disabled {
          background: rgba(255, 255, 255, 0.3);
          color: rgba(255, 255, 255, 0.5);
        }

        &:not(.disabled):active {
          transform: scale(0.95);
          background: #d42c1f;
        }
      }
    }

    // 购物车按钮
    .cart-button {
      width: 80rpx;
      height: 80rpx;
      background: rgba(233, 51, 35, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6rpx 20rpx rgba(233, 51, 35, 0.4);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .iconfont {
        font-size: 40rpx;
        color: #fff;
      }
    }
  }
}

// 商品抽屉
.product-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 600rpx;
  background: #232323;
  z-index: 30;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -8rpx 0 24rpx rgba(0, 0, 0, 0.3);

  &.show {
    transform: translateX(0);
  }

  .drawer-header {
    padding: 30rpx;
    border-bottom: 2rpx solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .drawer-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }

    .iconfont {
      font-size: 36rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .drawer-content {
    height: calc(100vh - 120rpx);
    padding: 20rpx;

    .product-item {
      background: #121212;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .product-image {
        position: relative;
        height: 300rpx;

        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .hot-tag {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          background: #E93323;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
        }
      }

      .product-info {
        padding: 24rpx;

        .product-name {
          font-size: 28rpx;
          font-weight: 500;
          color: #fff;
          margin-bottom: 8rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-desc {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 16rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .product-price {
            font-size: 32rpx;
            font-weight: bold;
            color: #E93323;
          }

          .buy-button {
            background: rgba(233, 51, 35, 0.9);
            color: #fff;
            border: none;
            border-radius: 50rpx;
            padding: 16rpx 24rpx;
            font-size: 24rpx;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
              background: #d42c1f;
            }
          }
        }
      }
    }
  }
}

// 遮罩层
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 20;
  transition: opacity 0.3s ease;
}

// 商品对话框
.product-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 40;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;

  .dialog-content {
    background: #232323;
    border-radius: 24rpx;
    overflow: hidden;
    width: 100%;
    max-width: 600rpx;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.6);
    animation: scaleIn 0.3s ease;

    .dialog-image {
      position: relative;
      height: 400rpx;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .close-button {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 50rpx; // 小一号
        height: 50rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);

        .iconfont {
          font-size: 26rpx; // 图标小一号
          color: #fff;
        }
      }
    }

    .dialog-info {
      padding: 35rpx; // 小一号

      .dialog-title {
        font-size: 30rpx; // 小一号
        font-weight: bold;
        color: #fff;
        margin-bottom: 14rpx;
        display: block;
        line-height: 1.4;
      }

      .dialog-price {
        font-size: 40rpx; // 小一号
        font-weight: bold;
        color: #E93323;
        margin-bottom: 28rpx;
        display: block;
      }

      .dialog-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 35rpx;

        .sold-count,
        .stock-count {
          font-size: 22rpx; // 小一号
          color: #999;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            width: 6rpx; // 小一号
            height: 6rpx;
            background: #FFDD2D;
            border-radius: 50%;
            margin-right: 10rpx;
          }
        }
      }

      .dialog-buy-button {
        width: 100%;
        background: #E93323;
        color: #fff;
        border: none;
        border-radius: 14rpx;
        padding: 28rpx; // 小一号
        font-size: 28rpx; // 小一号
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .iconfont {
          font-size: 28rpx; // 图标小一号
          margin-right: 14rpx;
        }

        &:active {
          transform: scale(0.98);
          background: #d42c1f;
        }
      }
    }
  }
}

// 动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 未开播状态样式 */
.not-live-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 100;
}

.not-live-content {
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin: 0 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.not-live-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.not-live-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.not-live-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.room-desc {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.desc-text {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}
</style>