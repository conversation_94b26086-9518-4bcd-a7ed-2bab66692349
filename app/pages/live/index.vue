<template>
  <view class="live-container">
    <!-- 直播视频背景 -->
    <view id="tcView" ref="tcView" class="video-background">
    </view>

    <!-- 顶部信息区 -->
    <view class="top-info">
      <view class="live-info">
        <view class="avatar">
          <image :src="liveData.avatar" mode="aspectFill"></image>
        </view>
        <view class="info-text">
          <text class="live-title">{{ liveData.title }}</text>
          <text class="live-status">正在直播 · {{ liveData.category }}</text>
        </view>
      </view>
      <view class="viewer-count">
        <text class="iconfont icon-yonghu"></text>
        <text>{{ viewersCount }}</text>
      </view>
    </view>

    <!-- 底部互动区域 -->
    <view class="bottom-interaction" >
      <!-- 留言滚动区 -->
      <scroll-view
        class="messages-container"
        scroll-y
        :scroll-top="scrollTop"
        scroll-with-animation
        :scroll-into-view="scrollIntoView"
      >
        <view class="messages-list">
          <view
            v-for="(message, index) in displayMessages"
            :key="index"
            :id="'message-' + index"
            class="message-item"
            :class="{ 'fade-in': message.isNew }"
          >
            <view class="message-content">
              <image class="user-avatar" :src="message.avatar" mode="aspectFill"></image>
              <text class="username">{{ message.username }}:</text>
              <text class="content">{{ message.content }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 输入框区域 -->
      <view class="input-area">
        <view class="input-wrapper">
          <input
            v-model="newMessage"
            class="message-input"
            placeholder="说点什么..."
            @confirm="sendMessage"
            confirm-type="send"
          />
          <button
            class="send-button"
            :class="{ 'disabled': !isConnected }"
            @click="sendMessage"
            :disabled="!isConnected"
          >
            <text class="iconfont icon-fasong"></text>
          </button>
        </view>

        <!-- 购物车按钮 -->
        <view class="cart-button" @click="toggleCart">
          <text class="iconfont icon-gouwuche"></text>
        </view>
      </view>
    </view>

    <!-- 商品抽屉 -->
    <view
      class="product-drawer"
      :class="{ 'show': cartVisible }"
    >
      <view class="drawer-header">
        <text class="drawer-title">直播间商品</text>
        <text class="iconfont icon-guanbi" @click="toggleCart"></text>
      </view>
      <scroll-view class="drawer-content" scroll-y="true">
        <view v-if="productsLoading" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>
        <view v-else-if="products.length === 0" class="empty-container">
          <text class="empty-text">暂无商品</text>
        </view>
        <view
          v-else
          v-for="(product, index) in products"
          :key="product.id || index"
          class="product-item"
          @click="showProductDialog(product)"
        >
          <view class="product-image">
            <image :src="product.productImage" mode="aspectFill"></image>
            <view v-if="product.isHot" class="hot-tag">热卖</view>
            <view v-if="product.isRecommend" class="recommend-tag">推荐</view>
            <view v-if="product.isExplaining" class="explaining-tag">讲解中</view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.productName }}</text>
            <text class="product-desc">{{ product.productInfo || '暂无描述' }}</text>
            <view class="product-price-info">
              <text class="product-price">¥{{ product.productPrice }}</text>
              <text v-if="product.originalPrice && product.originalPrice > product.productPrice"
                    class="original-price">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-bottom">
              <view class="product-stats">
                <text class="stock-text">库存: {{ product.stock }}</text>
                <text class="sales-text">销量: {{ product.sales }}</text>
              </view>
              <button class="buy-button" @click.stop="buyProduct(product)">去购买</button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="cartVisible || productDialogVisible"
      class="overlay"
      @click="closeOverlay"
    ></view>

    <!-- 商品对话框 -->
    <view
      v-if="productDialogVisible"
      class="product-dialog"
    >
      <view class="dialog-content" @click.stop>
        <view class="dialog-image">
          <image :src="currentProduct.productImage || currentProduct.image" mode="aspectFill"></image>
          <view class="close-button" @click="closeProductDialog">
            <text class="iconfont icon-guanbi"></text>
          </view>
        </view>
        <view class="dialog-info">
          <text class="dialog-title">{{ currentProduct.productName || currentProduct.name }}</text>
          <text class="dialog-desc">{{ currentProduct.productInfo || currentProduct.description }}</text>
          <view class="dialog-price-info">
            <text class="dialog-price">¥{{ currentProduct.productPrice || currentProduct.price }}</text>
            <text v-if="currentProduct.originalPrice && currentProduct.originalPrice > (currentProduct.productPrice || currentProduct.price)"
                  class="dialog-original-price">¥{{ currentProduct.originalPrice }}</text>
          </view>
          <view class="dialog-stats">
            <text class="dialog-stock">库存: {{ currentProduct.stock || '充足' }}</text>
            <text class="dialog-sales">销量: {{ currentProduct.sales || 0 }}</text>
          </view>
          <button class="dialog-buy-button" @click="buyProduct(currentProduct)" :disabled="currentProduct.stock <= 0">
            <text class="iconfont icon-gouwuche"></text>
            {{ currentProduct.stock <= 0 ? '库存不足' : '立即购买' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

import store from '../../store';
import {
  toLogin,
  checkLogin
} from '../../libs/login';

export default {
  name: 'LiveRoom',
  data() {
    return {
      roomId: 'room_001', // 从页面参数获取或默认值
  	  player: null,
      // 直播数据
      liveData: {
        title: '加载中...',
        category: '直播',
        avatar: '',
        description: '正在加载直播间信息...',
        roomName: '',
        roomDesc: '',
        anchorName: '',
        anchorAvatar: '',
        status: 1,
        startTime: null,
        endTime: null
      },
      videoSrc: 'https://assets.mixkit.co/videos/preview/mixkit-woman-dancing-at-a-nightclub-4340-large.mp4',

      // 观众数据
      viewersCount: 12345,

      // 消息数据
      messages: [
        { username: '用户**', content: '111有优惠券吗？', avatar: 'https://picsum.photos/200/200?random=2', isNew: false },
        { username: '用户**', content: '已下单，等着收货', avatar: 'https://picsum.photos/200/200?random=3', isNew: false }
      ],
      newMessage: '', 然后
      scrollTop: 0,
      scrollIntoView: '',

      // 购物车和商品数据
      cartVisible: false,
      products: [], // 从服务端获取
      recommendProducts: [], // 推荐商品
      hotProducts: [], // 热卖商品
      explainingProducts: [], // 正在讲解的商品
      productsLoading: false, // 商品加载状态

      // 商品对话框
      productDialogVisible: false,
      currentProduct: null,

      // WebSocket相关
      websocket: null,
      isConnected: false,
      isAuthenticated: false,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatTimer: null,
      authTimeout: null,

      // 定时器
      messageTimer: null,
      viewerTimer: null,
      productPushTimer: null,

    }
  },
  computed: {
    displayMessages() {
      // 只显示最新的8条消息
      return this.messages.slice(-8);
    }
  },
  methods: {
    // ==================== 数据加载方法 ====================

    // 加载直播间信息
    async loadRoomInfo() {
      try {
        const response = await uni.request({
          url: `${this.$baseUrl}/api/front/live/getRoomInfo`,
          method: 'GET',
          data: {
            roomId: this.roomId
          },
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });

        if (response.data && response.data.status === 200) {
          const roomInfo = response.data.data.roomInfo;
          this.liveData = {
            title: roomInfo.roomName || '直播间',
            category: roomInfo.category || '直播',
            avatar: roomInfo.anchorAvatar || '',
            description: roomInfo.roomDesc || '',
            roomName: roomInfo.roomName || '',
            roomDesc: roomInfo.roomDesc || '',
            anchorName: roomInfo.anchorName || '',
            anchorAvatar: roomInfo.anchorAvatar || '',
            status: roomInfo.status || 1,
            startTime: roomInfo.startTime,
            endTime: roomInfo.endTime
          };

          console.log('直播间信息加载成功:', this.liveData);
        } else {
          console.error('加载直播间信息失败:', response.data);
          uni.showToast({
            title: '加载直播间信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载直播间信息异常:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    },

    // 加载商品列表
    async loadProducts() {
      this.productsLoading = true;
      try {
        // 加载所有商品
        const allProductsResponse = await uni.request({
          url: `${this.$baseUrl}/api/front/live/products/${this.roomId}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });

        if (allProductsResponse.data && allProductsResponse.data.status === 200) {
          this.products = allProductsResponse.data.data || [];
        }

        // 加载推荐商品
        const recommendResponse = await uni.request({
          url: `${this.$baseUrl}/api/front/live/products/${this.roomId}/recommend`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });

        if (recommendResponse.data && recommendResponse.data.status === 200) {
          this.recommendProducts = recommendResponse.data.data || [];
        }

        // 加载热卖商品
        const hotResponse = await uni.request({
          url: `${this.$baseUrl}/api/front/live/products/${this.roomId}/hot`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });

        if (hotResponse.data && hotResponse.data.status === 200) {
          this.hotProducts = hotResponse.data.data || [];
        }

        // 加载正在讲解的商品
        const explainingResponse = await uni.request({
          url: `${this.$baseUrl}/api/front/live/products/${this.roomId}/explaining`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });

        if (explainingResponse.data && explainingResponse.data.status === 200) {
          this.explainingProducts = explainingResponse.data.data || [];
        }

        console.log('商品数据加载完成:', {
          total: this.products.length,
          recommend: this.recommendProducts.length,
          hot: this.hotProducts.length,
          explaining: this.explainingProducts.length
        });

      } catch (error) {
        console.error('加载商品列表异常:', error);
        uni.showToast({
          title: '加载商品失败',
          icon: 'none'
        });
      } finally {
        this.productsLoading = false;
      }
    },

    // 刷新商品数据
    refreshProducts() {
      this.loadProducts();
    },

    // 发送消息
    sendMessage() {
      if (!this.newMessage.trim()) return;

      if (!this.isConnected || !this.isAuthenticated) {
        uni.showToast({
          title: '连接已断开，请稍后重试',
          icon: 'none'
        });
        return;
      }

      // 构建消息对象
      const messageData = {
        roomId: this.roomId,
        msgType: 1002, // 文本消息
        content: this.newMessage.trim()
      };

      // 通过WebSocket发送消息
      this.sendWebSocketMessage(messageData);

      // 清空输入框
      this.newMessage = '';
    },

    // ==================== WebSocket相关方法 ====================

    // 连接WebSocket
    connectWebSocket() {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        return;
      }

      const wsUrl = `ws://localhost:8083/chat/${this.roomId}`;

      try {
        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = (event) => {
          console.log('WebSocket连接成功');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // 发送AUTH消息
          this.sendAuthMessage();

          // 设置认证超时
          this.authTimeout = setTimeout(() => {
            if (!this.isAuthenticated) {
              console.warn('认证超时，关闭连接');
              this.disconnectWebSocket();
            }
          }, 10000);
        };

        this.websocket.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.websocket.onclose = (event) => {
          console.log('WebSocket连接关闭', event.code, event.reason);
          this.isConnected = false;
          this.isAuthenticated = false;

          // 清理认证超时
          if (this.authTimeout) {
            clearTimeout(this.authTimeout);
            this.authTimeout = null;
          }

          // 尝试重连
          this.attemptReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          this.isConnected = false;
          this.isAuthenticated = false;
        };

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        this.attemptReconnect();
      }
    },

    // 断开WebSocket连接
    disconnectWebSocket() {
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
      this.isConnected = false;
      this.isAuthenticated = false;

      // 清理定时器
      if (this.authTimeout) {
        clearTimeout(this.authTimeout);
        this.authTimeout = null;
      }
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },

    // 尝试重连
    attemptReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log('达到最大重连次数，停止重连');
        uni.showToast({
          title: '连接失败，请刷新页面',
          icon: 'none'
        });
        return;
      }

      this.reconnectAttempts++;
      console.log(`尝试重连 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      setTimeout(() => {
        this.connectWebSocket();
      }, this.reconnectInterval);
    },

    // 发送WebSocket消息
    sendWebSocketMessage(data) {
      if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
        console.warn('WebSocket未连接，无法发送消息');
        return false;
      }

      try {
        this.websocket.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
        return false;
      }
    },

    // 处理WebSocket消息
    handleWebSocketMessage(event) {
      try {
        const message = JSON.parse(event.data);
        console.log('收到WebSocket消息:', message);

        // 根据消息类型处理
        switch (message.msgType) {
		  case 2001: // AUTH_SUCCESS
		    this.handleAuthSuccess(message);
			break;
          case 2002: // 系统消息
            this.handleSystemMessage(message);
            break;
          case 2004: // 文本消息响应
            this.handleTextMessage(message);
            break;
          case 2005: // 商品推荐
            this.handleProductMessage(message);
            break;
          case 2003: // 欢迎消息
            this.handleWelcomeMessage(message);
            break;
          case -1: // 强制关闭
            this.handleForceCloseMessage(message);
            break;
          default:
            console.log('未知消息类型:', message.msgType);
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const lastIndex = this.displayMessages.length - 1;
        if (lastIndex >= 0) {
          this.scrollIntoView = 'message-' + lastIndex;
        }
      });
    },

    // 切换购物车显示
    toggleCart() {
      this.cartVisible = !this.cartVisible;
      if (this.cartVisible) {
        this.animateViewersCount();
      }
    },

    // 动画更新观众数
    animateViewersCount() {
      const originalCount = this.viewersCount;
      const maxChange = 50;
      const steps = 20;
      let step = 0;

      const timer = setInterval(() => {
        step++;
        if (step >= steps) {
          clearInterval(timer);
          this.viewersCount = originalCount + Math.floor(Math.random() * maxChange);
          return;
        }

        const change = Math.floor(Math.random() * 10) - 3;
        this.viewersCount += change;
      }, 100);
    },

    // 显示商品对话框
    showProductDialog(product) {
      this.currentProduct = product;
      this.productDialogVisible = true;

	  console.log('收到商品推荐:', product);
	  
	  this.addMessageToChat({
	    username: '系统通知',
	    content: `正在讲解【${product.name}】，点击查看详情！`,
	    avatar: '',
	    msgType: 4
	  });

      this.scrollToBottom();

      // 30秒后自动关闭
      setTimeout(() => {
        this.closeProductDialog();
      }, 30000);
    },

    // 关闭商品对话框
    closeProductDialog() {
      this.productDialogVisible = false;
      this.currentProduct = null;
    },

    // 购买商品
    buyProduct(product) {
      // 检查库存
      if (product.stock <= 0) {
        uni.showToast({
          title: '商品库存不足',
          icon: 'none'
        });
        return;
      }

      // 使用新的商品数据结构
      const productName = product.productName || product.name;
      const productId = product.productId || product.id;

      // 这里可以跳转到商品详情页或购买页面
      if (product.productUrl) {
        uni.navigateTo({
          url: product.productUrl
        });
      } else if (productId) {
        // 跳转到商品详情页
        uni.navigateTo({
          url: `/pages/goods_details/index?id=${productId}`
        });
      } else {
        uni.showToast({
          title: `已成功购买【${productName}】！`,
          icon: 'success',
          duration: 2000
        });
      }

      this.closeProductDialog();
      this.cartVisible = false;

      // 添加购买成功消息到聊天区
      this.addMessageToChat({
        username: '我',
        content: `我购买了【${productName}】`,
        avatar: '',
        isNew: true,
        msgType: 3
      });
    },

    // 关闭遮罩
    closeOverlay() {
      this.cartVisible = false;
      this.productDialogVisible = false;
    },

    // 随机推送商品
    // scheduleProductPush() {
    //   this.productPushTimer = setTimeout(() => {
    //     const randomProduct = this.products[Math.floor(Math.random() * this.products.length)];
    //     this.showProductDialog(randomProduct);

    //     // 继续下一次推送
    //     this.scheduleProductPush();
    //   }, 15000 + Math.random() * 30000); // 15-45秒之间随机推送
    // },

    // ==================== 消息处理方法 ====================
	
	handleAuthSuccess(message) {
		// 认证成功
		this.isAuthenticated = true;
		console.log('用户认证成功');
		
		// 清理认证超时
		if (this.authTimeout) {
		  clearTimeout(this.authTimeout);
		  this.authTimeout = null;
	    }
		
		// 开始心跳
		this.startHeartbeat();
	},

    // 处理系统消息
    handleSystemMessage(message) {
      if (message.content === 'AUTH_REQUIRED') {
        // 需要认证，发送token
        this.sendAuthMessage();
      } else if (message.content.startsWith('ONLINE_COUNT_UPDATE:')) {
        // 更新在线人数
        const count = parseInt(message.content.split(':')[1]);
        this.viewersCount = count;
      } else {
        // 其他系统消息显示在聊天区
        this.addMessageToChat(message);
      }
    },

    // 处理文本消息
    handleTextMessage(message) {
      this.addMessageToChat(message);
    },

    // 处理商品推荐消息
    handleProductMessage(message) {
      try {
        const productInfo = JSON.parse(message.content);
		this.showProductDialog(productInfo);
      } catch (error) {
        console.error('解析商品信息失败:', error);
      }
    },

    // 处理欢迎消息
    handleWelcomeMessage(message) {
      this.addMessageToChat(message);
    },

    // 处理强制关闭消息
    handleForceCloseMessage(message) {
      console.warn('收到强制关闭消息:', message.content);
      uni.showModal({
        title: '连接已断开',
        content: message.content,
        showCancel: false,
        success: () => {
          this.disconnectWebSocket();
        }
      });
    },

    // 发送认证消息
    sendAuthMessage() {
      const token =  'e972ba1c6bc84555a7fbb678846611d2'// store.state.app.token
      if (!token) {
        console.log('no token')
        toLogin();
        return;
      }

      const authData = {
        msgType: 1001,
        content: token
      };

      this.sendWebSocketMessage(authData);
    },

    // 添加消息到聊天区
    addMessageToChat(message) {
      this.messages.push({
        username: message.username || '系统消息',
        content: message.content,
        avatar: message.avatar || '',
        msgType: message.msgType
      });

      // 限制消息数量
      if (this.messages.length > 100) {
        this.messages = this.messages.slice(-50);
      }

      this.scrollToBottom();

      // 移除新消息标记
      setTimeout(() => {
        this.messages.forEach(msg => msg.isNew = false);
      }, 500);
    },

    // 开始心跳
    startHeartbeat() {
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected && this.isAuthenticated) {
          this.sendWebSocketMessage({
            msgType: 1000, // 心跳消息
            content: 'ping'
          });
        }
      }, 10000); // 30秒心跳
    },

    // 显示商品推荐
    showProductRecommendation(productInfo) {
     
    },

    // 清理所有定时器
    clearAllTimers() {
      if (this.messageTimer) {
        clearInterval(this.messageTimer);
        this.messageTimer = null;
      }
      if (this.viewerTimer) {
        clearInterval(this.viewerTimer);
        this.viewerTimer = null;
      }
      if (this.productPushTimer) {
        clearTimeout(this.productPushTimer);
        this.productPushTimer = null;
      }
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
      if (this.authTimeout) {
        clearTimeout(this.authTimeout);
        this.authTimeout = null;
      }
    }
  },

  onLoad(options) {
    // 获取房间ID参数
    if (options && options.roomId) {
      this.roomId = options.roomId;
    }

    // 初始化滚动位置
    this.scrollToBottom();

    // 加载直播间信息
    this.loadRoomInfo();

    // 加载商品列表
    this.loadProducts();

    // 连接WebSocket
    this.connectWebSocket();
  },

  onReady() {

    // 定时增加观众人数
    this.viewerTimer = setInterval(() => {
      this.viewersCount += Math.floor(Math.random() * 10) + 1;
    }, 5000);

    // 模拟用户持续发送消息
    // this.messageTimer = setInterval(() => {
    //   this.simulateUserMessage();
    // }, 5000 + Math.random() * 10000);

    // 模拟服务器推送商品消息
    // this.scheduleProductPush();


    // 开启直播
	if (!this.player) {
		const container = document.getElementById('tcView');
		const video = document.createElement('video');
		video.id = 'player-container-id';
		video.width = container.clientWidth;
		video.height = container.clientHeight;
		video.setAttribute('preload', 'auto');
		video.setAttribute('playsinline', 'true');
		video.setAttribute('webkit-playsinline', 'true');
		container.appendChild(video);
		
		this.player = TCPlayer('player-container-id', {
		  sources: ['https://live.aimaibumai.com/live/112233.flv'],
		  muted: true,
		  autoplay: true,
		  poster: 'https://img.tukuppt.com/ad_preview/00/10/88/5c993a1767f44.jpg!/fw/780',
		  controlBar: {
			  playToggle: true,
			  progressControl: false,
			  volumePanel: false,
			  currentTimeDisplay: false,
			  durationDisplay: false,
			  timeDivider: false,
			  playbackRateMenuButton: false,
			  fullscreenToggle: false,
			  QualitySwitcherMenuButton: false
		  }
		});
	}
	this.player.play()
	
	/*
	VePlayer.createLivePlayer({
		id: 'tcView',
		url: 'https://sylive.aimaibumai.com/live/112233.flv',
		autoplay: true,
		control: false,
		closeVideoClick: true,
		'h5-video-player-type': 'h5',
		logger: {
			appId: '788563'
		}
	})
	*/
    
  },

  onUnload() {
    // 断开WebSocket连接
    this.disconnectWebSocket();

    // 清理定时器
    this.clearAllTimers();
  }
}
</script>

<style lang="scss" scoped>
.live-container {
  position: relative;
  width: 100vw;
  height: 100vh; // calc(100vh - 100rpx); // 减去tabbar高度
  overflow: hidden;
  background-color: #000;
}

// 视频背景
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .live-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 顶部信息区
.top-info {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 60rpx 30rpx 30rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .live-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .info-text {
      display: flex;
      flex-direction: column;

      .live-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
        margin-bottom: 8rpx;
      }

      .live-status {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
      }
    }
  }

  .viewer-count {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10rpx);

    .iconfont {
      font-size: 28rpx;
      color: #fff;
      margin-right: 8rpx;
    }

    text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}



// 底部互动区域
.bottom-interaction {
  position: fixed;
  bottom: 0; // 100rpx; // tabbar高度
  left: 0;
  right: 0;
  z-index: 10;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  transition: transform 0.3s ease; // 键盘弹出时的过渡动画

  &.keyboard-active {
    bottom: 0; // 键盘弹出时贴底
  }

  .messages-container {
    height: 400rpx;
    margin-bottom: 20rpx;
    max-height: 400rpx;

    .messages-list {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      min-height: 240rpx;

      .message-item {
        margin-bottom: 16rpx;
        animation: fadeInUp 0.5s ease;

        &.fade-in {
          animation: fadeInUp 0.5s ease;
        }

        .message-content {
          background: rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(10rpx);
          border-radius: 20rpx;
          padding: 16rpx 20rpx;
          max-width: 80%;
          display: flex;
          align-items: center;

          .user-avatar {
            width: 48rpx;
            height: 48rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .username {
            font-size: 26rpx;
            color: #FFDD2D;
            font-weight: 500;
            margin-right: 8rpx;
            flex-shrink: 0;
          }

          .content {
            font-size: 26rpx;
            color: #fff;
            line-height: 1.4;
            word-break: break-all;
          }
        }
      }
    }
  }

  .input-area {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .input-wrapper {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(10rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      border-radius: 40rpx;
      height: 80rpx;

      .message-input {
        flex: 1;
        background: transparent;
        border: none;
        padding: 20rpx 28rpx;
        padding-right: 80rpx; // 为发送按钮留空间
        font-size: 24rpx;
        color: #fff;
        height: 100%;
        line-height: 40rpx;
        box-sizing: border-box;

        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .send-button {
        position: absolute;
        right: 8rpx;
        background: #E93323;
        color: #fff;
        border: none;
        border-radius: 50%;
        padding: 0;
        transition: all 0.3s ease;
        height: 64rpx;
        width: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        .iconfont {
          font-size: 28rpx;
        }

        &.disabled {
          background: rgba(255, 255, 255, 0.3);
          color: rgba(255, 255, 255, 0.5);
        }

        &:not(.disabled):active {
          transform: scale(0.95);
          background: #d42c1f;
        }
      }
    }

    // 购物车按钮
    .cart-button {
      width: 80rpx;
      height: 80rpx;
      background: rgba(233, 51, 35, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6rpx 20rpx rgba(233, 51, 35, 0.4);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .iconfont {
        font-size: 40rpx;
        color: #fff;
      }
    }
  }
}

// 商品抽屉
.product-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 600rpx;
  background: #232323;
  z-index: 30;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -8rpx 0 24rpx rgba(0, 0, 0, 0.3);

  &.show {
    transform: translateX(0);
  }

  .drawer-header {
    padding: 30rpx;
    border-bottom: 2rpx solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .drawer-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }

    .iconfont {
      font-size: 36rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .drawer-content {
    height: calc(100vh - 120rpx);
    padding: 20rpx;

    .product-item {
      background: #121212;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .product-image {
        position: relative;
        height: 300rpx;

        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .hot-tag {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          background: #E93323;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
        }

        .recommend-tag {
          position: absolute;
          top: 16rpx;
          left: 16rpx;
          background: #FF6B35;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
        }

        .explaining-tag {
          position: absolute;
          bottom: 16rpx;
          left: 16rpx;
          background: #1890FF;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          animation: blink 1s infinite;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0.5; }
        }
      }

      .product-info {
        padding: 24rpx;

        .product-name {
          font-size: 28rpx;
          font-weight: 500;
          color: #fff;
          margin-bottom: 8rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-desc {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 16rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .product-price {
            font-size: 32rpx;
            font-weight: bold;
            color: #E93323;
          }
        }

        .product-price-info {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          .product-price {
            font-size: 32rpx;
            font-weight: bold;
            color: #E93323;
            margin-right: 16rpx;
          }

          .original-price {
            font-size: 24rpx;
            color: #999;
            text-decoration: line-through;
          }
        }

        .product-stats {
          display: flex;
          gap: 24rpx;

          .stock-text, .sales-text {
            font-size: 24rpx;
            color: #666;
          }
        }

        .loading-container, .empty-container {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200rpx;

          .loading-text, .empty-text {
            font-size: 28rpx;
            color: #999;
          }

          .buy-button {
            background: rgba(233, 51, 35, 0.9);
            color: #fff;
            border: none;
            border-radius: 50rpx;
            padding: 16rpx 24rpx;
            font-size: 24rpx;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
              background: #d42c1f;
            }
          }
        }
      }
    }
  }
}

// 遮罩层
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 20;
  transition: opacity 0.3s ease;
}

// 商品对话框
.product-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 40;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;

  .dialog-content {
    background: #232323;
    border-radius: 24rpx;
    overflow: hidden;
    width: 100%;
    max-width: 600rpx;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.6);
    animation: scaleIn 0.3s ease;

    .dialog-image {
      position: relative;
      height: 400rpx;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .close-button {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 50rpx; // 小一号
        height: 50rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);

        .iconfont {
          font-size: 26rpx; // 图标小一号
          color: #fff;
        }
      }
    }

    .dialog-info {
      padding: 35rpx; // 小一号

      .dialog-title {
        font-size: 30rpx; // 小一号
        font-weight: bold;
        color: #fff;
        margin-bottom: 14rpx;
        display: block;
        line-height: 1.4;
      }

      .dialog-desc {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 20rpx;
        line-height: 1.4;
      }

      .dialog-price-info {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
      }

      .dialog-price {
        font-size: 40rpx;
        font-weight: bold;
        color: #E93323;
        margin-right: 20rpx;
      }

      .dialog-original-price {
        font-size: 28rpx;
        color: #999;
        text-decoration: line-through;
      }

      .dialog-stats {
        display: flex;
        gap: 30rpx;
        margin-bottom: 20rpx;

        .dialog-stock, .dialog-sales {
          font-size: 26rpx;
          color: #666;
        }
      }

      .dialog-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-bottom: 20rpx;

        .dialog-tag {
          background: #f5f5f5;
          color: #666;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
        }
      }

      .dialog-discount {
        font-size: 26rpx;
        color: #FF6B35;
        background: #FFF2E8;
        padding: 12rpx 20rpx;
        border-radius: 8rpx;
        margin-bottom: 28rpx;
        display: block;
      }

      .dialog-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 35rpx;

        .sold-count,
        .stock-count {
          font-size: 22rpx; // 小一号
          color: #999;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            width: 6rpx; // 小一号
            height: 6rpx;
            background: #FFDD2D;
            border-radius: 50%;
            margin-right: 10rpx;
          }
        }
      }

      .dialog-buy-button {
        width: 100%;
        background: #E93323;
        color: #fff;
        border: none;
        border-radius: 14rpx;
        padding: 28rpx; // 小一号
        font-size: 28rpx; // 小一号
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .iconfont {
          font-size: 28rpx; // 图标小一号
          margin-right: 14rpx;
        }

        &:active {
          transform: scale(0.98);
          background: #d42c1f;
        }

        &:disabled {
          background: #ccc;
          color: #999;
          transform: none;
        }
      }
    }
  }
}

// 动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>