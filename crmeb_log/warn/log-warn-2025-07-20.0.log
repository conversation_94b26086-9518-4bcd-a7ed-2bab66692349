{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 16:53:20.099",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 18:26:19.314",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 19:05:10.594",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 19:24:53.498",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 19:36:44.475",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 19:46:39.167",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 21:09:41.314",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 22:41:24.437",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 22:41:41.977",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 22:42:53.710",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 22:45:13.860",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-20 23:49:45.146",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
