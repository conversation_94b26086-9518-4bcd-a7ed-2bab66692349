{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:01:30.660",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP POST https://sms.crmeb.net/api/sms_v2/send" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:01:30.664",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[text/plain, application/json, application/*+json, text/html, */*]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:01:30.664",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Writing [{phone=[18501027178], temp_id=[440396], param[pay_price]=[1.00], param[order_id]=[order90713175302725424912999]}] as "application/x-www-form-urlencoded"" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:01:30.989",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:01:30.989",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [java.lang.String] as "application/json"" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:06:33.468",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP POST https://sms.crmeb.net/api/sms_v2/send" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:06:33.476",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[text/plain, application/json, application/*+json, text/html, */*]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:06:33.480",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Writing [{phone=[18501027178], temp_id=[440396], param[pay_price]=[1.00], param[order_id]=[order67868175302757025223261]}] as "application/x-www-form-urlencoded"" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:06:33.936",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:06:33.937",
                    "level": "DEBUG",
                    "thread": "crmeb-scheduled-task-pool-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [java.lang.String] as "application/json"" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:38.456",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:38.456",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:38.520",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:38.520",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:39.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:39.260",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@48cdbcf4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:39.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:41.338",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:41.338",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5fc95a27]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:22:41.341",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:36.221",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:36.227",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:36.997",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=b8ce04a3d57a2deaa1d3dadccb9962d6, code=fG1G)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.468",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.488",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.489",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5e135f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.505",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 验证码不正确]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.506",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=1753028618", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.585",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.587",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@52a65b9b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:38.590",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:43.701",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:43.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:43.704",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=1c430b68f99eb6f93a4ae57cef4923b7, code=FMq8)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:45.660",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:45.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@439924f6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:45.665",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.039",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1753028625", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.039",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753028625", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.041",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.041",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@50b87d9c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.082",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.220",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753028626", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.221",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@258fcb51]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.545",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.565",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.565",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3536b62]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:46.612",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753028626", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753028626", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753028626", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753028626", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.182",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.182",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.232",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.232",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@11331fdb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.233",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.233",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3c8eb9e4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.233",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.234",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.505",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.505",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@515087ee]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.510",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.513",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.517",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4bedf31f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.517",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7fad29c7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:23:47.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.154",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029504", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.169",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.253",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.255",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6e9a8dc1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.263",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.455",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029505", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.459",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.541",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029504", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.551",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.554",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@fbf9d2f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.558",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.653",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.653",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4154da33]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.669",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.778",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029505", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.782",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@44733008]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:25.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.692",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.694",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.749",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.750",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.811",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.816",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@35add282]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.862",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7bcf7adf]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.863",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:26.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.116",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.117",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1e9fe309]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.117",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.117",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6ac7550f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.121",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.121",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.149",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.150",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.152",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.152",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@75cbece7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.154",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.176",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.177",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1b5df7cd]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.178",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.206",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.211",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.220",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029506", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.221",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.231",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.231",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3d7c883e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.233",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.239",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@a81dfda]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.253",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3a79369d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.255",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.322",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@62ce84a8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:38:27.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.252",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029548", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.258",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@28f2b4d4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.803",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029549", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:09.805",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@2464595d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.002",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.816",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029550", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.817",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029550", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.863",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753029550", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753029550", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.868",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.870",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.873",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.875",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@13aedc97]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.876",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.919",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.919",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6b578d51]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.920",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.921",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.921",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@2de524f5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.924",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.953",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.954",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@557589c8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:10.965",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:11.375",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:11.375",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@14941016]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:11.376",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:16.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029556", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:16.830",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:16.833",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:16.834",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@735bbed]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:16.835",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:17.341",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029556", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:17.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:17.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:17.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@26faa40a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:17.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.060",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.064",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.067",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029557", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753029557", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.074",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.078",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753029557", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.078",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.150",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029557", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.151",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6cf95eda]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.168",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.168",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6fcb94f5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.168",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.168",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@89a0ee]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.169",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.169",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.174",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.175",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3a775848]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.176",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.310",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.310",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5de7e84c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:18.310",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:33.794",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753029573", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:33.800",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:34.638",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:34.640",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6b85df4b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:34.650",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:51.974",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029591", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:51.981",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:51.985",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:51.987",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4f480941]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:51.987",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.331",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029592", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.334",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.433",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.434",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@52175133]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.984",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753029592", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:52.985",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029592", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.004",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.007",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029592", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.011",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5c03ac98]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.014",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.014",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.498",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.499",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.500",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4b21da1d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.500",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@427c4480]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.501",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:39:53.503",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.438",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029615", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.439",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.442",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.443",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7058e24d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.443",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.772",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029615", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:15.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.005",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.006",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6037abf2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.009",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029616", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.332",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.342",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753029616", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.345",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.369",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029616", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.371",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3585c538]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.401",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.401",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.401",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5ebac261]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.545",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@20ec14c9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:16.547",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.271",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a8ad54a2f1da49e68530ce669dec0ba3&temp=1753029631", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.274",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.281",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.282",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@49494c45]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.582",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753029632", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.584",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.717",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.717",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1bcc603e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:32.722",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.157",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753029632", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.165",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.167",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753029632", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.169",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.170",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753029632", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.171",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.192",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.194",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@58a31c80]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.197",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.197",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.197",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@17b90f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.201",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.214",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.214",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@420945f6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:33.215",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:45.916",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/room/start/112233", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:45.959",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#startLive(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:46.976",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:46.976",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@63cf6483]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:40:46.976",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:43:46.448",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753029826", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:43:46.453",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:43:46.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:43:46.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@19034a05]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:43:46.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:29.922",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753029869", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:29.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:29.992",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:29.993",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7b4af4c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:29.994",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:35.278",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753029875", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:35.283",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:35.561",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:35.562",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@26634a43]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:35.565",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:46.416",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/startExplain?roomId=112233&productId=1", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:46.421",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#startExplaining(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:46.766",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:46.767",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@22895421]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:46.767",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:47.071",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753029886", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:47.072",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:47.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:47.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@266d24a8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 00:44:47.297",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:10.563",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:10.563",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:10.579",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:10.579",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:11.377",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:11.378",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5213d93f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:11.387",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:12.868",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:12.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@5e0c3010]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:12.871",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.095",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.095",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.096",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.103",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.525",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.526",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6012f62d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:20.528",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:21.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:21.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3f4a9061]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:21.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:52.795",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:52.808",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:52.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=303e504d3c68b9b6945fa0a3e1d86427, code=80TC)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:53.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:53.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@496cc4fc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:53.912",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.055",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=32306251c455434ea936b54d83e7cfe0&temp=1753096733", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.060",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.061",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1753096733", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.064",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.067",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.067",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7224c93a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.069",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.374",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753096734", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.375",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.459",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.459",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3229797f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.461",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.546",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.546",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@49f245b6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.615",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753096734", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.871",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.887",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753096734", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.890",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4956d1ab]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:54.927",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@481c1cc1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.208",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753096734", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753096734", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.220",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.335",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.338",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.507",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.508",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@480fe70b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:55.510",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:56.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:56.161",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@8c38d86]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:56.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:57.530",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:57.530",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@540dfd79]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:18:57.531",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:00.991",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/room/list?page=1&limit=20&temp=1753096979", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:01.002",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getRoomList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:01.377",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:01.378",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@785c1a3e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:01.421",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:04.635",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753096984", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:04.637",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:04.891",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:04.891",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1b076abc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:23:04.896",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:52.453",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.040",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.admin.CrmebAdminApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.447",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.448",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.449",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.449",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:53.461",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@560cbf1a" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:11.335",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:11.338",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:11.339",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:12.999",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:15.945",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:15.946",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.107",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.108",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.109",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.110",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.110",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:16.110",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:39.257",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "313 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:40.705",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:40.727",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:40.890",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:40.935",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:48.864",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(38)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:48.964",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(38)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:48:54.062",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(36)-127.0.0.1",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:20.023",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753098556", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:20.064",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:21.078",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:21.109",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6aa8de7f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:21.165",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:35.346",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753098574", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:35.351",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:35.604",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753098574", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:35.605",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:36.312",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:36.313",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@64f32be8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:36.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:43.104",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:43.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6fe9b335]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:49:43.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.868",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.879",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.ServletInvocableHandlerMethod",
                    "message": "Could not resolve parameter [0] in public com.zbkj.common.response.CommonResult<java.lang.String> com.zbkj.admin.controller.LiveManageController.addProduct(java.lang.String,java.lang.Integer): Required String parameter 'roomId' is not present" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.895",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.906",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.907",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@33cb81e5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.915",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required String parameter 'roomId' is not present]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:50:50.915",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.050",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.051",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.ServletInvocableHandlerMethod",
                    "message": "Could not resolve parameter [0] in public com.zbkj.common.response.CommonResult<java.lang.String> com.zbkj.admin.controller.LiveManageController.addProduct(java.lang.String,java.lang.Integer): Required String parameter 'roomId' is not present" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.051",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.053",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.053",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@efb7423]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.054",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required String parameter 'roomId' is not present]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:51:08.054",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 21:18:40.621",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@560cbf1a, started on Mon Jul 21 19:47:53 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.463",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.641",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.admin.CrmebAdminApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.738",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.738",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.739",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.739",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:42.740",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@551a20d6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:47.338",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:47.339",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:47.339",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:47.843",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.073",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.074",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.129",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.130",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.131",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.131",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.132",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:49.133",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:59.462",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "316 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:00.616",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:00.636",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:00.780",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:00.816",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:05.187",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(4)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:05.345",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(4)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:07.607",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(5)-127.0.0.1",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.621",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=32306251c455434ea936b54d83e7cfe0&temp=1753112568", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.647",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.752",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.752",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@52e2c5ed]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.947",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753112569", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:49.948",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.062",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.062",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@53d59df0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.517",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753112570", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.518",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.519",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112570", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.520",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.571",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753112570", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.572",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.752",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.752",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.753",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@56832bef]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.753",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@639ea3fe]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.756",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:50.756",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:51.091",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:51.092",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4032ba3f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:51.100",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:57.833",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/remove?roomId=112233&productId=1", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:57.833",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#removeProduct(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.389",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.389",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@4a8c72f8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.389",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.863",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112578", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.901",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@13d82f16]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:42:58.905",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:02.701",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/remove?roomId=112233&productId=2", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:02.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#removeProduct(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:02.762",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:02.762",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1299f24d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:02.763",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:03.176",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112582", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:03.178",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:03.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:03.215",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@67c3ec06]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:03.232",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:11.681",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753112591", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:11.683",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:11.941",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753112591", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:11.946",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:13.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:13.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@e0dfb12]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:13.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:15.772",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:15.772",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@78de48fb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:15.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.016",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753112608", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.018",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.395",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753112608", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.396",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.414",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.414",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@2bb28d8f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:29.415",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:32.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:32.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1f0f9791]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:32.547",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:47.899",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:47.901",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(AddProductRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:47.928",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AddProductRequest(roomId=112233, productId=4, productName=null, productPrice=null, stock=null, sort= (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.136",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.137",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(AddProductRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.139",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AddProductRequest(roomId=112233, productId=10, productName=null, productPrice=null, stock=null, sort (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6b7cd478]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.191",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.363",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.364",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7ba1b3bc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.364",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:12.804",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/remove?roomId=112233&productId=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:12.804",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#removeProduct(String, Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:13.401",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:13.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@68e753d9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:13.403",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:14.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112653", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:14.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:14.440",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:14.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@230a94e9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:14.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.368",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753112663", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.369",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.381",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753112663", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.403",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.444",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.444",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@60344c99]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:23.446",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:24.358",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:24.358",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@722426b5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:24.360",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:33.948",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:33.948",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(AddProductRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:33.949",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AddProductRequest(roomId=112233, productId=10, productName=null, productPrice=null, stock=null, sort (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@6050515d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.507",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112674", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.508",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@36e471f5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:34.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:39.253",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112678", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:39.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:39.270",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:39.270",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7431d802]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:39.271",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:54.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753112694", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:54.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:54.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753112694", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:54.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:55.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:55.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7ac83f2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:55.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:58.453",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:58.453",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@40a5178e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:44:58.469",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.314",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.315",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(AddProductRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.315",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AddProductRequest(roomId=112233, productId=10, productName=null, productPrice=null, stock=null, sort (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.902",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.902",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1dbcb0a7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:04.902",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:05.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112704", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:05.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:05.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:05.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@3f6c7746]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:05.208",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.880",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/category/list/tree?status=-1&type=1&temp=1753112738", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.880",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=10&cateId=&keywords=&type=1&temp=1753112738", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.881",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.CategoryController#getListTree(Integer, Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.881",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.996",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.996",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@85e4b7e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:38.997",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:41.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:41.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@26220937]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:41.538",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.342",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/live/product/add", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#addProduct(AddProductRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.344",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AddProductRequest(roomId=112233, productId=10, productName=null, productPrice=null, stock=null, sort (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.449",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.449",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@7435a3ee]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.449",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112758", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.681",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.698",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.698",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@501cde08]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:45:58.699",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:46:31.204",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/live/product/list?roomId=112233&page=1&limit=20&temp=1753112791", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:46:31.211",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.LiveManageController#getProductList(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:46:31.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:46:31.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@57080378]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:46:31.229",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.066",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@551a20d6, started on Mon Jul 21 23:41:42 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.243",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.244",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.245",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.245",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.245",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.246",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.246",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.246",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.246",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.247",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.247",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:34.248",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.196",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.327",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.admin.CrmebAdminApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.392",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:48.394",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@71f67a79" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:52.014",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:52.014",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:52.015",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:52.458",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.521",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.522",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.560",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.561",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.561",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.561",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.562",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:53.562",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:02.243",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "316 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:03.128",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:03.149",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:03.270",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:03.300",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:07.055",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(4)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:07.097",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(4)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:52:09.657",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(3)-127.0.0.1",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:05.958",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@71f67a79, started on Mon Jul 21 23:51:48 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.000",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.000",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.000",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.000",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.000",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.001",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.001",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.001",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.001",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.001",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.002",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:06.002",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.645",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-service/target/classes/, file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.34/tomcat-embed-jasper-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.34/tomcat-annotations-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.34/tomcat-embed-el-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.34/tomcat-jsp-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.34/tomcat-el-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.34/tomcat-servlet-api-9.0.34.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.7.RELEASE/spring-boot-starter-jdbc-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.3/HikariCP-3.4.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.7.RELEASE/spring-boot-test-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.10/byte-buddy-1.10.10.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.7.RELEASE/spring-boot-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.7.RELEASE/spring-boot-starter-web-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.7.RELEASE/spring-boot-starter-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.7.RELEASE/spring-boot-starter-logging-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.7.RELEASE/spring-boot-starter-json-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.7.RELEASE/spring-boot-starter-tomcat-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.34/tomcat-embed-websocket-9.0.34.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.7.RELEASE/spring-boot-starter-validation-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.19.Final/hibernate-validator-6.0.19.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.7.RELEASE/spring-boot-starter-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.7.RELEASE/spring-boot-actuator-autoconfigure-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.7.RELEASE/spring-boot-actuator-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.7.RELEASE/spring-boot-starter-aop-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.6/joda-time-2.10.6.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.8/okhttp-3.14.8.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.7.RELEASE/spring-boot-starter-security-2.2.7.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.782",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.admin.CrmebAdminApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.861",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.862",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.862",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.862",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/Users/<USER>/workspace/sysd/crmlive/crmeb/crmeb-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:16.863",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4bff1903" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:21.353",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:21.354",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.7.RELEASE/spring-boot-2.2.7.RELEASE.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:21.354",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:21.876",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.205",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.206",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.258",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.259",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.259",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.260",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.260",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:23.260",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:33.560",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "316 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:35.058",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:35.079",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:35.220",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:35.246",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:39.455",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(2)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:39.522",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(2)-127.0.0.1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:41.306",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(4)-127.0.0.1",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
