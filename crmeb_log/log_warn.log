{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 19:47:59.859",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:41:44.943",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.151",
                    "level": "WARN",
                    "thread": "http-nio-20010-exec-2",
                    "class": "c.z.s.s.impl.LiveRoomProductServiceImpl",
                    "message": "商品已存在于直播间: roomId=112233, productId=4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:43:48.174",
                    "level": "WARN",
                    "thread": "http-nio-20010-exec-5",
                    "class": "c.z.s.s.impl.LiveRoomProductServiceImpl",
                    "message": "商品已存在于直播间: roomId=112233, productId=10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:51:50.081",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-21 23:55:18.701",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.admin]' package. Please check your configuration." }
                    
