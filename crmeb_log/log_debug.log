{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:11:55.309",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:11:55.330",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:11:55.393",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:11:55.393",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.296",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.297",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@302229c5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.339",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.631",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.632",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@81bcb06]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:08.141",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:08.146",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:08.482",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=2610ae956dcc7ac8e13e2307c013b8ce, code=9DC4)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.524",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.525",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@1e11ea76]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.526",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.654",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1753114332", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.654",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=fb8129d3515949a6ac75bba5a77999f7&temp=1753114332", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.701",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@127b96a1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.710",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.843",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1753114332", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.988",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.988",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@69bb58d9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.994",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.143",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@631f7c72]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.166",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.538",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/order?temp=1753114333", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartOrder()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.545",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#indexDate()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.598",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1753114333", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.599",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/statistics/home/<USER>/user?temp=1753114333", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.601",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.HomeController#chartUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.672",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1753114333", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.673",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.685",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@42a191a7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@2eceac6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.731",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@67a52b7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.736",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.747",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.747",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@40aa1ae9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.748",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:14.104",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:14.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.response.CommonResult@359fdce6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:14.107",
                    "level": "DEBUG",
                    "thread": "http-nio-20010-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
