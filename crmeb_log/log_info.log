{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.276",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-22",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：4555 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.723",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.z.admin.task.order.OrderPaySuccessTask",
                    "message": "---OrderPaySuccessTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:00 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.724",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.zbkj.admin.task.order.OrderRefundTask",
                    "message": "---OrderRefundTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:00 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.729",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.z.a.t.w.AsyncWeChatProgramTempMessage",
                    "message": "---AsyncWeChatProgramTempMessage task------produce Data with fixed rate task: Execution Time - 20250722" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.732",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.z.a.t.w.AsyncWeChatPublicTempMessage",
                    "message": "---AsyncWeChatPublicTempMessage task------produce Data with fixed rate task: Execution Time - 20250722" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.733",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.zbkj.admin.task.order.OrderCancelTask",
                    "message": "---OrderCancelTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:00 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.734",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.z.admin.task.order.OrderAutoCancelTask",
                    "message": "---OrderAutoCancelTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:00 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:00.736",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.zbkj.admin.task.order.OrderReceiptTask",
                    "message": "---OrderReceiptTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:00 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.029",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.z.admin.task.product.ProductStockTask",
                    "message": "---OrderTakeByUser task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:01 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.607",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.z.s.s.impl.StoreProductServiceImpl",
                    "message": "StoreProductServiceImpl.doProductStock | size:0" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.618",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.z.s.s.impl.StoreSeckillServiceImpl",
                    "message": "StoreProductServiceImpl.doProductStock | size:0" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.627",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.z.s.s.impl.StoreBargainServiceImpl",
                    "message": "StoreBargainServiceImpl.consumeProductStock | size:0" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.630",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-26",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.ValidateCodeController.get()，prams：[]，cost time：6012053940 ns，cost：6012 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:01.639",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.z.s.s.impl.StoreCombinationServiceImpl",
                    "message": "StoreProductServiceImpl.doProductStock | size:0" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:02.571",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.z.admin.task.coupon.CouponOverdueTask",
                    "message": "---CouponOverdueTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:02 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:02.597",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-15",
                    "class": "c.z.a.task.integral.IntegralFrozenTask",
                    "message": "---IntegralFrozenTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:02 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:02.598",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.zbkj.admin.task.order.OrderReceiptTask",
                    "message": "---BrokerageFrozenTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:02 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:02.743",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.z.admin.task.order.OrderCompleteTask",
                    "message": "---OrderCompleteTask task------produce Data with fixed rate task: Execution Time - Tue Jul 22 00:12:02 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:03.016",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.z.s.service.impl.OrderTaskServiceImpl",
                    "message": "OrderTaskServiceImpl.complete | size:7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.524",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-21",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=123456, key=2610ae956dcc7ac8e13e2307c013b8ce, code=9DC4), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@aca43fa]]，cost time：********** ns，cost：2784 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.689",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-17",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：******** ns，cost：15 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:12.988",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-16",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：140111892 ns，cost：140 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.142",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-19",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：371012864 ns，cost：371 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.723",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-10",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：37273221 ns，cost：37 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.723",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-9",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：27645927 ns，cost：27 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.730",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-13",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.HomeController.chartOrder()，prams：[]，cost time：186250372 ns，cost：186 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:13.746",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-6",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.HomeController.chartUser()，prams：[]，cost time：79997558 ns，cost：79 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-22 00:12:14.104",
                    "level": "INFO",
                    "thread": "http-nio-20010-exec-14",
                    "class": "com.zbkj.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.zbkj.admin.controller.HomeController.indexDate()，prams：[]，cost time：557354070 ns，cost：557 ms" }
                    
